import*as t from"../../core/common/common.js";import*as e from"../../core/i18n/i18n.js";import*as i from"../../ui/legacy/legacy.js";import*as o from"../../ui/visual_logging/visual_logging.js";import*as n from"../../core/host/host.js";import*as a from"../../core/sdk/sdk.js";const r=new CSSStyleSheet;r.replaceSync(":host{overflow:hidden}.header{padding:0 0 6px;border-bottom:1px solid var(--sys-color-divider);font-size:18px;font-weight:normal;flex:none}.add-locations-button{flex:none;margin:10px 5px;min-width:140px;align-self:flex-start}.locations-list{max-width:600px;min-width:340px;flex:auto}.locations-list-item{padding:3px 6px;height:30px;display:flex;align-items:center;position:relative;flex:auto 1 1}.locations-list-text{white-space:nowrap;text-overflow:ellipsis;flex-basis:170px;user-select:none;color:var(--sys-color-on-surface);position:relative;overflow:hidden}.locations-list-title{text-align:start}.locations-list-title-text{overflow:hidden;flex:auto;white-space:nowrap;text-overflow:ellipsis}.locations-list-separator{flex:0 0 1px;background-color:var(--sys-color-divider);height:30px;margin:0 4px}.locations-list-separator-invisible{visibility:hidden;height:100%!important}.locations-edit-row{display:flex;flex-direction:row;margin:6px 5px}.locations-edit-row input{width:100%;text-align:inherit}.locations-input-container{padding:1px}\n/*# sourceURL=locationsSettingsTab.css */\n");const l={customLocations:"Custom locations",locationName:"Location name",lat:"Lat",long:"Long",timezoneId:"Timezone ID",locale:"Locale",latitude:"Latitude",longitude:"Longitude",locationNameCannotBeEmpty:"Location name cannot be empty",locationNameMustBeLessThanS:"Location name must be less than {PH1} characters",latitudeMustBeANumber:"Latitude must be a number",latitudeMustBeGreaterThanOrEqual:"Latitude must be greater than or equal to {PH1}",latitudeMustBeLessThanOrEqualToS:"Latitude must be less than or equal to {PH1}",longitudeMustBeANumber:"Longitude must be a number",longitudeMustBeGreaterThanOr:"Longitude must be greater than or equal to {PH1}",longitudeMustBeLessThanOrEqualTo:"Longitude must be less than or equal to {PH1}",timezoneIdMustContainAlphabetic:"Timezone ID must contain alphabetic characters",localeMustContainAlphabetic:"Locale must contain alphabetic characters",addLocation:"Add location..."},s=e.i18n.registerUIStrings("panels/sensors/LocationsSettingsTab.ts",l),c=e.i18n.getLocalizedString.bind(void 0,s);class d extends i.Widget.VBox{list;customSetting;editor;constructor(){super(!0),this.element.setAttribute("jslog",`${o.pane("emulation-locations")}`),this.contentElement.createChild("div","header").textContent=c(l.customLocations);const e=i.UIUtils.createTextButton(c(l.addLocation),this.addButtonClicked.bind(this),{className:"add-locations-button",jslogContext:"emulation.add-location"});this.contentElement.appendChild(e),this.list=new i.ListWidget.ListWidget(this,void 0,!0),this.list.element.classList.add("locations-list"),this.list.show(this.contentElement),this.customSetting=t.Settings.Settings.instance().moduleSetting("emulation.locations");const n=this.customSetting.get().map((t=>function(t,e){if(!t.title){const i=e.find((e=>e.lat===t.lat&&e.long===t.long&&e.timezoneId===t.timezoneId&&e.locale===t.locale));if(i)return i;console.error("Could not determine a location setting title")}return t}(t,this.customSetting.defaultValue)));this.customSetting.set(n),this.customSetting.addChangeListener(this.locationsUpdated,this),this.setDefaultFocusedElement(e)}wasShown(){super.wasShown(),this.registerCSSFiles([r]),this.list.registerCSSFiles([r]),this.locationsUpdated()}locationsUpdated(){this.list.clear();const t=this.customSetting.get();for(const e of t)this.list.appendItem(e,!0);this.list.appendSeparator()}addButtonClicked(){this.list.addNewItem(this.customSetting.get().length,{title:"",lat:0,long:0,timezoneId:"",locale:""})}renderItem(t,e){const o=document.createElement("div");o.role="row",o.classList.add("locations-list-item");const n=o.createChild("div","locations-list-text locations-list-title");n.role="cell";const a=n.createChild("div","locations-list-title-text");a.textContent=t.title,i.Tooltip.Tooltip.install(a,t.title),o.createChild("div","locations-list-separator");const r=o.createChild("div","locations-list-text");r.textContent=String(t.lat),r.role="cell",o.createChild("div","locations-list-separator");const l=o.createChild("div","locations-list-text");l.textContent=String(t.long),l.role="cell",o.createChild("div","locations-list-separator");const s=o.createChild("div","locations-list-text");s.textContent=t.timezoneId,s.role="cell",o.createChild("div","locations-list-separator");const c=o.createChild("div","locations-list-text");return c.textContent=t.locale,c.role="cell",o}removeItemRequested(t,e){const i=this.customSetting.get();i.splice(e,1),this.customSetting.set(i)}commitEdit(t,e,i){t.title=e.control("title").value.trim();const o=e.control("lat").value.trim();t.lat=o?parseFloat(o):0;const n=e.control("long").value.trim();t.long=n?parseFloat(n):0;const a=e.control("timezone-id").value.trim();t.timezoneId=a;const r=e.control("locale").value.trim();t.locale=r;const l=this.customSetting.get();i&&l.push(t),this.customSetting.set(l)}beginEdit(t){const e=this.createEditor();return e.control("title").value=t.title,e.control("lat").value=String(t.lat),e.control("long").value=String(t.long),e.control("timezone-id").value=t.timezoneId,e.control("locale").value=t.locale,e}createEditor(){if(this.editor)return this.editor;const t=new i.ListWidget.Editor;this.editor=t;const e=t.contentElement(),o=e.createChild("div","locations-edit-row");o.createChild("div","locations-list-text locations-list-title").textContent=c(l.locationName),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.lat),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.long),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.timezoneId),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.locale);const n=e.createChild("div","locations-edit-row");n.createChild("div","locations-list-text locations-list-title locations-input-container").appendChild(t.createInput("title","text",c(l.locationName),(function(t,e,i){const o=i.value.trim();let n;o.length?o.length>50&&(n=c(l.locationNameMustBeLessThanS,{PH1:50})):n=c(l.locationNameCannotBeEmpty);if(n)return{valid:!1,errorMessage:n};return{valid:!0,errorMessage:void 0}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible");let a=n.createChild("div","locations-list-text locations-input-container");return a.appendChild(t.createInput("lat","text",c(l.latitude),(function(t,e,i){const o=i.value.trim(),n=Number(o);if(!o)return{valid:!0,errorMessage:void 0};let a;Number.isNaN(n)?a=c(l.latitudeMustBeANumber):parseFloat(o)<-90?a=c(l.latitudeMustBeGreaterThanOrEqual,{PH1:-90}):parseFloat(o)>90&&(a=c(l.latitudeMustBeLessThanOrEqualToS,{PH1:90}));if(a)return{valid:!1,errorMessage:a};return{valid:!0,errorMessage:void 0}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible"),a=n.createChild("div","locations-list-text locations-list-text-longitude locations-input-container"),a.appendChild(t.createInput("long","text",c(l.longitude),(function(t,e,i){const o=i.value.trim(),n=Number(o);if(!o)return{valid:!0,errorMessage:void 0};let a;Number.isNaN(n)?a=c(l.longitudeMustBeANumber):parseFloat(o)<-180?a=c(l.longitudeMustBeGreaterThanOr,{PH1:-180}):parseFloat(o)>180&&(a=c(l.longitudeMustBeLessThanOrEqualTo,{PH1:180}));if(a)return{valid:!1,errorMessage:a};return{valid:!0,errorMessage:void 0}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible"),a=n.createChild("div","locations-list-text locations-input-container"),a.appendChild(t.createInput("timezone-id","text",c(l.timezoneId),(function(t,e,i){const o=i.value.trim();if(""===o||/[a-zA-Z]/.test(o))return{valid:!0,errorMessage:void 0};return{valid:!1,errorMessage:c(l.timezoneIdMustContainAlphabetic)}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible"),a=n.createChild("div","locations-list-text locations-input-container"),a.appendChild(t.createInput("locale","text",c(l.locale),(function(t,e,i){const o=i.value.trim();if(""===o||/[a-zA-Z]{2}/.test(o))return{valid:!0,errorMessage:void 0};return{valid:!1,errorMessage:c(l.localeMustContainAlphabetic)}}))),t}}var h=Object.freeze({__proto__:null,LocationsSettingsTab:d});const p=new CSSStyleSheet;p.replaceSync('.sensors-view{padding:12px;display:block}.sensors-view input{width:100%;max-width:120px;margin:-5px 10px 0 0;text-align:end}.sensors-view input[readonly]{background-color:var(--sys-color-neutral-container)}.sensors-view fieldset{border:none;padding:10px 0;flex:0 0 auto;margin:0}.sensors-view fieldset[disabled]{opacity:50%}.orientation-axis-input-container input{max-width:120px}.sensors-view input:focus::-webkit-input-placeholder{color:transparent!important}.sensors-view .chrome-select{width:200px}.sensors-group-title{width:80px;line-height:24px}.sensors-group{display:flex;flex-wrap:wrap;margin-bottom:10px}.manage-locations{margin-left:var(--sys-size-4)}.geo-fields{flex:2 0 200px}.latlong-group{display:flex;margin-bottom:10px}.latlong-title{width:70px}.timezone-error,\n.locale-error{margin-left:10px;color:var(--legacy-input-validation-error)}.orientation-content{display:flex;flex-wrap:wrap}.orientation-fields{margin-right:10px}.orientation-stage{--override-gradient-color-1:var(--ref-palette-cyan95);--override-gradient-color-2:var(--ref-palette-cyan90);perspective:700px;perspective-origin:50% 50%;width:160px;height:150px;background:linear-gradient(var(--override-gradient-color-1) 0%,var(--override-gradient-color-1) 64%,var(--override-gradient-color-2) 64%,var(--override-gradient-color-1) 100%);transition:0.2s ease opacity,0.2s ease filter;overflow:hidden;margin-bottom:10px}.theme-with-dark-background .orientation-stage,\n:host-context(.theme-with-dark-background) .orientation-stage{--override-gradient-color-1:var(--ref-palette-cyan10);--override-gradient-color-2:var(--ref-palette-cyan30)}.orientation-stage.disabled{filter:grayscale();opacity:50%}.orientation-element,\n.orientation-element::before,\n.orientation-element::after{position:absolute;box-sizing:border-box;transform-style:preserve-3d;background:no-repeat;background-size:cover;backface-visibility:hidden}.orientation-box{width:62px;height:122px;left:0;right:0;top:0;bottom:0;margin:auto;transform:rotate3d(1,0,0,90deg)}.orientation-layer{width:100%;height:100%;transform-style:preserve-3d}.orientation-box.is-animating,\n.is-animating .orientation-layer{transition:transform 300ms cubic-bezier(0.4,0,0.2,1) 0ms}.orientation-front,\n.orientation-back{width:62px;height:122px;border-radius:8px}.orientation-front{background-image:var(--image-file-accelerometer-front)}.orientation-back{transform:rotateY(180deg) translateZ(8px);background-image:var(--image-file-accelerometer-back)}.orientation-left,\n.orientation-right{width:8px;height:106px;top:8px;background-position:center center}.orientation-left{left:-8px;transform-origin:right center;transform:rotateY(-90deg);background-image:var(--image-file-accelerometer-left)}.orientation-right{right:-8px;transform-origin:left center;transform:rotateY(90deg);background-image:var(--image-file-accelerometer-right)}.orientation-left::before,\n.orientation-left::after,\n.orientation-right::before,\n.orientation-right::after{content:"";width:8px;height:6px}.orientation-left::before,\n.orientation-left::after{background-image:var(--image-file-accelerometer-left)}.orientation-right::before,\n.orientation-right::after{background-image:var(--image-file-accelerometer-right)}.orientation-left::before,\n.orientation-right::before{top:-6px;transform-origin:center bottom;transform:rotateX(26deg);background-position:center top}.orientation-left::after,\n.orientation-right::after{bottom:-6px;transform-origin:center top;transform:rotateX(-25deg);background-position:center bottom}.orientation-top,\n.orientation-bottom{width:50px;height:8px;left:8px;background-position:center center}.orientation-top{top:-8px;transform-origin:center bottom;transform:rotateX(90deg);background-image:var(--image-file-accelerometer-top)}.orientation-bottom{bottom:-8px;transform-origin:center top;transform:rotateX(-90deg);background-image:var(--image-file-accelerometer-bottom)}.orientation-top::before,\n.orientation-top::after,\n.orientation-bottom::before,\n.orientation-bottom::after{content:"";width:8px;height:8px}.orientation-top::before,\n.orientation-top::after{background-image:var(--image-file-accelerometer-top)}.orientation-bottom::before,\n.orientation-bottom::after{background-image:var(--image-file-accelerometer-bottom)}.orientation-top::before,\n.orientation-bottom::before{left:-6px;transform-origin:right center;transform:rotateY(-26deg);background-position:left center}.orientation-top::after,\n.orientation-bottom::after{right:-6px;transform-origin:left center;transform:rotateY(26deg);background-position:right center}.orientation-axis-input-container{margin-bottom:10px}.orientation-reset-button{min-width:80px}fieldset.device-orientation-override-section{margin:0;display:flex}.panel-section-separator{height:1px;margin-bottom:20px;margin-left:-12px;margin-right:-12px;background:var(--sys-color-divider)}button.text-button{margin:4px 0 0 10px}@media (forced-colors: active){.sensors-view fieldset[disabled]{opacity:100%}}.chrome-select-label{margin-bottom:16px}\n/*# sourceURL=sensors.css */\n');const m={location:"Location",noOverride:"No override",overrides:"Overrides",manage:"Manage",manageTheListOfLocations:"Manage the list of locations",other:"Other…",error:"Error",locationUnavailable:"Location unavailable",adjustWithMousewheelOrUpdownKeys:"Adjust with mousewheel or up/down keys. {PH1}: ±10, Shift: ±1, Alt: ±0.01",latitude:"Latitude",longitude:"Longitude",timezoneId:"Timezone ID",locale:"Locale",orientation:"Orientation",off:"Off",customOrientation:"Custom orientation",enableOrientationToRotate:"Enable orientation to rotate",shiftdragHorizontallyToRotate:"Shift+drag horizontally to rotate around the y-axis",deviceOrientationSetToAlphaSBeta:"Device orientation set to alpha: {PH1}, beta: {PH2}, gamma: {PH3}",reset:"Reset",resetDeviceOrientation:"Reset device orientation",forcesTouchInsteadOfClick:"Forces touch instead of click",forcesSelectedIdleStateEmulation:"Forces selected idle state emulation",presets:"Presets",portrait:"Portrait",portraitUpsideDown:"Portrait upside down",landscapeLeft:"Landscape left",landscapeRight:"Landscape right",displayUp:"Display up",displayDown:"Display down",alpha:"α (alpha)",beta:"β (beta)",gamma:"γ (gamma)"},u=e.i18n.registerUIStrings("panels/sensors/SensorsView.ts",m),g=e.i18n.getLocalizedString.bind(void 0,u);class v extends i.Widget.VBox{#t;#e;#i;fieldsetElement;timezoneError;locationSelectElement;latitudeInput;longitudeInput;timezoneInput;localeInput;latitudeSetter;longitudeSetter;timezoneSetter;localeSetter;localeError;customLocationsGroup;deviceOrientationSetting;deviceOrientation;deviceOrientationOverrideEnabled;deviceOrientationFieldset;stageElement;orientationSelectElement;alphaElement;betaElement;gammaElement;alphaSetter;betaSetter;gammaSetter;orientationLayer;boxElement;boxMatrix;mouseDownVector;originalBoxMatrix;constructor(){super(!0),this.element.setAttribute("jslog",`${o.panel("sensors").track({resize:!0})}`),this.contentElement.classList.add("sensors-view"),this.#t=t.Settings.Settings.instance().createSetting("emulation.location-override",""),this.#e=a.EmulationModel.Location.parseSetting(this.#t.get()),this.#i=!1,this.createLocationSection(this.#e),this.createPanelSeparator(),this.deviceOrientationSetting=t.Settings.Settings.instance().createSetting("emulation.device-orientation-override",""),this.deviceOrientation=a.EmulationModel.DeviceOrientation.parseSetting(this.deviceOrientationSetting.get()),this.deviceOrientationOverrideEnabled=!1,this.createDeviceOrientationSection(),this.createPanelSeparator(),this.appendTouchControl(),this.createPanelSeparator(),this.appendIdleEmulator(),this.createPanelSeparator()}wasShown(){super.wasShown(),this.registerCSSFiles([p])}createPanelSeparator(){this.contentElement.createChild("div").classList.add("panel-section-separator")}createLocationSection(e){const r=this.contentElement.createChild("section","sensors-group");r.setAttribute("jslog",`${o.section("location")}`);const l=i.UIUtils.createLabel(g(m.location),"sensors-group-title");r.appendChild(l);const s=r.createChild("div","geo-fields");let c=0;const d={title:g(m.noOverride),location:b.NoOverride};this.locationSelectElement=s.createChild("select","chrome-select"),this.locationSelectElement.setAttribute("jslog",`${o.dropDown().track({change:!0})}`),i.ARIAUtils.bindLabelToControl(l,this.locationSelectElement),this.locationSelectElement.appendChild(i.UIUtils.createOption(d.title,d.location,"no-override")),this.customLocationsGroup=this.locationSelectElement.createChild("optgroup"),this.customLocationsGroup.label=g(m.overrides);const h=t.Settings.Settings.instance().moduleSetting("emulation.locations"),p=i.UIUtils.createTextButton(g(m.manage),(()=>t.Revealer.reveal(h)),{className:"manage-locations",jslogContext:"sensors.manage-locations"});i.ARIAUtils.setLabel(p,g(m.manageTheListOfLocations)),s.appendChild(p);const u=()=>{if(this.customLocationsGroup){this.customLocationsGroup.removeChildren();for(const[t,o]of h.get().entries())this.customLocationsGroup.appendChild(i.UIUtils.createOption(o.title,JSON.stringify(o),"custom")),e.latitude===o.lat&&e.longitude===o.long&&(c=t+1)}};h.addChangeListener(u),u();const v={title:g(m.other),location:b.Custom};this.locationSelectElement.appendChild(i.UIUtils.createOption(v.title,v.location,"other"));const f=this.locationSelectElement.createChild("optgroup");f.label=g(m.error),f.appendChild(i.UIUtils.createOption(g(m.locationUnavailable),b.Unavailable,"unavailable")),this.locationSelectElement.selectedIndex=c,this.locationSelectElement.addEventListener("change",this.#o.bind(this)),this.fieldsetElement=s.createChild("fieldset"),this.fieldsetElement.disabled=!this.#i,this.fieldsetElement.id="location-override-section";const x=this.fieldsetElement.createChild("div","latlong-group"),S=this.fieldsetElement.createChild("div","latlong-group"),E=this.fieldsetElement.createChild("div","latlong-group"),C=this.fieldsetElement.createChild("div","latlong-group"),I=n.Platform.isMac()?"⌘":"Ctrl",O=g(m.adjustWithMousewheelOrUpdownKeys,{PH1:I});this.latitudeInput=i.UIUtils.createInput("","number","latitude"),x.appendChild(this.latitudeInput),this.latitudeInput.setAttribute("step","any"),this.latitudeInput.value="0",this.latitudeSetter=i.UIUtils.bindInput(this.latitudeInput,this.applyLocationUserInput.bind(this),a.EmulationModel.Location.latitudeValidator,!0,.1),this.latitudeSetter(String(e.latitude)),i.Tooltip.Tooltip.install(this.latitudeInput,O),x.appendChild(i.UIUtils.createLabel(g(m.latitude),"latlong-title",this.latitudeInput)),this.longitudeInput=i.UIUtils.createInput("","number","longitude"),S.appendChild(this.longitudeInput),this.longitudeInput.setAttribute("step","any"),this.longitudeInput.value="0",this.longitudeSetter=i.UIUtils.bindInput(this.longitudeInput,this.applyLocationUserInput.bind(this),a.EmulationModel.Location.longitudeValidator,!0,.1),this.longitudeSetter(String(e.longitude)),i.Tooltip.Tooltip.install(this.longitudeInput,O),S.appendChild(i.UIUtils.createLabel(g(m.longitude),"latlong-title",this.longitudeInput)),this.timezoneInput=i.UIUtils.createInput("","text","timezone"),E.appendChild(this.timezoneInput),this.timezoneInput.value="Europe/Berlin",this.timezoneSetter=i.UIUtils.bindInput(this.timezoneInput,this.applyLocationUserInput.bind(this),a.EmulationModel.Location.timezoneIdValidator,!1),this.timezoneSetter(e.timezoneId),E.appendChild(i.UIUtils.createLabel(g(m.timezoneId),"timezone-title",this.timezoneInput)),this.timezoneError=E.createChild("div","timezone-error"),this.localeInput=i.UIUtils.createInput("","text","locale"),C.appendChild(this.localeInput),this.localeInput.value="en-US",this.localeSetter=i.UIUtils.bindInput(this.localeInput,this.applyLocationUserInput.bind(this),a.EmulationModel.Location.localeValidator,!1),this.localeSetter(e.locale),C.appendChild(i.UIUtils.createLabel(g(m.locale),"locale-title",this.localeInput)),this.localeError=C.createChild("div","locale-error")}#o(){this.fieldsetElement.disabled=!1,this.timezoneError.textContent="";const t=this.locationSelectElement.options[this.locationSelectElement.selectedIndex].value;if(t===b.NoOverride)this.#i=!1,this.clearFieldsetElementInputs(),this.fieldsetElement.disabled=!0;else if(t===b.Custom){this.#i=!0;const t=a.EmulationModel.Location.parseUserInput(this.latitudeInput.value.trim(),this.longitudeInput.value.trim(),this.timezoneInput.value.trim(),this.localeInput.value.trim());if(!t)return;this.#e=t}else if(t===b.Unavailable)this.#i=!0,this.#e=new a.EmulationModel.Location(0,0,"","",!0);else{this.#i=!0;const e=JSON.parse(t);this.#e=new a.EmulationModel.Location(e.lat,e.long,e.timezoneId,e.locale,!1),this.latitudeSetter(e.lat),this.longitudeSetter(e.long),this.timezoneSetter(e.timezoneId),this.localeSetter(e.locale)}this.applyLocation(),t===b.Custom&&this.latitudeInput.focus()}applyLocationUserInput(){const t=a.EmulationModel.Location.parseUserInput(this.latitudeInput.value.trim(),this.longitudeInput.value.trim(),this.timezoneInput.value.trim(),this.localeInput.value.trim());t&&(this.timezoneError.textContent="",this.setSelectElementLabel(this.locationSelectElement,b.Custom),this.#e=t,this.applyLocation())}applyLocation(){this.#i?this.#t.set(this.#e.toSetting()):this.#t.set("");for(const t of a.TargetManager.TargetManager.instance().models(a.EmulationModel.EmulationModel))t.emulateLocation(this.#i?this.#e:null).catch((t=>{switch(t.type){case"emulation-set-timezone":this.timezoneError.textContent=t.message;break;case"emulation-set-locale":this.localeError.textContent=t.message}}))}clearFieldsetElementInputs(){this.latitudeSetter("0"),this.longitudeSetter("0"),this.timezoneSetter(""),this.localeSetter("")}createDeviceOrientationSection(){const t=this.contentElement.createChild("section","sensors-group");t.setAttribute("jslog",`${o.section("device-orientation")}`);const e=i.UIUtils.createLabel(g(m.orientation),"sensors-group-title");t.appendChild(e);const n=t.createChild("div","orientation-content"),a=n.createChild("div","orientation-fields"),r={title:g(m.off),orientation:b.NoOverride,jslogContext:"off"},l={title:g(m.customOrientation),orientation:b.Custom},s=[{title:g(m.presets),value:[{title:g(m.portrait),orientation:"[0, 90, 0]",jslogContext:"portrait"},{title:g(m.portraitUpsideDown),orientation:"[180, -90, 0]",jslogContext:"portrait-upside-down"},{title:g(m.landscapeLeft),orientation:"[90, 0, -90]",jslogContext:"landscape-left"},{title:g(m.landscapeRight),orientation:"[90, -180, -90]",jslogContext:"landscape-right"},{title:g(m.displayUp),orientation:"[0, 0, 0]",jslogContext:"display-up"},{title:g(m.displayDown),orientation:"[0, -180, 0]",jslogContext:"displayUp-down"}]}];this.orientationSelectElement=this.contentElement.createChild("select","chrome-select"),this.orientationSelectElement.setAttribute("jslog",`${o.dropDown().track({change:!0})}`),i.ARIAUtils.bindLabelToControl(e,this.orientationSelectElement),this.orientationSelectElement.appendChild(i.UIUtils.createOption(r.title,r.orientation,r.jslogContext)),this.orientationSelectElement.appendChild(i.UIUtils.createOption(l.title,l.orientation,"custom"));for(let t=0;t<s.length;++t){const e=this.orientationSelectElement.createChild("optgroup");e.label=s[t].title;const o=s[t].value;for(let t=0;t<o.length;++t)e.appendChild(i.UIUtils.createOption(o[t].title,o[t].orientation,o[t].jslogContext))}this.orientationSelectElement.selectedIndex=0,a.appendChild(this.orientationSelectElement),this.orientationSelectElement.addEventListener("change",this.orientationSelectChanged.bind(this)),this.deviceOrientationFieldset=this.createDeviceOrientationOverrideElement(this.deviceOrientation),this.stageElement=n.createChild("div","orientation-stage"),this.stageElement.setAttribute("jslog",`${o.preview().track({drag:!0})}`),this.orientationLayer=this.stageElement.createChild("div","orientation-layer"),this.boxElement=this.orientationLayer.createChild("section","orientation-box orientation-element"),this.boxElement.createChild("section","orientation-front orientation-element"),this.boxElement.createChild("section","orientation-top orientation-element"),this.boxElement.createChild("section","orientation-back orientation-element"),this.boxElement.createChild("section","orientation-left orientation-element"),this.boxElement.createChild("section","orientation-right orientation-element"),this.boxElement.createChild("section","orientation-bottom orientation-element"),i.UIUtils.installDragHandle(this.stageElement,this.onBoxDragStart.bind(this),(t=>{this.onBoxDrag(t)}),null,"-webkit-grabbing","-webkit-grab"),a.appendChild(this.deviceOrientationFieldset),this.enableOrientationFields(!0),this.setBoxOrientation(this.deviceOrientation,!1)}enableOrientationFields(t){t?(this.deviceOrientationFieldset.disabled=!0,this.stageElement.classList.add("disabled"),i.Tooltip.Tooltip.install(this.stageElement,g(m.enableOrientationToRotate))):(this.deviceOrientationFieldset.disabled=!1,this.stageElement.classList.remove("disabled"),i.Tooltip.Tooltip.install(this.stageElement,g(m.shiftdragHorizontallyToRotate)))}orientationSelectChanged(){const t=this.orientationSelectElement.options[this.orientationSelectElement.selectedIndex].value;if(this.enableOrientationFields(!1),t===b.NoOverride)this.deviceOrientationOverrideEnabled=!1,this.enableOrientationFields(!0),this.applyDeviceOrientation();else if(t===b.Custom)this.deviceOrientationOverrideEnabled=!0,this.resetDeviceOrientation(),this.alphaElement.focus();else{const e=JSON.parse(t);this.deviceOrientationOverrideEnabled=!0,this.deviceOrientation=new a.EmulationModel.DeviceOrientation(e[0],e[1],e[2]),this.setDeviceOrientation(this.deviceOrientation,"selectPreset")}}applyDeviceOrientation(){this.deviceOrientationOverrideEnabled&&this.deviceOrientationSetting.set(this.deviceOrientation.toSetting());for(const t of a.TargetManager.TargetManager.instance().models(a.EmulationModel.EmulationModel))t.emulateDeviceOrientation(this.deviceOrientationOverrideEnabled?this.deviceOrientation:null)}setSelectElementLabel(t,e){const i=Array.prototype.map.call(t.options,(t=>t.value));t.selectedIndex=i.indexOf(e)}applyDeviceOrientationUserInput(){this.setDeviceOrientation(a.EmulationModel.DeviceOrientation.parseUserInput(this.alphaElement.value.trim(),this.betaElement.value.trim(),this.gammaElement.value.trim()),"userInput"),this.setSelectElementLabel(this.orientationSelectElement,b.Custom)}resetDeviceOrientation(){this.setDeviceOrientation(new a.EmulationModel.DeviceOrientation(0,90,0),"resetButton"),this.setSelectElementLabel(this.orientationSelectElement,"[0, 90, 0]")}setDeviceOrientation(t,e){if(!t)return;function o(t){return Math.round(1e4*t)/1e4}"userInput"!==e&&(this.alphaSetter(String(o(t.alpha))),this.betaSetter(String(o(t.beta))),this.gammaSetter(String(o(t.gamma))));const n="userDrag"!==e;this.setBoxOrientation(t,n),this.deviceOrientation=t,this.applyDeviceOrientation(),i.ARIAUtils.alert(g(m.deviceOrientationSetToAlphaSBeta,{PH1:t.alpha,PH2:t.beta,PH3:t.gamma}))}createAxisInput(t,e,o,n){const a=t.createChild("div","orientation-axis-input-container");return a.appendChild(e),a.appendChild(i.UIUtils.createLabel(o,"",e)),i.UIUtils.bindInput(e,this.applyDeviceOrientationUserInput.bind(this),n,!0)}createDeviceOrientationOverrideElement(t){const e=document.createElement("fieldset");e.classList.add("device-orientation-override-section");const o=e.createChild("td","orientation-inputs-cell");this.alphaElement=i.UIUtils.createInput("","number","alpha"),this.alphaElement.setAttribute("step","any"),this.alphaSetter=this.createAxisInput(o,this.alphaElement,g(m.alpha),a.EmulationModel.DeviceOrientation.alphaAngleValidator),this.alphaSetter(String(t.alpha)),this.betaElement=i.UIUtils.createInput("","number","beta"),this.betaElement.setAttribute("step","any"),this.betaSetter=this.createAxisInput(o,this.betaElement,g(m.beta),a.EmulationModel.DeviceOrientation.betaAngleValidator),this.betaSetter(String(t.beta)),this.gammaElement=i.UIUtils.createInput("","number","gamma"),this.gammaElement.setAttribute("step","any"),this.gammaSetter=this.createAxisInput(o,this.gammaElement,g(m.gamma),a.EmulationModel.DeviceOrientation.gammaAngleValidator),this.gammaSetter(String(t.gamma));const n=i.UIUtils.createTextButton(g(m.reset),this.resetDeviceOrientation.bind(this),{className:"orientation-reset-button",jslogContext:"sensors.reset-device-orientiation"});return i.ARIAUtils.setLabel(n,g(m.resetDeviceOrientation)),n.setAttribute("type","reset"),o.appendChild(n),e}setBoxOrientation(t,e){e?this.stageElement.classList.add("is-animating"):this.stageElement.classList.remove("is-animating");const{alpha:i,beta:o,gamma:n}=t;this.boxMatrix=(new DOMMatrixReadOnly).rotate(0,0,i).rotate(o,0,0).rotate(0,n,0),this.orientationLayer.style.transform=`rotateY(${i}deg) rotateX(${-o}deg) rotateZ(${n}deg)`}onBoxDrag(t){const e=this.calculateRadiusVector(t.x,t.y);if(!e)return!0;if(!this.mouseDownVector)return!0;let o,n;t.consume(!0),t.shiftKey?(o=new i.Geometry.Vector(0,0,1),n=(e.x-this.mouseDownVector.x)*f):(o=i.Geometry.crossProduct(this.mouseDownVector,e),n=i.Geometry.calculateAngle(this.mouseDownVector,e));const r=(new DOMMatrixReadOnly).rotateAxisAngle(-o.x,o.z,o.y,n).multiply(this.originalBoxMatrix),l=i.Geometry.EulerAngles.fromDeviceOrientationRotationMatrix(r),s=new a.EmulationModel.DeviceOrientation(l.alpha,l.beta,l.gamma);return this.setDeviceOrientation(s,"userDrag"),this.setSelectElementLabel(this.orientationSelectElement,b.Custom),!1}onBoxDragStart(t){return!!this.deviceOrientationOverrideEnabled&&(this.mouseDownVector=this.calculateRadiusVector(t.x,t.y),this.originalBoxMatrix=this.boxMatrix,!!this.mouseDownVector&&(t.consume(!0),!0))}calculateRadiusVector(t,e){const o=this.stageElement.getBoundingClientRect(),n=Math.max(o.width,o.height)/2,a=(t-o.left-o.width/2)/n,r=(e-o.top-o.height/2)/n,l=a*a+r*r;return l>.5?new i.Geometry.Vector(a,r,.5/Math.sqrt(l)):new i.Geometry.Vector(a,r,Math.sqrt(1-l))}appendTouchControl(){const e=this.contentElement.createChild("div","touch-section"),o=i.SettingsUI.createControlForSetting(t.Settings.Settings.instance().moduleSetting("emulation.touch"),g(m.forcesTouchInsteadOfClick));o&&e.appendChild(o)}appendIdleEmulator(){const e=this.contentElement.createChild("div","idle-section"),o=i.SettingsUI.createControlForSetting(t.Settings.Settings.instance().moduleSetting("emulation.idle-detection"),g(m.forcesSelectedIdleStateEmulation));o&&e.appendChild(o)}}const b={NoOverride:"noOverride",Custom:"custom",Unavailable:"unavailable"};const f=16;var x=Object.freeze({__proto__:null,SensorsView:v,NonPresetOptions:b,ShowActionDelegate:class{handleAction(t,e){return i.ViewManager.ViewManager.instance().showView("sensors"),!0}},ShiftDragOrientationSpeed:f});export{h as LocationsSettingsTab,x as SensorsView};
