{"name": "dukancard-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "NODE_ENV=development expo run:android", "android:release": "NODE_ENV=production expo run:android --variant release", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@bottom-tabs/react-navigation": "^0.9.2", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^4.6.4", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.49.8", "ajv": "^8.17.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "53.0.12", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.1", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-media-library": "~17.1.7", "expo-router": "~5.1.0", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.514.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "react-native": "0.79.4", "react-native-bottom-tabs": "^0.9.2", "react-native-config": "^1.5.5", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-keychain": "^10.0.0", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-qr-code": "^2.0.16", "ts-interface-checker": "^1.0.2", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "~29.7.0", "jest-environment-node": "^30.0.0", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}