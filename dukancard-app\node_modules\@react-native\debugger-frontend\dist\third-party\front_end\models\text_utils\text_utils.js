import*as t from"../../third_party/codemirror.next/codemirror.next.js";import*as e from"../../core/platform/platform.js";import*as n from"../../core/common/common.js";var s=Object.freeze({__proto__:null,createCssTokenizer:function(){return async function(e,n){const s=await t.cssStreamParser(),i=new t.StringStream(e,4,2),r=s.startState(2);let o=i.pos;for(;!i.eol();){i.start=o;let t=s.token(i,r);"error"===t&&"maybeprop"===r.state&&(t="property");n(i.current(),t),o=i.pos}}}});class i{lineNumber;lineContent;columnNumber;matchLength;constructor(t,e,n,s){this.lineNumber=t,this.lineContent=e,this.columnNumber=n,this.matchLength=s}static comparator(t,e){return t.lineNumber-e.lineNumber||t.columnNumber-e.columnNumber}}const r=function(t,e,n,s,i=!0){return null==t||i&&t.length>1048576?null:"data:"+e+(s?";charset="+s:"")+(n?";base64":"")+","+(t=n?t:encodeURIComponent(t))};var o=Object.freeze({__proto__:null,SearchMatch:i,contentAsDataURL:r,isStreamingContentProvider:function(t){return"requestStreamingContent"in t}});class a{mimeType;charset;#t;#e;constructor(t,e,n,s){this.charset=s||"utf-8",e?this.#t=t:this.#e=t,this.mimeType=n,this.mimeType||(this.mimeType=e?"application/octet-stream":"text/plain")}get base64(){if(void 0===this.#t)throw new Error("Encoding text content as base64 is not supported");return this.#t}get text(){if(void 0!==this.#e)return this.#e;if(!this.isTextContent)throw new Error("Cannot interpret binary data as text");const t=window.atob(this.#t),e=Uint8Array.from(t,(t=>t.codePointAt(0)));return this.#e=new TextDecoder(this.charset).decode(e),this.#e}get isTextContent(){return e.MimeType.isTextType(this.mimeType)}get isEmpty(){return!Boolean(this.#t)&&!Boolean(this.#e)}get createdFromBase64(){return void 0!==this.#t}contentEqualTo(t){return void 0!==this.#t&&void 0!==t.#t?this.#t===t.#t:void 0!==this.#e&&void 0!==t.#e?this.#e===t.#e:!(!this.isTextContent||!t.isTextContent)&&this.text===t.text}asDataUrl(){if(void 0!==this.#t){const t=this.isTextContent?this.charset:null;return r(this.#t,this.mimeType??"",!0,t)}return r(this.text,this.mimeType??"",!1,"utf-8")}asDeferedContent(){if(this.isTextContent)return{content:this.text,isEncoded:!1};if(void 0!==this.#e)return{content:this.#e,isEncoded:!1};if(void 0!==this.#t)return{content:this.#t,isEncoded:!0};throw new Error("Unreachable")}static isError(t){return"error"in t}static textOr(t,e){return a.isError(t)?e:t.text}static asDeferredContent(t){return a.isError(t)?{error:t.error,content:null,isEncoded:!1}:t.asDeferedContent()}}var l=Object.freeze({__proto__:null,ContentData:a});class h{lineEndings;offsetInternal;lineNumberInternal;columnNumberInternal;constructor(t){this.lineEndings=t,this.offsetInternal=0,this.lineNumberInternal=0,this.columnNumberInternal=0}advance(t){for(this.offsetInternal=t;this.lineNumberInternal<this.lineEndings.length&&this.lineEndings[this.lineNumberInternal]<this.offsetInternal;)++this.lineNumberInternal;this.columnNumberInternal=this.lineNumberInternal?this.offsetInternal-this.lineEndings[this.lineNumberInternal-1]-1:this.offsetInternal}offset(){return this.offsetInternal}resetTo(t){this.offsetInternal=t,this.lineNumberInternal=e.ArrayUtilities.lowerBound(this.lineEndings,t,e.ArrayUtilities.DEFAULT_COMPARATOR),this.columnNumberInternal=this.lineNumberInternal?this.offsetInternal-this.lineEndings[this.lineNumberInternal-1]-1:this.offsetInternal}lineNumber(){return this.lineNumberInternal}columnNumber(){return this.columnNumberInternal}}var u=Object.freeze({__proto__:null,TextCursor:h});const c=2**31-1;class d{startLine;startColumn;endLine;endColumn;constructor(t,e,n,s){this.startLine=t,this.startColumn=e,this.endLine=n,this.endColumn=s}static createFromLocation(t,e){return new d(t,e,t,e)}static createUnboundedFromLocation(t,e){return new d(t,e,c,c)}static fromObject(t){return new d(t.startLine,t.startColumn,t.endLine,t.endColumn)}static comparator(t,e){return t.compareTo(e)}static fromEdit(t,n){let s=t.startLine,i=t.startColumn+n.length;const r=e.StringUtilities.findLineEndingIndexes(n);if(r.length>1){s=t.startLine+r.length-1;const e=r.length;i=r[e-1]-r[e-2]-1}return new d(t.startLine,t.startColumn,s,i)}isEmpty(){return this.startLine===this.endLine&&this.startColumn===this.endColumn}immediatelyPrecedes(t){return!!t&&(this.endLine===t.startLine&&this.endColumn===t.startColumn)}immediatelyFollows(t){return!!t&&t.immediatelyPrecedes(this)}follows(t){return t.endLine===this.startLine&&t.endColumn<=this.startColumn||t.endLine<this.startLine}get linesCount(){return this.endLine-this.startLine}collapseToEnd(){return new d(this.endLine,this.endColumn,this.endLine,this.endColumn)}collapseToStart(){return new d(this.startLine,this.startColumn,this.startLine,this.startColumn)}normalize(){return this.startLine>this.endLine||this.startLine===this.endLine&&this.startColumn>this.endColumn?new d(this.endLine,this.endColumn,this.startLine,this.startColumn):this.clone()}clone(){return new d(this.startLine,this.startColumn,this.endLine,this.endColumn)}serializeToObject(){return{startLine:this.startLine,startColumn:this.startColumn,endLine:this.endLine,endColumn:this.endColumn}}compareTo(t){return this.startLine>t.startLine?1:this.startLine<t.startLine?-1:this.startColumn>t.startColumn?1:this.startColumn<t.startColumn?-1:0}compareToPosition(t,e){return t<this.startLine||t===this.startLine&&e<this.startColumn?-1:t>this.endLine||t===this.endLine&&e>this.endColumn?1:0}equal(t){return this.startLine===t.startLine&&this.endLine===t.endLine&&this.startColumn===t.startColumn&&this.endColumn===t.endColumn}relativeTo(t,e){const n=this.clone();return this.startLine===t&&(n.startColumn-=e),this.endLine===t&&(n.endColumn-=e),n.startLine-=t,n.endLine-=t,n}relativeFrom(t,e){const n=this.clone();return 0===this.startLine&&(n.startColumn+=e),0===this.endLine&&(n.endColumn+=e),n.startLine+=t,n.endLine+=t,n}rebaseAfterTextEdit(t,e){console.assert(t.startLine===e.startLine),console.assert(t.startColumn===e.startColumn);const n=this.clone();if(!this.follows(t))return n;const s=e.endLine-t.endLine,i=e.endColumn-t.endColumn;return n.startLine+=s,n.endLine+=s,n.startLine===e.endLine&&(n.startColumn+=i),n.endLine===e.endLine&&(n.endColumn+=i),n}toString(){return JSON.stringify(this)}containsLocation(t,e){return this.startLine===this.endLine?this.startLine===t&&this.startColumn<=e&&e<this.endColumn:this.startLine===t?this.startColumn<=e:this.endLine===t?e<this.endColumn:this.startLine<t&&t<this.endLine}get start(){return{lineNumber:this.startLine,columnNumber:this.startColumn}}get end(){return{lineNumber:this.endLine,columnNumber:this.endColumn}}intersection(t){let{startLine:e,startColumn:n}=this;e<t.startLine?(e=t.startLine,n=t.startColumn):e===t.startLine&&(n=Math.max(n,t.startColumn));let{endLine:s,endColumn:i}=this;return s>t.endLine?(s=t.endLine,i=t.endColumn):s===t.endLine&&(i=Math.min(i,t.endColumn)),e>s||e===s&&n>=i?new d(0,0,0,0):new d(e,n,s,i)}}class m{offset;length;constructor(t,e){this.offset=t,this.length=e}}var f=Object.freeze({__proto__:null,TextRange:d,SourceRange:m});class g{valueInternal;lineEndingsInternal;constructor(t){this.valueInternal=t}lineEndings(){return this.lineEndingsInternal||(this.lineEndingsInternal=e.StringUtilities.findLineEndingIndexes(this.valueInternal)),this.lineEndingsInternal}value(){return this.valueInternal}lineCount(){return this.lineEndings().length}offsetFromPosition(t,e){return(t?this.lineEndings()[t-1]+1:0)+e}positionFromOffset(t){const n=this.lineEndings(),s=e.ArrayUtilities.lowerBound(n,t,e.ArrayUtilities.DEFAULT_COMPARATOR);return{lineNumber:s,columnNumber:t-(s&&n[s-1]+1)}}lineAt(t){const e=this.lineEndings(),n=t>0?e[t-1]+1:0,s=e[t];let i=this.valueInternal.substring(n,s);return i.length>0&&"\r"===i.charAt(i.length-1)&&(i=i.substring(0,i.length-1)),i}toSourceRange(t){const e=this.offsetFromPosition(t.startLine,t.startColumn),n=this.offsetFromPosition(t.endLine,t.endColumn);return new m(e,n-e)}toTextRange(t){const e=new h(this.lineEndings()),n=d.createFromLocation(0,0);return e.resetTo(t.offset),n.startLine=e.lineNumber(),n.startColumn=e.columnNumber(),e.advance(t.offset+t.length),n.endLine=e.lineNumber(),n.endColumn=e.columnNumber(),n}replaceRange(t,e){const n=this.toSourceRange(t);return this.valueInternal.substring(0,n.offset)+e+this.valueInternal.substring(n.offset+n.length)}extract(t){const e=this.toSourceRange(t);return this.valueInternal.substr(e.offset,e.length)}}var C=Object.freeze({__proto__:null,Text:g});const x=/(?:^|\s)(\-)?([\w\-]+):([^\s]+)/,b=/(?:^|\s)(\-)?\/([^\/\\]+(\\.[^\/]*)*)\//,p=/(?:^|\s)(\-)?([^\s]+)/,L=/\s/,T={isSpaceChar:function(t){return L.test(t)},lineIndent:function(t){let e=0;for(;e<t.length&&T.isSpaceChar(t.charAt(e));)++e;return t.substr(0,e)},splitStringByRegexes(t,e){const n=[],s=[];for(let t=0;t<e.length;t++){const n=e[t];n.global?s.push(n):s.push(new RegExp(n.source,n.flags?n.flags+"g":"g"))}return function t(e,i,r){if(i>=s.length)return void n.push({value:e,position:r,regexIndex:-1,captureGroups:[]});const o=s[i];let a,l=0;o.lastIndex=0;for(;null!==(a=o.exec(e));){const s=e.substring(l,a.index);s&&t(s,i+1,r+l);const o=a[0];n.push({value:o,position:r+a.index,regexIndex:i,captureGroups:a.slice(1)}),l=a.index+o.length}const h=e.substring(l);h&&t(h,i+1,r+l)}(t,0,0),n}};const y=function(t,e,n,s){return a.isError(t)||!t.isTextContent?[]:I(t.text,e,n,s)},I=function(t,n,s,r){const o=e.StringUtilities.createSearchRegex(n,s,r),a=new g(t),l=[];for(let t=0;t<a.lineCount();++t){const e=a.lineAt(t),n=e.matchAll(o);for(const s of n)l.push(new i(t,e,s.index,s[0].length))}return l};var v=Object.freeze({__proto__:null,Utils:T,FilterParser:class{keys;constructor(t){this.keys=t}static cloneFilter(t){return{key:t.key,text:t.text,regex:t.regex,negative:t.negative}}parse(t){const e=T.splitStringByRegexes(t,[x,b,p]),n=[];for(const{regexIndex:t,captureGroups:s}of e)if(-1!==t)if(0===t){const t=s[0],e=s[1],i=s[2];-1!==this.keys.indexOf(e)?n.push({key:e,regex:void 0,text:i,negative:Boolean(t)}):n.push({key:void 0,regex:void 0,text:`${e}:${i}`,negative:Boolean(t)})}else if(1===t){const t=s[0],e=s[1];try{n.push({key:void 0,regex:new RegExp(e,"i"),text:void 0,negative:Boolean(t)})}catch(s){n.push({key:void 0,regex:void 0,text:`/${e}/`,negative:Boolean(t)})}}else if(2===t){const t=s[0],e=s[1];n.push({key:void 0,regex:void 0,text:e,negative:Boolean(t)})}return n}},BalancedJSONTokenizer:class{callback;index;balance;buffer;findMultiple;closingDoubleQuoteRegex;lastBalancedIndex;constructor(t,e){this.callback=t,this.index=0,this.balance=0,this.buffer="",this.findMultiple=e||!1,this.closingDoubleQuoteRegex=/[^\\](?:\\\\)*"/g}write(t){this.buffer+=t;const e=this.buffer.length,n=this.buffer;let s;for(s=this.index;s<e;++s){const t=n[s];if('"'===t){if(this.closingDoubleQuoteRegex.lastIndex=s,!this.closingDoubleQuoteRegex.test(n))break;s=this.closingDoubleQuoteRegex.lastIndex-1}else if("{"===t)++this.balance;else if("}"===t){if(--this.balance,this.balance<0)return this.reportBalanced(),!1;if(!this.balance&&(this.lastBalancedIndex=s+1,!this.findMultiple))break}else if("]"===t&&!this.balance)return this.reportBalanced(),!1}return this.index=s,this.reportBalanced(),!0}reportBalanced(){this.lastBalancedIndex&&(this.callback(this.buffer.slice(0,this.lastBalancedIndex)),this.buffer=this.buffer.slice(this.lastBalancedIndex),this.index-=this.lastBalancedIndex,this.lastBalancedIndex=0)}remainder(){return this.buffer}},detectIndentation:function(t){const e=[0,0,0,0,0,0,0,0,0];let n=0,s=0;for(const i of t){let t=0;if(0!==i.length){let e=i.charAt(0);if("\t"===e){n++;continue}for(;" "===e;)e=i.charAt(++t)}if(t===i.length){s=0;continue}const r=Math.abs(t-s);r<e.length&&(e[r]=e[r]+1),s=t}let i=0,r=0;for(let t=1;t<e.length;++t){const n=e[t];n>r&&(r=n,i=t)}return n>i?"\t":i?" ".repeat(i):null},isMinified:function(t){let e=0;for(let n=0;n<t.length;++e){let e=t.indexOf("\n",n);e<0&&(e=t.length),n=e+1}return(t.length-e)/e>=80},performSearchInContentData:y,performSearchInContent:I,performSearchInSearchMatches:function(t,n,s,r){const o=e.StringUtilities.createSearchRegex(n,s,r),a=[];for(const{lineNumber:e,lineContent:n}of t){const t=n.matchAll(o);for(const s of t)a.push(new i(e,n,s.index,s[0].length))}return a}});class A{#n;#s;#i;constructor(t,e,n){this.#n=t,this.#s=e,this.#i=n}static fromString(t,e,n){return new A(t,e,(()=>Promise.resolve(new a(n,!1,e.canonicalMimeType()))))}contentURL(){return this.#n}contentType(){return this.#s}requestContent(){return this.#i().then(a.asDeferredContent.bind(void 0))}requestContentData(){return this.#i()}async searchInContent(t,e,n){const s=await this.requestContentData();return y(s,t,e,n)}}var B=Object.freeze({__proto__:null,StaticContentProvider:A});class w extends n.ObjectWrapper.ObjectWrapper{mimeType;#r;#o;#a=[];#l;constructor(t,e,n){super(),this.mimeType=t,this.#r=e,this.#o=Boolean(n&&!n.createdFromBase64),this.#l=n}static create(t,e){return new w(t,e)}static from(t){return new w(t.mimeType,t.charset,t)}get isTextContent(){return e.MimeType.isTextType(this.mimeType)}addChunk(t){if(this.#o)throw new Error("Cannot add base64 data to a text-only ContentData.");this.#a.push(t),this.dispatchEventToListeners("ChunkAdded",{content:this,chunk:t})}content(){if(this.#l&&0===this.#a.length)return this.#l;const t=this.#l?.base64??"",n=this.#a.reduce(((t,n)=>e.StringUtilities.concatBase64(t,n)),t);return this.#l=new a(n,!0,this.mimeType,this.#r),this.#a=[],this.#l}}const E=function(t){return"error"in t};var N=Object.freeze({__proto__:null,StreamingContentData:w,isError:E,asDeferredContent:function(t){return E(t)?{error:t.error,content:null,isEncoded:!1}:t.content().asDeferedContent()}});var _=Object.freeze({__proto__:null,WasmDisassembly:class extends a{lines;#h;#u;#c;constructor(t,e,n){if(super("",!1,"text/x-wast","utf-8"),t.length!==e.length)throw new Error("Lines and offsets don't match");this.lines=t,this.#h=e,this.#u=n}get text(){return void 0===this.#c&&(this.#c=this.lines.join("\n")),this.#c}get isEmpty(){return 0===this.lines.length||1===this.lines.length&&0===this.lines[0].length}get lineNumbers(){return this.#h.length}bytecodeOffsetToLineNumber(t){return e.ArrayUtilities.upperBound(this.#h,t,e.ArrayUtilities.DEFAULT_COMPARATOR)-1}lineNumberToBytecodeOffset(t){return this.#h[t]}*nonBreakableLineNumbers(){let t=0,e=0;for(;t<this.lineNumbers;){if(e<this.#u.length){if(this.lineNumberToBytecodeOffset(t)>=this.#u[e].start){t=this.bytecodeOffsetToLineNumber(this.#u[e++].end)+1;continue}}yield t++}}asDeferedContent(){return{content:"",isEncoded:!1,wasmDisassemblyInfo:this}}}});export{s as CodeMirrorUtils,l as ContentData,o as ContentProvider,B as StaticContentProvider,N as StreamingContentData,C as Text,u as TextCursor,f as TextRange,v as TextUtils,_ as WasmDisassembly};
