<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina5_9" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Dev Launcher Error View Controller-->
        <scene sceneID="j7w-kc-rpg">
            <objects>
                <viewController storyboardIdentifier="EXDevLauncherErrorView" useStoryboardIdentifierAsRestorationIdentifier="YES" id="IMq-iY-6oO" customClass="EXDevLauncherErrorViewController" customModule="EXDevLauncher" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="a0B-Gx-7OO">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" tag="6634" contentMode="scaleToFill" axis="vertical" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="GM9-4h-o48" userLabel="Main View">
                                <rect key="frame" x="20" y="70" width="335" height="588"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="1Bh-aT-q2c" userLabel="Header Stack View">
                                        <rect key="frame" x="0.0" y="0.0" width="335" height="103"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="There was a problem loading the project." lineBreakMode="wordWrap" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Yz7-qW-osQ">
                                                <rect key="frame" x="0.0" y="0.0" width="335" height="57.333333333333336"/>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="24"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="This development build encountered the following error:" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NuU-oV-ttb">
                                                <rect key="frame" x="0.0" y="69.333333333333343" width="335" height="33.666666666666657"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="Yt5-QM-Cuc" userLabel="Error Stack View">
                                        <rect key="frame" x="0.0" y="121" width="335" height="467"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Error infromation" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VXA-BB-Jf6">
                                                <rect key="frame" x="0.0" y="0.0" width="335" height="17"/>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="YTm-Ru-mwN">
                                                <rect key="frame" x="0.0" y="29" width="335" height="438"/>
                                                <color key="backgroundColor" white="1" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="separatorColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="sectionIndexColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="sectionIndexBackgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="sectionIndexTrackingBackgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <prototypes>
                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" selectionStyle="none" hidesAccessoryWhenEditing="NO" indentationLevel="1" indentationWidth="0.0" reuseIdentifier="cell" id="SER-Yx-7Ha" customClass="EXDevLauncherStackTrace" customModule="EXDevLauncher" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="50" width="335" height="35.333332061767578"/>
                                                        <autoresizingMask key="autoresizingMask"/>
                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="SER-Yx-7Ha" id="17f-61-Bd4">
                                                            <rect key="frame" x="0.0" y="0.0" width="335" height="35.333332061767578"/>
                                                            <autoresizingMask key="autoresizingMask"/>
                                                            <subviews>
                                                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="equalSpacing" translatesAutoresizingMaskIntoConstraints="NO" id="7Ns-Yn-uv2">
                                                                    <rect key="frame" x="0.0" y="1.9999999999999982" width="335" height="31.333333333333329"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="onCreate" textAlignment="natural" lineBreakMode="characterWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="B3N-pO-Laq">
                                                                            <rect key="frame" x="0.0" y="0.0" width="335" height="17"/>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                                            <nil key="textColor"/>
                                                                            <color key="highlightedColor" systemColor="linkColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="Controller.swift" textAlignment="natural" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Yg3-Rb-p8Q">
                                                                            <rect key="frame" x="0.0" y="17" width="335" height="14.333333333333336"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="14.333333333333336" id="fja-si-I3S"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                                            <nil key="textColor"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                </stackView>
                                                            </subviews>
                                                            <color key="backgroundColor" red="0.1058823529" green="0.1215686275" blue="0.13725490200000001" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="7Ns-Yn-uv2" firstAttribute="leading" secondItem="17f-61-Bd4" secondAttribute="leading" id="9T1-mE-A6E"/>
                                                                <constraint firstAttribute="trailing" secondItem="7Ns-Yn-uv2" secondAttribute="trailing" id="Ivh-cg-s4q"/>
                                                                <constraint firstAttribute="bottom" secondItem="7Ns-Yn-uv2" secondAttribute="bottom" constant="2" id="Rnl-QP-qVn" userLabel="bottom = Stack View.bottom + 2"/>
                                                                <constraint firstItem="7Ns-Yn-uv2" firstAttribute="top" secondItem="17f-61-Bd4" secondAttribute="top" constant="2" id="pLj-fw-53s" userLabel="Stack View.top = top + 2"/>
                                                            </constraints>
                                                        </tableViewCellContentView>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <connections>
                                                            <outlet property="file" destination="Yg3-Rb-p8Q" id="cOR-G4-Oc0"/>
                                                            <outlet property="function" destination="B3N-pO-Laq" id="HBW-sk-Gho"/>
                                                        </connections>
                                                    </tableViewCell>
                                                </prototypes>
                                            </tableView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                            </stackView>
                            <view tag="6634" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zFL-lG-zl9" userLabel="Footer View">
                                <rect key="frame" x="0.0" y="658" width="375" height="120"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="C4a-fX-PLy">
                                        <rect key="frame" x="20" y="20" width="335" height="80"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WWt-h3-sGX">
                                                <rect key="frame" x="0.0" y="0.0" width="335" height="37"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="filled" title="Reload">
                                                    <fontDescription key="titleFontDescription" type="boldSystem" pointSize="14"/>
                                                    <color key="baseForegroundColor" red="0.94117647059999998" green="0.94509803920000002" blue="0.94901960780000005" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <color key="baseBackgroundColor" red="0.1083101556" green="0.12358053770000001" blue="0.13850507140000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </buttonConfiguration>
                                                <connections>
                                                    <action selector="reload:" destination="IMq-iY-6oO" eventType="touchDown" id="tFU-23-8WE"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="L9n-He-0VJ">
                                                <rect key="frame" x="0.0" y="43" width="335" height="37"/>
                                                <state key="normal" title="Button"/>
                                                <buttonConfiguration key="configuration" style="filled" title="Go to home">
                                                    <fontDescription key="titleFontDescription" type="boldSystem" pointSize="14"/>
                                                    <color key="baseForegroundColor" red="0.10588235294117647" green="0.12156862745098039" blue="0.13725490196078433" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <color key="baseBackgroundColor" red="0.94117647058823528" green="0.94509803921568625" blue="0.94901960784313721" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </buttonConfiguration>
                                                <connections>
                                                    <action selector="goToHome:" destination="IMq-iY-6oO" eventType="touchDown" id="HyS-3z-WBf"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="tintColor" red="0.94117647058823528" green="0.94509803921568625" blue="0.94901960784313721" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" constant="120" id="KqV-iT-ZRo"/>
                                    <constraint firstItem="C4a-fX-PLy" firstAttribute="top" secondItem="zFL-lG-zl9" secondAttribute="top" constant="20" id="PnF-4a-zPi"/>
                                    <constraint firstItem="C4a-fX-PLy" firstAttribute="leading" secondItem="zFL-lG-zl9" secondAttribute="leading" constant="20" id="cuV-lj-FP4"/>
                                    <constraint firstAttribute="bottom" secondItem="C4a-fX-PLy" secondAttribute="bottom" constant="20" id="dW1-P1-gCh"/>
                                    <constraint firstAttribute="trailing" secondItem="C4a-fX-PLy" secondAttribute="trailing" constant="20" id="dau-76-cYN"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="c2L-io-eK1"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="zFL-lG-zl9" firstAttribute="top" secondItem="GM9-4h-o48" secondAttribute="bottom" id="Ieu-gd-kCR"/>
                            <constraint firstItem="c2L-io-eK1" firstAttribute="trailing" secondItem="GM9-4h-o48" secondAttribute="trailing" constant="20" id="Lgo-wV-ONP"/>
                            <constraint firstItem="c2L-io-eK1" firstAttribute="trailing" secondItem="zFL-lG-zl9" secondAttribute="trailing" id="bfY-0L-Cu5"/>
                            <constraint firstItem="GM9-4h-o48" firstAttribute="leading" secondItem="c2L-io-eK1" secondAttribute="leading" constant="20" id="kLe-G2-WId"/>
                            <constraint firstItem="zFL-lG-zl9" firstAttribute="leading" secondItem="c2L-io-eK1" secondAttribute="leading" id="mnq-cN-iSc"/>
                            <constraint firstItem="c2L-io-eK1" firstAttribute="bottom" secondItem="zFL-lG-zl9" secondAttribute="bottom" id="pL3-gB-D67"/>
                            <constraint firstItem="GM9-4h-o48" firstAttribute="top" secondItem="c2L-io-eK1" secondAttribute="top" constant="20" id="pmV-TO-6PO"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="CCd-BK-HXN"/>
                    <connections>
                        <outlet property="errorInformation" destination="VXA-BB-Jf6" id="R6L-gX-8wL"/>
                        <outlet property="errorStack" destination="YTm-Ru-mwN" id="l91-m5-Lbh"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UpG-SF-d9I" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1233" y="527"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="linkColor">
            <color red="0.0" green="0.47843137250000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
