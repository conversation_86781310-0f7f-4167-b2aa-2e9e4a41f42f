import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/theme_support/theme_support.js";import*as i from"../../models/trace/trace.js";import*as n from"../../core/sdk/sdk.js";import*as r from"../../core/common/common.js";import*as a from"../../core/root/root.js";import*as s from"./components/components.js";import{Utils as o}from"./components/components.js";import*as l from"../../core/host/host.js";import*as c from"../../core/platform/platform.js";import*as d from"../../models/workspace/workspace.js";import*as h from"../../services/trace_bounds/trace_bounds.js";import*as m from"../../ui/components/adorners/adorners.js";import*as p from"../../ui/legacy/components/perf_ui/perf_ui.js";import*as u from"../../ui/legacy/legacy.js";import*as g from"../../ui/visual_logging/visual_logging.js";import*as v from"../mobile_throttling/mobile_throttling.js";import*as T from"./components/insights/insights.js";import*as f from"../../ui/components/menus/menus.js";import*as y from"../../models/source_map_scopes/source_map_scopes.js";import*as w from"../../models/extensions/extensions.js";import*as b from"../../models/bindings/bindings.js";import*as S from"./overlays/overlays.js";import*as C from"../../models/timeline_model/timeline_model.js";import*as E from"../../ui/legacy/components/utils/utils.js";import*as k from"../../ui/legacy/components/data_grid/data_grid.js";import*as x from"../../ui/components/code_highlighter/code_highlighter.js";import*as P from"./extensions/extensions.js";import*as I from"../layer_viewer/layer_viewer.js";import*as M from"../../ui/components/data_grid/data_grid.js";import*as F from"../../ui/components/linkifier/linkifier.js";import*as L from"../../ui/lit-html/lit-html.js";import*as R from"./utils/utils.js";import*as D from"../../ui/components/panel_feedback/panel_feedback.js";import*as A from"../../ui/components/icon_button/icon_button.js";const N={sSelfS:"{PH1} (self {PH2})"},B=e.i18n.registerUIStrings("panels/timeline/AppenderUtils.ts",N),H=e.i18n.getLocalizedString.bind(void 0,B);function U(e){const i={padding:4,height:17,collapsible:!0,color:t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),backgroundColor:t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),nestingLevel:0,shareHeaderLine:!0};return Object.assign(i,e)}function W(e,t,i,n,r,a,s,o){const l={startLevel:t,name:i,style:n,selectable:r,expanded:a,showStackContextMenu:s,legends:o};return null!==e&&(l.jslogContext=e),l}function O(t,n){const r=i.Helpers.Timing.microSecondsToMilliseconds(t||0);if(r===i.Types.Timing.MilliSeconds(0))return"";const a=i.Helpers.Timing.microSecondsToMilliseconds(n||0),s=1e-6;return Math.abs(r-a)>s&&a>s?H(N.sSelfS,{PH1:e.TimeUtilities.millisToString(r,!0),PH2:e.TimeUtilities.millisToString(a,!0)}):e.TimeUtilities.millisToString(r,!0)}function V(e,t){let i=0;const n=e.ts,r=e.ts+(e.dur||0);for(;i<t.length&&n<t[i];)++i;return t[i]=r,i}function _(e,t,i){const n=e.entryDecorations[t]||[];n.push(i),e.entryDecorations[t]=n}var z=Object.freeze({__proto__:null,buildGroupStyle:U,buildTrackHeader:W,getFormattedTime:O,getEventLevel:V,addDecorationToEvent:_});const G={animations:"Animations"},j=e.i18n.registerUIStrings("panels/timeline/AnimationsTrackAppender.ts",G),q=e.i18n.getLocalizedString.bind(void 0,j);class ${appenderName="Animations";#e;#t;constructor(e,t){this.#e=e,this.#t=t}appendTrackAtLevel(e,t){const i=this.#t.Animations.animations;return 0===i.length?e:(this.#i(e,t),this.#e.appendEventsAtLevel(i,e,this))}#i(e,t){const i=U({useFirstLineForOverview:!1}),n=W("animations",e,q(G.animations),i,!0,t);this.#e.registerTrackForGroup(n,this)}colorForEvent(){return t.ThemeSupport.instance().getComputedValue("--app-color-rendering")}titleForEvent(e){return e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:O(e.dur)}}}var J=Object.freeze({__proto__:null,AnimationsTrackAppender:$});class K extends Event{duration;static eventName="traceload";constructor(e){super(K.eventName,{bubbles:!0,composed:!0}),this.duration=e}}var Y=Object.freeze({__proto__:null,TraceLoadEvent:K});class X{x;y;width;height;color;outlineColor;constructor([e,t,i,n]){this.x=e,this.y=t,this.width=i,this.height=n,this.color={r:238,g:111,b:99,a:.4},this.outlineColor={r:238,g:111,b:99,a:.7}}}let Z;class Q{static instance(e={forceNew:null}){const{forceNew:t}=e;return Z&&!t||(Z=new Q),Z}linkify(e,t){const i=document.createElement("span"),r=e,{x:a,y:s,width:o,height:l}=r;return i.textContent=`Location: [${a},${s}], Size: [${o}x${l}]`,i.addEventListener("mouseover",(()=>n.OverlayModel.OverlayModel.highlightRect(r))),i.addEventListener("mouseleave",(()=>n.OverlayModel.OverlayModel.clearHighlight())),i}}var ee=Object.freeze({__proto__:null,CLSRect:X,Linkifier:Q});const te={loading:"Loading",experience:"Experience",scripting:"Scripting",rendering:"Rendering",painting:"Painting",gpu:"GPU",async:"Async",system:"System",idle:"Idle",task:"Task",other:"Other",animation:"Animation",event:"Event",requestMainThreadFrame:"Request Main Thread Frame",frameStart:"Frame Start",onMessage:"On Message",schedulePostMessage:"Schedule postMessage",messaging:"Messaging",frameStartMainThread:"Frame Start (main thread)",drawFrame:"Draw Frame",profilingOverhead:"Profiling Overhead",hitTest:"Hit Test",scheduleStyleRecalculation:"Schedule Style Recalculation",recalculateStyle:"Recalculate Style",invalidateLayout:"Invalidate Layout",layerize:"Layerize",layout:"Layout",paintSetup:"Paint Setup",paintImage:"Paint Image",prePaint:"Pre-Paint",updateLayer:"Update Layer",updateLayerTree:"Update Layer Tree",paint:"Paint",rasterizePaint:"Rasterize Paint",scroll:"Scroll",commit:"Commit",compositeLayers:"Composite Layers",computeIntersections:"Compute Intersections",parseHtml:"Parse HTML",parseStylesheet:"Parse Stylesheet",installTimer:"Install Timer",removeTimer:"Remove Timer",timerFired:"Timer Fired",xhrReadyStateChange:"`XHR` Ready State Change",xhrLoad:"`XHR` Load",compileScript:"Compile Script",cacheScript:"Cache Script Code",compileCode:"Compile Code",optimizeCode:"Optimize Code",evaluateScript:"Evaluate Script",compileModule:"Compile Module",cacheModule:"Cache Module Code",evaluateModule:"Evaluate Module",streamingCompileTask:"Streaming Compile Task",waitingForNetwork:"Waiting for Network",parseAndCompile:"Parse and Compile",deserializeCodeCache:"Deserialize Code Cache",streamingWasmResponse:"Streaming Wasm Response",compiledWasmModule:"Compiled Wasm Module",cachedWasmModule:"Cached Wasm Module",wasmModuleCacheHit:"Wasm Module Cache Hit",wasmModuleCacheInvalid:"Wasm Module Cache Invalid",frameStartedLoading:"Frame Started Loading",onloadEvent:"Onload Event",domcontentloadedEvent:"DOMContentLoaded Event",firstPaint:"First Paint",firstContentfulPaint:"First Contentful Paint",largestContentfulPaint:"Largest Contentful Paint",timestamp:"Timestamp",consoleTime:"Console Time",userTiming:"User Timing",willSendRequest:"Will Send Request",sendRequest:"Send Request",receiveResponse:"Receive Response",finishLoading:"Finish Loading",receiveData:"Receive Data",runMicrotasks:"Run Microtasks",functionCall:"Function Call",gcEvent:"GC Event",majorGc:"Major GC",minorGc:"Minor GC",requestAnimationFrame:"Request Animation Frame",cancelAnimationFrame:"Cancel Animation Frame",animationFrameFired:"Animation Frame Fired",requestIdleCallback:"Request Idle Callback",cancelIdleCallback:"Cancel Idle Callback",fireIdleCallback:"Fire Idle Callback",createWebsocket:"Create WebSocket",sendWebsocketHandshake:"Send WebSocket Handshake",receiveWebsocketHandshake:"Receive WebSocket Handshake",wsMessageReceived:"Receive WebSocket Message",wsMessageSent:"Send WebSocket Message",destroyWebsocket:"Destroy WebSocket",embedderCallback:"Embedder Callback",imageDecode:"Image Decode",domGc:"DOM GC",cppGc:"CPP GC",encrypt:"Encrypt",encryptReply:"Encrypt Reply",decrypt:"Decrypt",decryptReply:"Decrypt Reply",digest:"Digest",digestReply:"Digest Reply",sign:"Sign",signReply:"Sign Reply",verify:"Verify",verifyReply:"Verify Reply",asyncTask:"Async Task",layoutShift:"Layout Shift",eventTiming:"Event Timing",jsFrame:"JS Frame",rasterizing:"Rasterizing",drawing:"Drawing"};var ie;let ne;!function(e){e.DRAWING="drawing",e.RASTERIZING="rasterizing",e.LAYOUT="layout",e.LOADING="loading",e.EXPERIENCE="experience",e.SCRIPTING="scripting",e.MESSAGING="messaging",e.RENDERING="rendering",e.PAINTING="painting",e.GPU="gpu",e.ASYNC="async",e.OTHER="other",e.IDLE="idle"}(ie||(ie={}));const re=e.i18n.registerUIStrings("panels/timeline/EventUICategory.ts",te),ae=e.i18n.getLocalizedString.bind(void 0,re);class se{title;category;hidden;constructor(e,t,i=!1){this.title=e,this.category=t,this.hidden=i}}class oe{name;title;visible;childColor;colorInternal;hiddenInternal;constructor(e,t,i,n,r){this.name=e,this.title=t,this.visible=i,this.childColor=n,this.colorInternal=r,this.hidden=!1}get hidden(){return Boolean(this.hiddenInternal)}get color(){return this.getComputedColorValue()}getCSSValue(){return`var(${this.colorInternal})`}getComputedColorValue(){return t.ThemeSupport.instance().getComputedValue(this.colorInternal)}set hidden(e){this.hiddenInternal=e}}let le,ce;function de(e){return pe()[e]}function he(e){return Object.values(ie).includes(e)}function me(){return le||(le={loading:new oe(ie.LOADING,ae(te.loading),!0,"--app-color-loading-children","--app-color-loading"),experience:new oe(ie.EXPERIENCE,ae(te.experience),!1,"--app-color-rendering-children","--app-color-rendering"),messaging:new oe(ie.MESSAGING,ae(te.messaging),!0,"--app-color-messaging-children","--app-color-messaging"),scripting:new oe(ie.SCRIPTING,ae(te.scripting),!0,"--app-color-scripting-children","--app-color-scripting"),rendering:new oe(ie.RENDERING,ae(te.rendering),!0,"--app-color-rendering-children","--app-color-rendering"),painting:new oe(ie.PAINTING,ae(te.painting),!0,"--app-color-painting-children","--app-color-painting"),gpu:new oe(ie.GPU,ae(te.gpu),!1,"--app-color-painting-children","--app-color-painting"),async:new oe(ie.ASYNC,ae(te.async),!1,"--app-color-async-children","--app-color-async"),other:new oe(ie.OTHER,ae(te.system),!1,"--app-color-system-children","--app-color-system"),idle:new oe(ie.IDLE,ae(te.idle),!1,"--app-color-idle-children","--app-color-idle"),layout:new oe(ie.LAYOUT,ae(te.layout),!1,"--app-color-loading-children","--app-color-loading"),rasterizing:new oe(ie.RASTERIZING,ae(te.rasterizing),!1,"--app-color-children","--app-color-scripting"),drawing:new oe(ie.DRAWING,ae(te.drawing),!1,"--app-color-rendering-children","--app-color-rendering")},le)}function pe(){if(ce)return ce;const e=me();return ce={RunTask:new se(ae(te.task),e.other),ProfileCall:new se(ae(te.jsFrame),e.scripting),JSSample:new se("JSSample",e.scripting),Program:new se(ae(te.other),e.other),"CpuProfiler::StartProfiling":new se(ae(te.profilingOverhead),e.other),Animation:new se(ae(te.animation),e.rendering),EventDispatch:new se(ae(te.event),e.scripting),RequestMainThreadFrame:new se(ae(te.requestMainThreadFrame),e.rendering,!0),BeginFrame:new se(ae(te.frameStart),e.rendering,!0),BeginMainThreadFrame:new se(ae(te.frameStartMainThread),e.rendering,!0),DrawFrame:new se(ae(te.drawFrame),e.rendering,!0),HitTest:new se(ae(te.hitTest),e.rendering),ScheduleStyleRecalculation:new se(ae(te.scheduleStyleRecalculation),e.rendering),UpdateLayoutTree:new se(ae(te.recalculateStyle),e.rendering),InvalidateLayout:new se(ae(te.invalidateLayout),e.rendering,!0),Layerize:new se(ae(te.layerize),e.rendering),Layout:new se(ae(te.layout),e.rendering),PaintSetup:new se(ae(te.paintSetup),e.painting),PaintImage:new se(ae(te.paintImage),e.painting,!0),UpdateLayer:new se(ae(te.updateLayer),e.painting,!0),UpdateLayerTree:new se(ae(te.updateLayerTree),e.rendering),Paint:new se(ae(te.paint),e.painting),PrePaint:new se(ae(te.prePaint),e.rendering),RasterTask:new se(ae(te.rasterizePaint),e.painting),ScrollLayer:new se(ae(te.scroll),e.rendering),Commit:new se(ae(te.commit),e.painting),CompositeLayers:new se(ae(te.compositeLayers),e.painting),ComputeIntersections:new se(ae(te.computeIntersections),e.rendering),ParseHTML:new se(ae(te.parseHtml),e.loading),ParseAuthorStyleSheet:new se(ae(te.parseStylesheet),e.loading),TimerInstall:new se(ae(te.installTimer),e.scripting),TimerRemove:new se(ae(te.removeTimer),e.scripting),TimerFire:new se(ae(te.timerFired),e.scripting),XHRReadyStateChange:new se(ae(te.xhrReadyStateChange),e.scripting),XHRLoad:new se(ae(te.xhrLoad),e.scripting),"v8.compile":new se(ae(te.compileScript),e.scripting),"v8.produceCache":new se(ae(te.cacheScript),e.scripting),"V8.CompileCode":new se(ae(te.compileCode),e.scripting),"V8.OptimizeCode":new se(ae(te.optimizeCode),e.scripting),EvaluateScript:new se(ae(te.evaluateScript),e.scripting),"V8.CompileModule":new se(ae(te.compileModule),e.scripting),"v8.produceModuleCache":new se(ae(te.cacheModule),e.scripting),"v8.evaluateModule":new se(ae(te.evaluateModule),e.scripting),"v8.parseOnBackground":new se(ae(te.streamingCompileTask),e.other),"v8.parseOnBackgroundWaiting":new se(ae(te.waitingForNetwork),e.idle),"v8.parseOnBackgroundParsing":new se(ae(te.parseAndCompile),e.scripting),"v8.deserializeOnBackground":new se(ae(te.deserializeCodeCache),e.scripting),"V8.FinalizeDeserialization":new se(ae(te.profilingOverhead),e.other),"v8.wasm.streamFromResponseCallback":new se(ae(te.streamingWasmResponse),e.scripting),"v8.wasm.compiledModule":new se(ae(te.compiledWasmModule),e.scripting),"v8.wasm.cachedModule":new se(ae(te.cachedWasmModule),e.scripting),"v8.wasm.moduleCacheHit":new se(ae(te.wasmModuleCacheHit),e.scripting),"v8.wasm.moduleCacheInvalid":new se(ae(te.wasmModuleCacheInvalid),e.scripting),FrameStartedLoading:new se(ae(te.frameStartedLoading),e.loading,!0),MarkLoad:new se(ae(te.onloadEvent),e.scripting,!0),MarkDOMContent:new se(ae(te.domcontentloadedEvent),e.scripting,!0),firstPaint:new se(ae(te.firstPaint),e.painting,!0),firstContentfulPaint:new se(ae(te.firstContentfulPaint),e.rendering,!0),"largestContentfulPaint::Candidate":new se(ae(te.largestContentfulPaint),e.rendering,!0),TimeStamp:new se(ae(te.timestamp),e.scripting),ConsoleTime:new se(ae(te.consoleTime),e.scripting),UserTiming:new se(ae(te.userTiming),e.scripting),ResourceWillSendRequest:new se(ae(te.willSendRequest),e.loading),ResourceSendRequest:new se(ae(te.sendRequest),e.loading),ResourceReceiveResponse:new se(ae(te.receiveResponse),e.loading),ResourceFinish:new se(ae(te.finishLoading),e.loading),ResourceReceivedData:new se(ae(te.receiveData),e.loading),RunMicrotasks:new se(ae(te.runMicrotasks),e.scripting),FunctionCall:new se(ae(te.functionCall),e.scripting),GCEvent:new se(ae(te.gcEvent),e.scripting),MajorGC:new se(ae(te.majorGc),e.scripting),MinorGC:new se(ae(te.minorGc),e.scripting),"CppGC.IncrementalSweep":new se(ae(te.cppGc),e.scripting),RequestAnimationFrame:new se(ae(te.requestAnimationFrame),e.scripting),CancelAnimationFrame:new se(ae(te.cancelAnimationFrame),e.scripting),FireAnimationFrame:new se(ae(te.animationFrameFired),e.scripting),RequestIdleCallback:new se(ae(te.requestIdleCallback),e.scripting),CancelIdleCallback:new se(ae(te.cancelIdleCallback),e.scripting),FireIdleCallback:new se(ae(te.fireIdleCallback),e.scripting),WebSocketCreate:new se(ae(te.createWebsocket),e.scripting),WebSocketSendHandshakeRequest:new se(ae(te.sendWebsocketHandshake),e.scripting),WebSocketReceiveHandshakeResponse:new se(ae(te.receiveWebsocketHandshake),e.scripting),WebSocketDestroy:new se(ae(te.destroyWebsocket),e.scripting),WebSocketSend:new se(ae(te.wsMessageSent),e.scripting),WebSocketReceive:new se(ae(te.wsMessageReceived),e.scripting),EmbedderCallback:new se(ae(te.embedderCallback),e.scripting),"Decode Image":new se(ae(te.imageDecode),e.painting),GPUTask:new se(ae(te.gpu),e.gpu),"BlinkGC.AtomicPhase":new se(ae(te.domGc),e.scripting),DoEncrypt:new se(ae(te.encrypt),e.scripting),DoEncryptReply:new se(ae(te.encryptReply),e.scripting),DoDecrypt:new se(ae(te.decrypt),e.scripting),DoDecryptReply:new se(ae(te.decryptReply),e.scripting),DoDigest:new se(ae(te.digest),e.scripting),DoDigestReply:new se(ae(te.digestReply),e.scripting),DoSign:new se(ae(te.sign),e.scripting),DoSignReply:new se(ae(te.signReply),e.scripting),DoVerify:new se(ae(te.verify),e.scripting),DoVerifyReply:new se(ae(te.verifyReply),e.scripting),AsyncTask:new se(ae(te.asyncTask),e.async),LayoutShift:new se(ae(te.layoutShift),e.experience),EventTiming:new se(ae(te.eventTiming),e.experience),HandlePostMessage:new se(ae(te.onMessage),e.messaging),SchedulePostMessage:new se(ae(te.schedulePostMessage),e.messaging)},ce}function ue(e){ce=e}function ge(e){le=e}function ve(){const e=pe(),t=[];for(const i in e){const n=i;e[n]?.hidden||t.push(i)}return t}function Te(){return ne||(ne=[ie.IDLE,ie.LOADING,ie.PAINTING,ie.RENDERING,ie.SCRIPTING,ie.OTHER],ne)}function fe(e){ne=e}var ye=Object.freeze({__proto__:null,get EventCategory(){return ie},TimelineRecordStyle:se,TimelineCategory:oe,getEventStyle:de,stringIsEventCategory:he,getCategoryStyles:me,maybeInitSylesMap:pe,setEventStylesMap:ue,setCategories:ge,visibleTypes:ve,getTimelineMainEventCategories:Te,setTimelineMainEventCategories:fe});let we=null;class be{static instance(e={forceNew:null}){const t=Boolean(e.forceNew);return we&&!t||(we=new be),we}static removeInstance(){we=null}#n=[];activeFilters(){return this.#n}setFilters(e){this.#n=e}isVisible(e){return this.#n.every((t=>t.accept(e)))}}let Se=null;class Ce{#r=new WeakSet;static instance(e={forceNew:!1}){return Se&&!e.forceNew||(Se=new Ce),Se}registerFreshRecording(e){this.#r.add(e)}recordingIsFresh(e){return this.#r.has(e)}}var Ee=Object.freeze({__proto__:null,Tracker:Ce});const ke=new CSSStyleSheet;ke.replaceSync('.content{margin-left:5px}.history-dropdown-button{width:160px;height:26px;text-align:left;display:flex;border:1px solid transparent}.history-dropdown-button[disabled]{opacity:50%;border:1px solid transparent}.history-dropdown-button > .content{padding-right:5px;overflow:hidden;text-overflow:ellipsis;flex:1 1;min-width:35px;&::after{float:right;user-select:none;mask-image:var(--image-file-triangle-down);width:14px;height:14px;content:"";position:absolute;background-color:var(--icon-default);right:-3px}}.history-dropdown-button:focus-visible::before{content:"";position:absolute;top:2px;left:0;right:0;bottom:2px;border-radius:2px;background:var(--divider-line)}@media (forced-colors: active){.history-dropdown-button[disabled]{opacity:100%}.history-dropdown-button > .content::after{background-color:ButtonText}.history-dropdown-button[disabled] > .content::after{background-color:GrayText}}\n/*# sourceURL=historyToolbarButton.css */\n');const xe={empty:"(empty)",selectJavascriptVmInstance:"Select JavaScript VM instance"},Pe=e.i18n.registerUIStrings("panels/timeline/IsolateSelector.ts",xe),Ie=e.i18n.getLocalizedString.bind(void 0,Pe);class Me extends u.Toolbar.ToolbarItem{menu;options;items;itemByIsolate=new Map;constructor(){const e=new f.SelectMenu.SelectMenu;super(e),this.menu=e,e.buttonTitle=Ie(xe.selectJavascriptVmInstance),e.showArrow=!0,e.style.whiteSpace="normal",e.addEventListener("selectmenuselected",this.#a.bind(this)),n.IsolateManager.IsolateManager.instance().observeIsolates(this),n.TargetManager.TargetManager.instance().addEventListener("NameChanged",this.targetChanged,this),n.TargetManager.TargetManager.instance().addEventListener("InspectedURLChanged",this.targetChanged,this)}#s(e,t){const i=new Map;for(const t of e.models()){const e=t.target(),a=n.TargetManager.TargetManager.instance().rootTarget()!==e?e.name():"",s=new r.ParsedURL.ParsedURL(e.inspectedURL()),o=s.isValid?s.domain():"",l=e.decorateLabel(o&&a?`${o}: ${a}`:a||o||Ie(xe.empty));i.set(l,(i.get(l)||0)+1)}t.removeChildren();for(const[e,n]of i){const i=n>1?`${e} (${n})`:e;t.createChild("div").textContent=i}}#a(e){this.itemByIsolate.forEach(((t,i)=>{if(t.selected=t.value===e.itemValue,t.selected){const e=t.textContent?.slice(0,29);this.menu.buttonTitle=e||Ie(xe.empty);const r=i.runtimeModel();u.Context.Context.instance().setFlavor(n.CPUProfilerModel.CPUProfilerModel,r&&r.target().model(n.CPUProfilerModel.CPUProfilerModel))}}))}isolateAdded(e){const t=new f.Menu.MenuItem;this.menu.appendChild(t),t.value=e.id(),this.itemByIsolate.set(e,t),this.#s(e,t)}isolateRemoved(e){const t=this.itemByIsolate.get(e);t&&(t.selected&&(this.menu.buttonTitle=Ie(xe.selectJavascriptVmInstance),u.Context.Context.instance().setFlavor(n.CPUProfilerModel.CPUProfilerModel,null)),this.menu.removeChild(t))}isolateChanged(e){const t=this.itemByIsolate.get(e);t&&this.#s(e,t)}targetChanged(e){const t=e.data.model(n.RuntimeModel.RuntimeModel);if(!t)return;const i=n.IsolateManager.IsolateManager.instance().isolateByModel(t);i&&this.isolateChanged(i)}}class Fe{#o=new Map;keyForEvent(e){if(i.Types.TraceEvents.isProfileCall(e))return`p-${e.pid}-${e.tid}-${i.Types.TraceEvents.SampleIndex(e.sampleIndex)}-${e.nodeId}`;const t=i.Helpers.SyntheticEvents.SyntheticEventsManager.getActiveManager().getRawTraceEvents(),n=i.Types.TraceEvents.isSyntheticBasedEvent(e)?`s-${t.indexOf(e.rawSourceEvent)}`:`r-${t.indexOf(e)}`;return n.length<3?null:n}eventForKey(e,t){const n=i.Types.File.traceEventKeyToValues(e);if(Fe.isProfileCallKey(n))return this.#l(n,t);if(Fe.isSyntheticEventKey(n)){const e=i.Helpers.SyntheticEvents.SyntheticEventsManager.getActiveManager().getSyntheticTraceEvents().at(n.rawIndex);if(!e)throw new Error(`Attempted to get a synthetic event from an unknown raw event index: ${n.rawIndex}`);return e}if(Fe.isRawEventKey(n)){return i.Helpers.SyntheticEvents.SyntheticEventsManager.getActiveManager().getRawTraceEvents()[n.rawIndex]}throw new Error(`Unknown trace event serializable key values: ${n.join("-")}`)}static isProfileCallKey(e){return"p"===e.type}static isRawEventKey(e){return"r"===e.type}static isSyntheticEventKey(e){return"s"===e.type}#l(e,t){const i=this.#o.get(e);if(i)return i;const n=t.Renderer.processes.get(e.processID)?.threads.get(e.threadID)?.profileCalls;if(!n)throw new Error(`Unknown profile call serializable key: ${e}`);const r=c.ArrayUtilities.nearestIndexFromBeginning(n,(t=>t.sampleIndex>=e.sampleIndex&&t.nodeId>=e.protocol)),a=null!==r&&n.at(r);if(!a)throw new Error(`Unknown profile call serializable key: ${e}`);return this.#o.set(e,a),a}}var Le=Object.freeze({__proto__:null,EventsSerializer:Fe});const Re=[];let De;class Ae extends Event{overlay;action;static eventName="annotationmodifiedevent";constructor(e,t){super(Ae.eventName),this.overlay=e,this.action=t}}class Ne extends EventTarget{#c;#d;#h=null;#t;#m;#p;static activeManager(){return De}static initAndActivateModificationsManager(e,t){Re[t]&&(De=Re[t],Ne.activeManager()?.applyModificationsIfPresent());const i=e.traceParsedData(t);if(!i)throw new Error("ModificationsManager was initialized without a corresponding trace data");const n=i.Meta.traceBounds,r=e.rawTraceEvents(t);if(!r)throw new Error("ModificationsManager was initialized without a corresponding raw trace events array");const a=e.syntheticTraceEventsManager(t);if(!a)throw new Error("ModificationsManager was initialized without a corresponding SyntheticEventsManager");const s=e.metadata(t),o=new Ne({traceParsedData:i,traceBounds:n,rawTraceEvents:r,modifications:s?.modifications,syntheticEvents:a.getSyntheticTraceEvents()});return Re[t]=o,De=o,Ne.activeManager()?.applyModificationsIfPresent(),this.activeManager()}constructor({traceParsedData:e,traceBounds:t,modifications:n}){super();const r=new Map([...e.Samples.entryToNode,...e.Renderer.entryToNode]);this.#c=new i.EntriesFilter.EntriesFilter(r),this.#d=new s.Breadcrumbs.Breadcrumbs(t),this.#h=n||null,this.#t=e,this.#m=new Fe,this.#p=new Map}getEntriesFilter(){return this.#c}getTimelineBreadcrumbs(){return this.#d}createAnnotation(e){const t={type:"ENTRY_LABEL",entry:e.entry,label:e.label};this.#p.set(e,t),this.dispatchEvent(new Ae(t,"Add"))}removeAnnotation(e){const t=this.#p.get(e);t?(this.#p.delete(e),this.dispatchEvent(new Ae(t,"Remove"))):console.warn("Overlay for deleted Annotation does not exist")}removeAnnotationOverlay(e){const t=this.#u(e);t?(this.#p.delete(t),this.dispatchEvent(new Ae(e,"Remove"))):console.warn("Annotation for deleted Overlay does not exist")}updateAnnotationOverlay(e){const t=this.#u(e);t?("ENTRY_LABEL"===e.type&&(t.label=e.label),this.dispatchEvent(new Ae(e,"UpdateLabel"))):console.warn("Annotation for updated Overlay does not exist")}#u(e){for(const[t,i]of this.#p.entries())if(i===e)return t;return null}getAnnotations(){return[...this.#p.keys()]}getOverlays(){return[...this.#p.values()]}toJSON(){const e=this.#c.invisibleEntries().map((e=>this.#m.keyForEvent(e))).filter((e=>null!==e)),t=this.#c.expandableEntries().map((e=>this.#m.keyForEvent(e))).filter((e=>null!==e));return this.#h={entriesModifications:{hiddenEntries:e,expandableEntries:t},initialBreadcrumb:this.#d.initialBreadcrumb,annotations:this.#g()},this.#h}#g(){const e=this.getAnnotations(),t=[];for(let i=0;i<e.length;i++)if("ENTRY_LABEL"===e[i].type){const n=this.#m.keyForEvent(e[i].entry);n&&t.push({entry:n,label:e[i].label})}return{entryLabels:t}}applyModificationsIfPresent(){const e=this.#h;if(!e||!e.annotations)return;const t=e.entriesModifications.hiddenEntries,i=e.entriesModifications.expandableEntries;this.#v(t,i),this.#d.setInitialBreadcrumbFromLoadedModifications(e.initialBreadcrumb);e.annotations.entryLabels.forEach((e=>{this.createAnnotation({type:"ENTRY_LABEL",entry:this.#m.eventForKey(e.entry,this.#t),label:e.label})}))}#v(e,t){const i=e.map((e=>this.#m.eventForKey(e,this.#t))),n=t.map((e=>this.#m.eventForKey(e,this.#t)));this.#c.setHiddenAndExpandableEntries(i,n)}}var Be=Object.freeze({__proto__:null,AnnotationModifiedEvent:Ae,ModificationsManager:Ne});function*He(e){if(yield"[\n",e.length>0){const t=e[Symbol.iterator](),i=t.next().value;yield`  ${JSON.stringify(i)}`;let n=1e4,r="";for(const e of t)r+=`,\n  ${JSON.stringify(e)}`,n--,0===n&&(yield r,n=1e4,r="");yield r}yield"\n]"}function*Ue(e,t){yield`{"metadata": ${JSON.stringify(t||{},null,2)}`,yield',\n"traceEvents": ',yield*He(e),yield"}\n"}function We(e){return JSON.stringify(e)}var Oe=Object.freeze({__proto__:null,arrayOfObjectsJsonGenerator:He,traceJsonGenerator:Ue,cpuprofileJsonGenerator:We});class Ve extends Event{static eventName="nodenamesupdated";constructor(){super(Ve.eventName,{composed:!0,bubbles:!0})}}const _e=new Map;class ze extends EventTarget{#T;#f=!1;#y=new Set;constructor(e){super(),this.#T=e}static clearResolvedNodeNames(){_e.clear()}static resolvedNodeNameForEntry(e){return _e.get(e.pid)?.get(e.tid)?.get(e.nodeId)??null}static storeResolvedNodeNameForEntry(e,t,i,n){const r=_e.get(e)||new Map,a=r.get(t)||new Map;a.set(i,n),r.set(t,a),_e.set(e,r)}async install(){if(this.#T.Samples){for(const e of this.#T.Samples.profilesInProcess.values())for(const[t,i]of e){const e=i.parsedProfile.nodes();if(!e||0===e.length)continue;const r=this.#w(t),a=r?.model(n.DebuggerModel.DebuggerModel);if(a)for(const t of e){const e=a.scriptForId(String(t.callFrame.scriptId));(!e||e.sourceMapURL)&&this.#y.add(a)}}for(const e of this.#y)e.sourceMapManager().addEventListener(n.SourceMapManager.Events.SourceMapAttached,this.#b,this);await this.#S()}}uninstall(){for(const e of this.#y)e.sourceMapManager().removeEventListener(n.SourceMapManager.Events.SourceMapAttached,this.#b,this);this.#y.clear()}async#S(){if(this.#T.Samples){for(const[e,t]of this.#T.Samples.profilesInProcess)for(const[i,n]of t){const t=n.parsedProfile.nodes()??[],r=this.#w(i);if(r)for(const n of t){const t=await y.NamesResolver.resolveProfileFrameFunctionName(n.callFrame,r);n.setFunctionName(t),ze.storeResolvedNodeNameForEntry(e,i,n.id,t)}}this.dispatchEvent(new Ve)}}#b(){this.#f||(this.#f=!0,setTimeout((async()=>{this.#f=!1,await this.#S()}),500))}#w(e){const t=this.#T.Workers.workerIdByThread.get(e);return t?n.TargetManager.TargetManager.instance().targetById(t):n.TargetManager.TargetManager.instance().primaryPageTarget()}}var Ge=Object.freeze({__proto__:null,NodeNamesUpdated:Ve,SourceMapsResolver:ze});const je={tracingNotSupported:"Performance trace recording not supported for this type of target"},qe=e.i18n.registerUIStrings("panels/timeline/TimelineController.ts",je),$e=e.i18n.getLocalizedString.bind(void 0,qe);class Je{primaryPageTarget;rootTarget;tracingManager;#C=[];#E=null;client;tracingCompleteCallback;constructor(e,t,n){this.primaryPageTarget=t,this.rootTarget=e,this.tracingManager=e.model(i.TracingManager.TracingManager),this.client=n}async dispose(){this.tracingManager&&await this.tracingManager.reset()}async startRecording(e){function t(e){return"disabled-by-default-"+e}const r=[a.Runtime.experiments.isEnabled("timeline-show-all-events")?"*":"-*",i.Types.TraceEvents.Categories.Console,i.Types.TraceEvents.Categories.Loading,i.Types.TraceEvents.Categories.UserTiming,"devtools.timeline",t("devtools.timeline"),t("devtools.timeline.frame"),t("devtools.timeline.stack"),t("v8.compile"),t("v8.cpu_profiler.hires"),t("lighthouse"),"v8.execute","v8","cppgc","navigation,rail"];a.Runtime.experiments.isEnabled("timeline-v8-runtime-call-stats")&&e.enableJSSampling&&r.push(t("v8.runtime_stats_sampling")),e.enableJSSampling&&r.push(t("v8.cpu_profiler")),a.Runtime.experiments.isEnabled("timeline-invalidation-tracking")&&r.push(t("devtools.timeline.invalidationTracking")),e.capturePictures&&r.push(t("devtools.timeline.layers"),t("devtools.timeline.picture"),t("blink.graphics_context_annotations")),e.captureFilmStrip&&r.push(t("devtools.screenshot")),e.captureSelectorStats&&r.push(t("blink.debug")),a.Runtime.experiments.isEnabled("timeline-enhanced-traces")&&(r.push(t("devtools.target-rundown")),r.push(t("devtools.v8-source-rundown"))),a.Runtime.experiments.isEnabled("timeline-compiled-sources")&&r.push(t("devtools.v8-source-rundown-sources")),this.#E=Date.now();const s=await this.startRecordingWithCategories(r.join(","));return s.getError()&&(await this.waitForTracingToStop(!1),await n.TargetManager.TargetManager.instance().resumeAllTargets()),s}async stopRecording(){this.tracingManager&&this.tracingManager.stop(),this.client.loadingStarted(),await this.waitForTracingToStop(!0),await this.allSourcesFinished()}async waitForTracingToStop(e){const t=[];this.tracingManager&&e&&t.push(new Promise((e=>{this.tracingCompleteCallback=e}))),await Promise.all(t)}async startRecordingWithCategories(e){if(!this.tracingManager)throw new Error($e(je.tracingNotSupported));await n.TargetManager.TargetManager.instance().suspendAllTargets("performance-timeline");const t=await this.tracingManager.start(this,e,"");return await this.warmupJsProfiler(),w.ExtensionServer.ExtensionServer.instance().profilingStarted(),t}async warmupJsProfiler(){const e=this.primaryPageTarget.model(n.RuntimeModel.RuntimeModel);e&&await e.agent.invoke_evaluate({expression:"(async function(){ await 1; })()",throwOnSideEffect:!0})}traceEventsCollected(e){this.#C.push(...e)}tracingComplete(){this.tracingCompleteCallback&&(this.tracingCompleteCallback(void 0),this.tracingCompleteCallback=null)}async allSourcesFinished(){this.client.processingStarted(),await this.finalizeTrace()}async finalizeTrace(){await n.TargetManager.TargetManager.instance().resumeAllTargets(),w.ExtensionServer.ExtensionServer.instance().profilingStopped(),await this.client.loadingComplete(this.#C,null,!1,this.#E,null),this.client.loadingCompleteForTest()}tracingBufferUsage(e){this.client.recordingProgress(e)}eventsRetrievalProgress(e){this.client.loadingProgress(e)}}var Ke=Object.freeze({__proto__:null,TimelineController:Je});const Ye={jsHeap:"JS Heap",documents:"Documents",nodes:"Nodes",listeners:"Listeners",gpuMemory:"GPU Memory",ss:"[{PH1} – {PH2}]"},Xe=e.i18n.registerUIStrings("panels/timeline/CountersGraph.ts",Ye),Ze=e.i18n.getLocalizedString.bind(void 0,Xe);class Qe extends u.Widget.VBox{delegate;calculator;header;toolbar;graphsContainer;canvasContainer;canvas;timelineGrid;counters;counterUI;countersByName;gpuMemoryCounter;#k=null;currentValuesBar;markerXPosition;#x=this.#P.bind(this);constructor(e){super(),this.element.id="memory-graphs-container",this.delegate=e,this.calculator=new it,this.header=new u.Widget.HBox,this.header.element.classList.add("timeline-memory-header"),this.header.show(this.element),this.toolbar=new u.Toolbar.Toolbar("timeline-memory-toolbar"),this.header.element.appendChild(this.toolbar.element),this.graphsContainer=new u.Widget.VBox,this.graphsContainer.show(this.element);const t=new u.Widget.VBoxWithResizeCallback(this.resize.bind(this));t.show(this.graphsContainer.element),this.createCurrentValuesBar(),this.canvasContainer=t.element,this.canvasContainer.id="memory-graphs-canvas-container",this.canvas=document.createElement("canvas"),this.canvasContainer.appendChild(this.canvas),this.canvas.id="memory-counters-graph",this.canvasContainer.addEventListener("mouseover",this.onMouseMove.bind(this),!0),this.canvasContainer.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.canvasContainer.addEventListener("mouseleave",this.onMouseLeave.bind(this),!0),this.canvasContainer.addEventListener("click",this.onClick.bind(this),!0),this.timelineGrid=new p.TimelineGrid.TimelineGrid,this.canvasContainer.appendChild(this.timelineGrid.dividersElement),this.counters=[],this.counterUI=[],this.countersByName=new Map,this.countersByName.set("jsHeapSizeUsed",this.createCounter(Ze(Ye.jsHeap),"js-heap-size-used","hsl(220, 90%, 43%)",c.NumberUtilities.bytesToString)),this.countersByName.set("documents",this.createCounter(Ze(Ye.documents),"documents","hsl(0, 90%, 43%)")),this.countersByName.set("nodes",this.createCounter(Ze(Ye.nodes),"nodes","hsl(120, 90%, 43%)")),this.countersByName.set("jsEventListeners",this.createCounter(Ze(Ye.listeners),"js-event-listeners","hsl(38, 90%, 43%)")),this.gpuMemoryCounter=this.createCounter(Ze(Ye.gpuMemory),"gpu-memory-used-kb","hsl(300, 90%, 43%)",c.NumberUtilities.bytesToString),this.countersByName.set("gpuMemoryUsedKB",this.gpuMemoryCounter),h.TraceBounds.onChange(this.#x)}#P(e){if("RESET"===e.updateType||"VISIBLE_WINDOW"===e.updateType){const t=e.state.milli.timelineTraceWindow;this.calculator.setWindow(t.min,t.max),this.#I()}}setModel(e,t){if(this.#k=t,!t)return;const n=e?i.Helpers.Timing.traceWindowMilliSeconds(e.Meta.traceBounds).min:0;this.calculator.setZeroTime(n);for(let e=0;e<this.counters.length;++e)this.counters[e].reset(),this.counterUI[e].reset();this.#I();for(let e=0;e<t.length;++e){const n=t[e];if(!i.Types.TraceEvents.isTraceEventUpdateCounters(n))continue;const r=n.args.data;if(!r)return;for(const e in r){const t=this.countersByName.get(e);if(t){const{startTime:a}=i.Helpers.Timing.eventTimingsMilliSeconds(n);t.appendSample(a,r[e])}}void 0!==r.gpuMemoryLimitKB&&this.gpuMemoryCounter.setLimit(r.gpuMemoryLimitKB)}}createCurrentValuesBar(){this.currentValuesBar=this.graphsContainer.element.createChild("div"),this.currentValuesBar.id="counter-values-bar"}createCounter(e,t,i,n){const r=new et;return this.counters.push(r),this.counterUI.push(new tt(this,e,t,i,r,n)),r}resizerElement(){return this.header.element}resize(){const e=this.canvas.parentElement;this.canvas.width=e.clientWidth*window.devicePixelRatio,this.canvas.height=e.clientHeight*window.devicePixelRatio,this.calculator.setDisplayWidth(this.canvas.width),this.refresh()}#I(){u.UIUtils.invokeOnceAfterBatchUpdate(this,this.refresh)}draw(){this.clear();for(const e of this.counters)e.calculateVisibleIndexes(this.calculator),e.calculateXValues(this.canvas.width);for(const e of this.counterUI)e.drawGraph(this.canvas)}onClick(e){const t=e.x-this.canvasContainer.getBoundingClientRect().left;let i,n=1/0;for(const e of this.counterUI){if(!e.counter.times.length)continue;const r=e.recordIndexAt(t),a=Math.abs(t*window.devicePixelRatio-e.counter.x[r]);a<n&&(n=a,i=e.counter.times[r])}void 0!==i&&this.#k&&this.delegate.selectEntryAtTime(this.#k,i)}onMouseLeave(e){delete this.markerXPosition,this.clearCurrentValueAndMarker()}clearCurrentValueAndMarker(){for(let e=0;e<this.counterUI.length;e++)this.counterUI[e].clearCurrentValueAndMarker()}onMouseMove(e){const t=e.x-this.canvasContainer.getBoundingClientRect().left;this.markerXPosition=t,this.refreshCurrentValues()}refreshCurrentValues(){if(void 0!==this.markerXPosition)for(let e=0;e<this.counterUI.length;++e)this.counterUI[e].updateCurrentValue(this.markerXPosition)}refresh(){this.timelineGrid.updateDividers(this.calculator),this.draw(),this.refreshCurrentValues()}clear(){const e=this.canvas.getContext("2d");if(!e)throw new Error("Unable to get canvas context");e.clearRect(0,0,e.canvas.width,e.canvas.height)}}class et{times;values;x;minimumIndex;maximumIndex;maxTime;minTime;limitValue;constructor(){this.times=[],this.values=[],this.x=[],this.minimumIndex=0,this.maximumIndex=0,this.maxTime=0,this.minTime=0}appendSample(e,t){this.values.length&&this.values[this.values.length-1]===t||(this.times.push(e),this.values.push(t))}reset(){this.times=[],this.values=[]}setLimit(e){this.limitValue=e}calculateBounds(){let e,t;for(let i=this.minimumIndex;i<=this.maximumIndex;i++){const n=this.values[i];(void 0===t||n<t)&&(t=n),(void 0===e||n>e)&&(e=n)}return t=t||0,e=e||1,this.limitValue&&(e>.5*this.limitValue&&(e=Math.max(e,this.limitValue)),t=Math.min(t,this.limitValue)),{min:t,max:e}}calculateVisibleIndexes(e){const t=e.minimumBoundary(),i=e.maximumBoundary();this.minimumIndex=c.NumberUtilities.clamp(c.ArrayUtilities.upperBound(this.times,t,c.ArrayUtilities.DEFAULT_COMPARATOR)-1,0,this.times.length-1),this.maximumIndex=c.NumberUtilities.clamp(c.ArrayUtilities.lowerBound(this.times,i,c.ArrayUtilities.DEFAULT_COMPARATOR),0,this.times.length-1),this.minTime=t,this.maxTime=i}calculateXValues(e){if(!this.values.length)return;const t=e/(this.maxTime-this.minTime);this.x=new Array(this.values.length);for(let e=this.minimumIndex+1;e<=this.maximumIndex;e++)this.x[e]=t*(this.times[e]-this.minTime)}}class tt{countersPane;counter;formatter;setting;filter;range;value;graphColor;limitColor;graphYValues;verticalPadding;currentValueLabel;marker;constructor(e,t,i,n,a,s){this.countersPane=e,this.counter=a,this.formatter=s||c.NumberUtilities.withThousandsSeparator,this.setting=r.Settings.Settings.instance().createSetting("timeline-counters-graph-"+i,!0),this.setting.setTitle(t),this.filter=new u.Toolbar.ToolbarSettingCheckbox(this.setting,t),this.filter.inputElement.classList.add("-theme-preserve-input");const o=r.Color.parse(n);if(o){const e=o.setAlpha(.5).asString("rgba"),t=this.filter.element;e&&(t.style.backgroundColor=e),t.style.borderColor="transparent"}this.filter.inputElement.addEventListener("click",this.toggleCounterGraph.bind(this)),e.toolbar.appendToolbarItem(this.filter),this.range=this.filter.element.createChild("span","range"),this.value=e.currentValuesBar.createChild("span","memory-counter-value"),this.value.style.color=n,this.graphColor=n,o&&(this.limitColor=o.setAlpha(.3).asString("rgba")),this.graphYValues=[],this.verticalPadding=10,this.currentValueLabel=t,this.marker=e.canvasContainer.createChild("div","memory-counter-marker"),this.marker.style.backgroundColor=n,this.clearCurrentValueAndMarker()}reset(){this.range.textContent=""}setRange(e,t){const i=this.formatter(e),n=this.formatter(t);this.range.textContent=Ze(Ye.ss,{PH1:i,PH2:n})}toggleCounterGraph(){this.value.classList.toggle("hidden",!this.filter.checked()),this.countersPane.refresh()}recordIndexAt(e){return c.ArrayUtilities.upperBound(this.counter.x,e*window.devicePixelRatio,c.ArrayUtilities.DEFAULT_COMPARATOR,this.counter.minimumIndex+1,this.counter.maximumIndex+1)-1}updateCurrentValue(e){if(!this.visible()||!this.counter.values.length||!this.counter.x)return;const t=this.recordIndexAt(e),i=c.NumberUtilities.withThousandsSeparator(this.counter.values[t]);this.value.textContent=`${this.currentValueLabel}: ${i}`;const n=this.graphYValues[t]/window.devicePixelRatio;this.marker.style.left=e+"px",this.marker.style.top=n+"px",this.marker.classList.remove("hidden")}clearCurrentValueAndMarker(){this.value.textContent="",this.marker.classList.add("hidden")}drawGraph(e){const t=e.getContext("2d");if(!t)throw new Error("Unable to get canvas context");const i=e.width,n=e.height-2*this.verticalPadding;if(n<=0)return void(this.graphYValues=[]);const r=this.verticalPadding,a=this.counter,s=a.values;if(!s.length)return;const o=a.calculateBounds(),l=o.min,c=o.max;if(this.setRange(l,c),!this.visible())return;const d=this.graphYValues,h=c-l,m=h?n/h:1;t.save(),t.lineWidth=window.devicePixelRatio,t.lineWidth%2&&t.translate(.5,.5),t.beginPath();let p=s[a.minimumIndex],u=Math.round(r+n-(p-l)*m);t.moveTo(0,u);let g=a.minimumIndex;for(;g<=a.maximumIndex;g++){const e=Math.round(a.x[g]);t.lineTo(e,u);const i=s[g];void 0!==i&&(p=i),u=Math.round(r+n-(p-l)*m),t.lineTo(e,u),d[g]=u}if(d.length=g,t.lineTo(i,u),t.strokeStyle=this.graphColor,t.stroke(),a.limitValue){const e=Math.round(r+n-(a.limitValue-l)*m);t.moveTo(0,e),t.lineTo(i,e),this.limitColor&&(t.strokeStyle=this.limitColor),t.stroke()}t.closePath(),t.restore()}visible(){return this.filter.checked()}}class it{minimumBoundaryInternal;maximumBoundaryInternal;workingArea;zeroTimeInternal;constructor(){this.minimumBoundaryInternal=0,this.maximumBoundaryInternal=0,this.workingArea=0,this.zeroTimeInternal=0}setZeroTime(e){this.zeroTimeInternal=e}computePosition(e){return(e-this.minimumBoundaryInternal)/this.boundarySpan()*this.workingArea}setWindow(e,t){this.minimumBoundaryInternal=e,this.maximumBoundaryInternal=t}setDisplayWidth(e){this.workingArea=e}formatValue(t,i){return e.TimeUtilities.preciseMillisToString(t-this.zeroTime(),i)}maximumBoundary(){return this.maximumBoundaryInternal}minimumBoundary(){return this.minimumBoundaryInternal}zeroTime(){return this.zeroTimeInternal}boundarySpan(){return this.maximumBoundaryInternal-this.minimumBoundaryInternal}}var nt=Object.freeze({__proto__:null,CountersGraph:Qe,Counter:et,CounterUI:tt,Calculator:it});function rt(e,t){const i=n.TargetManager.TargetManager.instance(),r=e.Workers.workerIdByThread.get(t.tid);return r?i.targetById(r):i.primaryPageTarget()}var at=Object.freeze({__proto__:null,targetForEvent:rt});const st=new CSSStyleSheet;st.replaceSync(".token-variable{color:var(--sys-color-token-variable)}.token-property{color:var(--sys-color-token-property)}.token-type{color:var(--sys-color-token-type)}.token-variable-special{color:var(--sys-color-token-variable-special)}.token-definition{color:var(--sys-color-token-definition)}.token-builtin{color:var(--sys-color-token-builtin)}.token-number{color:var(--sys-color-token-number)}.token-string{color:var(--sys-color-token-string)}.token-string-special{color:var(--sys-color-token-string-special)}.token-atom{color:var(--sys-color-token-atom)}.token-keyword{color:var(--sys-color-token-keyword)}.token-comment{color:var(--sys-color-token-comment)}.token-meta{color:var(--sys-color-token-meta)}.token-invalid{color:var(--sys-color-error)}.token-tag{color:var(--sys-color-token-tag)}.token-attribute{color:var(--sys-color-token-attribute)}.token-attribute-value{color:var(--sys-color-token-attribute-value)}.token-inserted{color:var(--sys-color-token-inserted)}.token-deleted{color:var(--sys-color-token-deleted)}.token-heading{color:var(--sys-color-token-variable-special);font-weight:bold}.token-link{color:var(--sys-color-token-variable-special);text-decoration:underline}.token-strikethrough{text-decoration:strike-through}.token-strong{font-weight:bold}.token-emphasis{font-style:italic}\n/*# sourceURL=codeHighlighter.css */\n");const ot=new CSSStyleSheet;ot.replaceSync(".image-preview-container{background:transparent;text-align:center;border-spacing:0}.image-preview-container img{margin:6px 0;max-width:100px;max-height:100px;background-image:var(--image-file-checker);user-select:text;vertical-align:top;-webkit-user-drag:auto}.image-container{padding:0}.image-container > div{min-height:50px;display:flex;align-items:center;justify-content:center;cursor:pointer}.image-preview-container .row{line-height:1.2;vertical-align:baseline}.image-preview-container .title{padding-right:0.5em;text-align:right;color:var(--sys-color-token-subtle);white-space:nowrap}.image-preview-container .description{white-space:nowrap;text-align:left;color:var(--sys-color-on-surface)}.image-preview-container .description-link{max-width:20em}.image-preview-container .source-link{white-space:normal;word-break:break-all;color:var(--sys-color-primary);cursor:pointer}\n/*# sourceURL=imagePreview.css */\n");const lt=new CSSStyleSheet;lt.replaceSync('*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;--legacy-accent-color:#1a73e8;--legacy-accent-fg-color:#1a73e8;--legacy-accent-color-hover:#3b86e8;--legacy-accent-fg-color-hover:#1567d3;--legacy-active-control-bg-color:#5a5a5a;--legacy-focus-bg-color:hsl(214deg 40% 92%);--legacy-focus-ring-inactive-shadow-color:#e0e0e0;--legacy-input-validation-error:#db1600;--legacy-toolbar-hover-bg-color:#eaeaea;--legacy-selection-fg-color:#fff;--legacy-selection-bg-color:var(--legacy-accent-color);--legacy-selection-inactive-fg-color:#5a5a5a;--legacy-selection-inactive-bg-color:#dadada;--legacy-divider-border:1px solid var(--sys-color-divider);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-focus-ring-active-shadow:0 0 0 1px var(--legacy-accent-color);--legacy-item-selection-bg-color:#cfe8fc;--legacy-item-selection-inactive-bg-color:#e0e0e0;--monospace-font-size:10px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace;--sys-motion-duration-short4:200ms;--sys-motion-duration-medium2:300ms;--sys-motion-duration-long2:500ms;--sys-motion-easing-emphasized:cubic-bezier(0.2,0,0,1);--sys-motion-easing-emphasized-decelerate:cubic-bezier(0.05,0.7,0.1,1);--sys-motion-easing-emphasized-accelerate:cubic-bezier(0.2,0,0,1)}.theme-with-dark-background{color-scheme:dark;--legacy-accent-color:#0e639c;--legacy-accent-fg-color:#ccc;--legacy-accent-fg-color-hover:#fff;--legacy-accent-color-hover:rgb(17 119 187);--legacy-active-control-bg-color:#cdcdcd;--legacy-focus-bg-color:hsl(214deg 19% 27%);--legacy-focus-ring-inactive-shadow-color:#5a5a5a;--legacy-toolbar-hover-bg-color:#202020;--legacy-selection-fg-color:#cdcdcd;--legacy-selection-inactive-fg-color:#cdcdcd;--legacy-selection-inactive-bg-color:hsl(0deg 0% 28%);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-item-selection-bg-color:hsl(207deg 88% 22%);--legacy-item-selection-inactive-bg-color:#454545}body{--default-font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:var(--default-font-family);font-size:12px;tab-size:4;user-select:none;color:var(--sys-color-on-surface);background:var(--sys-color-cdt-base-container)}.platform-linux{--default-font-family:"Google Sans Text","Google Sans",system-ui,sans-serif}.platform-mac{--default-font-family:system-ui,sans-serif}.platform-windows{--default-font-family:system-ui,sans-serif}:focus{outline-width:0}.platform-mac,\n:host-context(.platform-mac){--monospace-font-size:11px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace}.platform-windows,\n:host-context(.platform-windows){--monospace-font-size:12px;--monospace-font-family:monospace;--source-code-font-size:12px;--source-code-font-family:monospace}.platform-linux,\n:host-context(.platform-linux){--monospace-font-size:11px;--monospace-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace;--source-code-font-size:11px;--source-code-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)!important}.source-code{font-family:var(--source-code-font-family);font-size:var(--source-code-font-size)!important;white-space:pre-wrap}.source-code .devtools-link.text-button{max-width:100%;overflow:hidden;text-overflow:ellipsis}img{-webkit-user-drag:none}iframe,\na img{border:none}.fill{position:absolute;top:0;left:0;right:0;bottom:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > .toolbar{border-bottom:1px solid var(--sys-color-divider)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--sys-color-cdt-base-container)}iframe.widget{position:absolute;width:100%;height:100%;left:0;right:0;top:0;bottom:0}.hidden{display:none!important}.highlighted-search-result{border-radius:1px;background-color:var(--sys-color-yellow-container);outline:1px solid var(--sys-color-yellow-container)}.link{cursor:pointer;text-decoration:underline;color:var(--sys-color-primary);outline-offset:2px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}select option,\nselect optgroup,\ninput{background-color:var(--sys-color-cdt-base-container)}input{color:inherit;&[type="checkbox"]{position:relative;&:hover::after,\n    &:active::before{content:"";height:24px;width:24px;border-radius:var(--sys-shape-corner-full);position:absolute;top:-6px;left:-6px}&:not(.-theme-preserve){accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}&:not(:disabled):hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:not(:disabled):active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:not(:disabled):focus-visible{outline:none;&::before{content:"";height:15px;width:15px;border-radius:5px;position:absolute;top:-3.5px;left:-3.5px;border:2px solid var(--sys-color-state-focus-ring)}}&.small:hover::after,\n    &.small:active::before{height:12px;width:12px;top:0;left:0;border-radius:2px}}}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.theme-with-dark-background input::placeholder,\n:host-context(.theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}.harmony-input:not([type]),\n.harmony-input[type="number"],\n.harmony-input[type="text"]{padding:3px 6px;height:24px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;&.error-input,\n  &:invalid{border-color:var(--sys-color-error)}&:not(.error-input):not(:invalid):focus{border-color:var(--sys-color-state-focus-ring)}&:not(.error-input):not(:invalid):hover:not(:focus){background:var(--sys-color-state-hover-on-subtle)}}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(--sys-color-on-surface)!important;text-decoration:none!important}.chrome-select{appearance:none;user-select:none;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;color:var(--sys-color-on-surface);font:inherit;margin:0;outline:none;padding-right:20px;padding-left:6px;background-image:var(--image-file-arrow-drop-down-light);background-color:var(--sys-color-surface);background-position:right center;background-repeat:no-repeat;min-height:24px;min-width:80px}.chrome-select:disabled{opacity:38%}.theme-with-dark-background .chrome-select,\n:host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.chrome-select:enabled{&:hover{background-color:var(--sys-color-state-hover-on-subtle)}&:active{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}}@media (forced-colors: active) and (prefers-color-scheme: light){.chrome-select{background-image:var(--image-file-arrow-drop-down-light)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){.chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}}.chrome-select-label{margin:0 22px;flex:none}.chrome-select-label p p{margin-top:0;color:var(--sys-color-token-subtle)}.settings-select{margin:0}.chrome-select optgroup,\n.chrome-select option{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--sys-color-token-subtle);white-space:nowrap}span[is="dt-icon-label"]{flex:none}.full-widget-dimmed-banner a{color:inherit}.full-widget-dimmed-banner{color:var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;text-align:center;padding:20px;position:absolute;top:0;right:0;bottom:0;left:0;font-size:13px;overflow:auto;z-index:500}.dot::before{content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--sys-color-green-bright)}.purple::before{background-color:var(--sys-color-purple-bright)}.expandable-inline-button{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);cursor:pointer;border-radius:3px}.undisplayable-text,\n.expandable-inline-button{border:none;padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,\n.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--sys-color-state-disabled);font-style:italic}.expandable-inline-button:hover,\n.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-focus-highlight)}::selection{background-color:var(--sys-color-tonal-container)}.reload-warning{align-self:center;margin-left:10px}button.link{border:none;background:none;padding:3px}button.link:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:var(--sys-shape-corner-full)}.theme-with-dark-background button.link:focus-visible,\n:host-context(.theme-with-dark-background) button.link:focus-visible{--override-link-focus-background-color:rgb(230 230 230/8%)}@media (forced-colors: active){.dimmed,\n  .chrome-select:disabled{opacity:100%}.harmony-input:not([type]),\n  .harmony-input[type="number"],\n  .harmony-input[type="text"]{border:1px solid ButtonText}.harmony-input:not([type]):focus,\n  .harmony-input[type="number"]:focus,\n  .harmony-input[type="text"]:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;width:16px;height:15px;margin-right:0;opacity:70%;mask-image:var(--image-file-cross-circle-filled);mask-position:center;mask-repeat:no-repeat;mask-size:99%;background-color:var(--icon-default)}input.custom-search-input::-webkit-search-cancel-button:hover{opacity:99%}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--sys-color-token-subtle));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:"";position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-flex;vertical-align:middle}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.theme-with-dark-background) devtools-adorner{--override-adorner-border-color:var(--sys-color-tonal-outline);--override-adorner-focus-border-color:var(--sys-color-state-focus-ring);--override-adorner-active-background-color:var(--sys-color-state-riple-neutral-on-subtle)}.panel{display:flex;overflow:hidden;position:absolute;top:0;left:0;right:0;bottom:0;z-index:0;background-color:var(--sys-color-cdt-base-container)}.panel-sidebar{overflow-x:hidden;background-color:var(--sys-color-cdt-base-container)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}\n/*# sourceURL=inspectorCommon.css */\n');const ct={interactions:"Interactions"},dt=e.i18n.registerUIStrings("panels/timeline/InteractionsTrackAppender.ts",ct),ht=e.i18n.getLocalizedString.bind(void 0,dt);class mt{appenderName="Interactions";#M;#e;#t;constructor(e,t,i){this.#e=e,this.#M=i,this.#t=t}appendTrackAtLevel(e,t){return 0===this.#t.UserInteractions.interactionEvents.length?e:(this.#i(e,t),this.#F(e))}#i(e,t){const i=U({collapsible:this.#t.UserInteractions.interactionEvents.length>0,useDecoratorsForOverview:!0}),n=W("interactions",e,ht(ct.interactions),i,!0,t);this.#e.registerTrackForGroup(n,this)}#F(e){const{interactionEventsWithNoNesting:t,interactionsOverThreshold:i}=this.#t.UserInteractions;return this.#e.appendEventsAtLevel(t,e,this,((e,t)=>{i.has(e)&&void 0!==t&&this.#L(e,t)}))}#L(e,t){const n=this.#e.getFlameChartTimelineData().entryDecorations[t]||[];n.push({type:"CANDY",startAtTime:i.Handlers.ModelHandlers.UserInteractions.LONG_INTERACTION_THRESHOLD,endAtTime:e.processingEnd},{type:"WARNING_TRIANGLE",customEndTime:e.processingEnd}),this.#e.getFlameChartTimelineData().entryDecorations[t]=n}colorForEvent(e){let t=this.titleForEvent(e);return i.Types.TraceEvents.isSyntheticInteractionEvent(e)&&(t+=e.interactionId),this.#M.colorForID(t)}titleForEvent(e){return i.Types.TraceEvents.isSyntheticInteractionEvent(e)?pt(e):e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:O(e.dur)}}}function pt(e){const t=i.Handlers.ModelHandlers.UserInteractions.categoryOfInteraction(e);return"OTHER"===t?"Other":"KEYBOARD"===t?"Keyboard":"POINTER"===t?"Pointer":e.type}var ut=Object.freeze({__proto__:null,InteractionsTrackAppender:mt,titleForInteractionEvent:pt});const gt=Symbol("SelectionRange");class vt{startTime;endTime;object;constructor(e,t,i){this.startTime=e,this.endTime=t,this.object=i}static isFrameObject(e){return e instanceof i.Handlers.ModelHandlers.Frames.TimelineFrame}static fromFrame(e){return new vt(i.Helpers.Timing.microSecondsToMilliseconds(e.startTime),i.Helpers.Timing.microSecondsToMilliseconds(e.endTime),e)}static isSyntheticNetworkRequestDetailsEventSelection(e){return!vt.isFrameObject(e)&&!vt.isRangeSelection(e)&&i.Types.TraceEvents.isSyntheticNetworkRequestEvent(e)}static isNetworkEventSelection(e){return!vt.isFrameObject(e)&&!vt.isRangeSelection(e)&&i.Types.TraceEvents.isNetworkTrackEntry(e)}static isTraceEventSelection(e){return!vt.isFrameObject(e)&&!vt.isRangeSelection(e)&&!i.Types.TraceEvents.isSyntheticNetworkRequestEvent(e)}static fromTraceEvent(e){const{startTime:t,endTime:n}=i.Helpers.Timing.eventTimingsMilliSeconds(e);return new vt(t,i.Types.Timing.MilliSeconds(n||t+1),e)}static isRangeSelection(e){return e===gt}static fromRange(e,t){return new vt(i.Types.Timing.MilliSeconds(e),i.Types.Timing.MilliSeconds(t),gt)}}var Tt=Object.freeze({__proto__:null,TimelineSelection:vt});const ft={emptyPlaceholder:"{PH1}",timestamp:"Timestamp",interactionID:"ID",inputDelay:"Input delay",processingDuration:"Processing duration",presentationDelay:"Presentation delay",compile:"Compile",parse:"Parse",sS:"{PH1}: {PH2}",sCollected:"{PH1} collected",sSs:"{PH1} [{PH2}…{PH3}]",sSSquareBrackets:"{PH1} [{PH2}…]",learnMore:"Learn more",compilationCacheStatus:"Compilation cache status",compilationCacheSize:"Compilation cache size",compilationCacheKind:"Compilation cache kind",scriptLoadedFromCache:"script loaded from cache",failedToLoadScriptFromCache:"failed to load script from cache",scriptNotEligibleToBeLoadedFromCache:"script not eligible",totalTime:"Total Time",selfTime:"Self Time",collected:"Collected",function:"Function",timerId:"Timer ID",timeout:"Timeout",repeats:"Repeats",callbackId:"Callback ID",module:"Module",script:"Script",streamed:"Streamed",eagerCompile:"Compiling all functions eagerly",url:"Url",producedCacheSize:"Produced Cache Size",consumedCacheSize:"Consumed Cache Size",location:"Location",sSCurlyBrackets:"({PH1}, {PH2})",dimensions:"Dimensions",sSDimensions:"{PH1} × {PH2}",layerRoot:"Layer Root",ownerElement:"Owner Element",imageUrl:"Image URL",stylesheetUrl:"Stylesheet URL",elementsAffected:"Elements Affected",nodesThatNeedLayout:"Nodes That Need Layout",sOfS:"{PH1} of {PH2}",layoutRoot:"Layout root",message:"Message",callbackFunction:"Callback Function",state:"State",range:"Range",allottedTime:"Allotted Time",invokedByTimeout:"Invoked by Timeout",type:"Type",size:"Size",details:"Details",cumulativeLayoutShifts:"Cumulative Layout Shifts",evolvedClsLink:"evolved",sCLSInformation:"{PH1} can result in poor user experiences. It has recently {PH2}.",warning:"Warning",score:"Score",cumulativeScore:"Cumulative Score",currentClusterScore:"Current Cluster Score",currentClusterId:"Current Cluster ID",hadRecentInput:"Had recent input",yes:"Yes",no:"No",movedFrom:"Moved from",movedTo:"Moved to",relatedNode:"Related Node",preview:"Preview",aggregatedTime:"Aggregated Time",duration:"Duration",initiatorStackTrace:"Initiator Stack Trace",initiatedBy:"Initiated by",initiatorFor:"Initiator for",traceEvent:"Trace Event",timerInstalled:"Timer Installed",animationFrameRequested:"Animation Frame Requested",idleCallbackRequested:"Idle Callback Requested",recalculationForced:"Recalculation Forced",firstLayoutInvalidation:"First Layout Invalidation",layoutForced:"Layout Forced",stackTrace:"Stack Trace",invalidations:"Invalidations",pendingFor:"Pending for",firstInvalidated:"First Invalidated",paintProfiler:"Paint Profiler",sAtS:"{PH1} at {PH2}",sSelf:"{PH1} (self)",sChildren:"{PH1} (children)",timeSpentInRendering:"Time spent in rendering",frame:"Frame",sAtSParentheses:"{PH1} (at {PH2})",UnknownNode:"[ unknown node ]",invalidationWithCallFrame:"{PH1} at {PH2}",outsideBreadcrumbRange:"(outside of the breadcrumb range)",entryIsHidden:"(entry is hidden)",selectorStatsTitle:"Selector Stats",sSelectorStatsInfo:'Select "{PH1}" to collect detailed CSS selector matching statistics.'},yt=e.i18n.registerUIStrings("panels/timeline/TimelineUIUtils.ts",ft),wt=e.i18n.getLocalizedString.bind(void 0,yt);let bt,St;class Ct{static frameDisplayName(e){if(!C.TimelineJSProfile.TimelineJSProfileProcessor.isNativeRuntimeFrame(e))return u.UIUtils.beautifyFunctionName(e.functionName);switch(C.TimelineJSProfile.TimelineJSProfileProcessor.nativeGroup(e.functionName)){case"Compile":return wt(ft.compile);case"Parse":return wt(ft.parse)}return e.functionName}static testContentMatching(e,t,n){const r=[Ct.eventStyle(e).title];if(i.Types.TraceEvents.isProfileCall(e)&&(n&&n.Samples?r.push(i.Handlers.ModelHandlers.Samples.getProfileCallFunctionName(n.Samples,e)):r.push(e.callFrame.functionName)),n){const t=i.Extras.URLForEntry.get(n,e);t&&r.push(t)}!function e(t,i){if(!i)return;for(const n in t){const a=t[n];"string"==typeof a?r.push(a):"number"==typeof a?r.push(String(a)):"object"==typeof a&&null!==a&&e(a,i-1)}}(e.args,2);const a=r.join("|").match(t);return!!a&&a.length>0}static eventStyle(e){if(i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.Console)||i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.UserTiming))return new se(e.name,me().scripting);if(i.Types.TraceEvents.isProfileCall(e)&&"(idle)"===e.callFrame.functionName)return new se(e.name,me().idle);const t=new se(e.name,me().other);return de(e.name)||t}static eventColor(e){if(i.Types.TraceEvents.isProfileCall(e)){const t=e.callFrame;if(Ct.isUserFrame(t))return Ct.colorForId(t.url)}if(i.Types.Extensions.isSyntheticExtensionEntry(e))return P.ExtensionUI.extensionEntryColor(e);let t=Ct.eventStyle(e).category.getComputedColorValue();if("v8.parseOnBackgroundWaiting"===e.name&&(t=me().scripting.getComputedColorValue(),!t))throw new Error("Unable to parse color from getCategoryStyles().scripting.color");return t}static eventTitle(e){if(i.Types.TraceEvents.isProfileCall(e)){return ze.resolvedNodeNameForEntry(e)||Ct.frameDisplayName(e.callFrame)}if("EventTiming"===e.name&&i.Types.TraceEvents.isSyntheticInteractionEvent(e))return pt(e);const t=Ct.eventStyle(e).title;return i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.Console)?t:i.Types.TraceEvents.isTraceEventTimeStamp(e)?wt(ft.sS,{PH1:t,PH2:e.args.data.message}):i.Types.TraceEvents.isTraceEventAnimation(e)&&e.args.data.name?wt(ft.sS,{PH1:t,PH2:e.args.data.name}):i.Types.TraceEvents.isTraceEventDispatch(e)?wt(ft.sS,{PH1:t,PH2:e.args.data.type}):t}static isUserFrame(e){return"0"!==e.scriptId&&!(e.url&&e.url.startsWith("native "))}static async buildDetailsTextForTraceEvent(e,t){let n;const r=e.args,a=e.args?.data;switch(e.name){case"GCEvent":case"MajorGC":case"MinorGC":{const e=r.usedHeapSizeBefore-r.usedHeapSizeAfter;n=wt(ft.sCollected,{PH1:c.NumberUtilities.bytesToString(e)});break}case"FunctionCall":{const{lineNumber:t,columnNumber:r}=i.Helpers.Trace.getZeroIndexedLineAndColumnForEvent(e);void 0!==t&&void 0!==r&&(n=a.url+":"+(t+1)+":"+(r+1));break}case"EventDispatch":n=a?a.type:null;break;case"Paint":{const e=Ct.quadWidth(a.clip),t=Ct.quadHeight(a.clip);e&&t&&(n=wt(ft.sSDimensions,{PH1:e,PH2:t}));break}case"ParseHTML":{const e=r.beginData.startLine,t=r.endData&&r.endData.endLine,i=b.ResourceUtils.displayNameForURL(r.beginData.url);n=t>=0?wt(ft.sSs,{PH1:i,PH2:e+1,PH3:t+1}):wt(ft.sSSquareBrackets,{PH1:i,PH2:e+1});break}case"V8.CompileModule":case"v8.produceModuleCache":n=b.ResourceUtils.displayNameForURL(r.fileName);break;case"V8.CompileScript":case"v8.produceCache":case"EvaluateScript":{const{lineNumber:t}=i.Helpers.Trace.getZeroIndexedLineAndColumnForEvent(e),r=a&&a.url;r&&(n=b.ResourceUtils.displayNameForURL(r)+":"+((t||0)+1));break}case"v8.wasm.compiledModule":case"v8.wasm.moduleCacheHit":{const e=r.url;e&&(n=b.ResourceUtils.displayNameForURL(e));break}case"v8.parseOnBackground":case"v8.deserializeOnBackground":case"XHRReadyStateChange":case"XHRLoad":{const e=a.url;e&&(n=b.ResourceUtils.displayNameForURL(e));break}case"TimeStamp":n=a.message;break;case"WebSocketCreate":case"WebSocketSendHandshakeRequest":case"WebSocketReceiveHandshakeResponse":case"WebSocketSend":case"WebSocketReceive":case"WebSocketDestroy":case"ResourceWillSendRequest":case"ResourceSendRequest":case"ResourceReceivedData":case"ResourceReceiveResponse":case"ResourceFinish":case"PaintImage":case"Decode Image":case"Decode LazyPixelRef":{const r=i.Extras.URLForEntry.get(t,e);r&&(n=b.ResourceUtils.displayNameForURL(r));break}case"EmbedderCallback":n=a.callbackName;break;case"Animation":n=a&&a.name;break;case"AsyncTask":n=a?a.name:null;break;default:n=i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.Console)?null:function(){const t=i.Helpers.Trace.getZeroIndexedStackTraceForEvent(e)?.at(0)??null;if(!t)return null;return t.url+":"+(t.lineNumber+1)+":"+(t.columnNumber+1)}()}return n}static async buildDetailsNodeForTraceEvent(e,t,n,r=!1,a){let s,o=null;const l=e.args,c=e.args?.data;switch(e.name){case"GCEvent":case"MajorGC":case"MinorGC":case"EventDispatch":case"Paint":case"Animation":case"EmbedderCallback":case"ParseHTML":case"v8.wasm.streamFromResponseCallback":case"v8.wasm.compiledModule":case"v8.wasm.moduleCacheHit":case"v8.wasm.cachedModule":case"v8.wasm.moduleCacheInvalid":case"WebSocketCreate":case"WebSocketSendHandshakeRequest":case"WebSocketReceiveHandshakeResponse":case"WebSocketSend":case"WebSocketReceive":case"WebSocketDestroy":s=await Ct.buildDetailsTextForTraceEvent(e,a);break;case"PaintImage":case"Decode Image":case"Decode LazyPixelRef":case"XHRReadyStateChange":case"XHRLoad":case"ResourceWillSendRequest":case"ResourceSendRequest":case"ResourceReceivedData":case"ResourceReceiveResponse":case"ResourceFinish":{const t=i.Extras.URLForEntry.get(a,e);if(t){const e={tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0};o=E.Linkifier.Linkifier.linkifyURL(t,e)}break}case"FunctionCall":{o=document.createElement("span"),i.Types.TraceEvents.isTraceEventFunctionCall(e)&&e.args.data&&i.Types.TraceEvents.objectIsTraceEventCallFrame(e.args.data)&&u.UIUtils.createTextChild(o,Ct.frameDisplayName({...e.args.data,scriptId:String(e.args.data.scriptId)}));const{lineNumber:a,columnNumber:s}=i.Helpers.Trace.getZeroIndexedLineAndColumnForEvent(e),l=this.linkifyLocation({scriptId:c.scriptId,url:c.url,lineNumber:a||0,columnNumber:s,target:t,isFreshRecording:r,linkifier:n});l&&(u.UIUtils.createTextChild(o," @ "),o.appendChild(l));break}case"V8.CompileModule":case"v8.produceModuleCache":o=this.linkifyLocation({scriptId:null,url:l.fileName,lineNumber:0,columnNumber:0,target:t,isFreshRecording:r,linkifier:n});break;case"V8.CompileScript":case"v8.produceCache":case"EvaluateScript":{const a=c.url;if(a){const{lineNumber:s}=i.Helpers.Trace.getZeroIndexedLineAndColumnForEvent(e);o=this.linkifyLocation({scriptId:null,url:a,lineNumber:s||0,columnNumber:0,target:t,isFreshRecording:r,linkifier:n})}break}case"v8.deserializeOnBackground":case"v8.parseOnBackground":{const e=c.url;e&&(o=this.linkifyLocation({scriptId:null,url:e,lineNumber:0,columnNumber:0,target:t,isFreshRecording:r,linkifier:n}));break}case"ProfileCall":{if(o=document.createElement("span"),!i.Types.TraceEvents.isProfileCall(e))break;const a=ze.resolvedNodeNameForEntry(e)||Ct.frameDisplayName(e.callFrame);u.UIUtils.createTextChild(o,a);const s=this.linkifyLocation({scriptId:e.callFrame.scriptId,url:e.callFrame.url,lineNumber:e.callFrame.lineNumber,columnNumber:e.callFrame.columnNumber,target:t,isFreshRecording:r,linkifier:n});s&&(u.UIUtils.createTextChild(o," @ "),o.appendChild(s));break}default:i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.Console)?s=null:o=this.linkifyTopCallFrame(e,t,n,r)??null}return!o&&s&&(o=document.createTextNode(s)),o}static linkifyLocation(e){const{scriptId:t,url:i,lineNumber:n,columnNumber:r,isFreshRecording:a,linkifier:s,target:o}=e,l={lineNumber:n,columnNumber:r,showColumnNumber:!0,inlineFrameIndex:0,className:"timeline-details",tabStop:!0};return a?s.linkifyScriptLocation(o,t,i,n,l):E.Linkifier.Linkifier.linkifyURL(i,l)}static linkifyTopCallFrame(e,t,n,r=!1){let a=i.Helpers.Trace.getZeroIndexedStackTraceForEvent(e)?.[0];if(i.Types.TraceEvents.isProfileCall(e)&&(a=e.callFrame),!a)return null;const s={className:"timeline-details",tabStop:!0,inlineFrameIndex:0,showColumnNumber:!0,columnNumber:a.columnNumber,lineNumber:a.lineNumber};return r?n.maybeLinkifyConsoleCallFrame(t,a,{showColumnNumber:!0,inlineFrameIndex:0}):E.Linkifier.Linkifier.linkifyURL(a.url,s)}static buildDetailsNodeForMarkerEvents(e){let t="https://web.dev/user-centric-performance-metrics/",i="page performance metrics";switch(e.name){case"largestContentfulPaint::Candidate":t="https://web.dev/lcp/",i="largest contentful paint";break;case"firstContentfulPaint":t="https://web.dev/first-contentful-paint/",i="first contentful paint"}return u.Fragment.html`<div>${u.XLink.XLink.create(t,wt(ft.learnMore),void 0,void 0,"learn-more")} about ${i}.</div>`}static buildConsumeCacheDetails(e,t){if("number"==typeof e.consumedCacheSize){t.appendTextRow(wt(ft.compilationCacheStatus),wt(ft.scriptLoadedFromCache)),t.appendTextRow(wt(ft.compilationCacheSize),c.NumberUtilities.bytesToString(e.consumedCacheSize));const i=e.cacheKind;i&&t.appendTextRow(wt(ft.compilationCacheKind),i)}else"cacheRejected"in e&&e.cacheRejected?t.appendTextRow(wt(ft.compilationCacheStatus),wt(ft.failedToLoadScriptFromCache)):t.appendTextRow(wt(ft.compilationCacheStatus),wt(ft.scriptNotEligibleToBeLoadedFromCache))}static async buildTraceEventDetails(t,n,o,l){const d=rt(t,n),{duration:h,selfTime:m}=i.Helpers.Timing.eventTimingsMilliSeconds(n),p=await i.Extras.FetchNodes.extractRelatedDOMNodesFromEvent(t,n);if(d&&void 0===n[kt]){let e=null;const r=i.Extras.URLForEntry.get(t,n);r?e=await E.ImagePreview.ImagePreview.build(d,r,!1,{imageAltText:E.ImagePreview.ImagePreview.defaultAltTextForImageURL(r),precomputedFeatures:void 0}):i.Types.TraceEvents.isTraceEventPaint(n)&&(e=await Ct.buildPicturePreviewContent(t,n,d)),n[kt]=e}let g;i.Types.TraceEvents.isSyntheticLayoutShift(n)&&(l=!1);const v=new Pt(rt(t,n),o),T=this.eventColor(n),f=t&&Ft(t,n)?Ct.markerStyleForEvent(n).color:T;v.addSection(Ct.eventTitle(n),f);const y=n.args,w=n.args?.data,b=t.Initiators.eventToInitiator.get(n)??null,S=t.Initiators.initiatorToEvents.get(n)??null;let C=null;if(t){const e=s.DetailsView.buildWarningElementsForEvent(n,t);for(const t of e)v.appendElementRow(wt(ft.warning),t,!0)}if(i.Helpers.Trace.eventHasCategory(n,i.Types.TraceEvents.Categories.UserTiming)){const i=Mt(n,t);v.appendTextRow(wt(ft.timestamp),e.TimeUtilities.preciseMillisToString(i,1))}if(l&&!Number.isNaN(h||0)&&0!==h&&(v.appendTextRow(wt(ft.totalTime),e.TimeUtilities.millisToString(h||0,!0)),v.appendTextRow(wt(ft.selfTime),e.TimeUtilities.millisToString(m,!0))),i.Types.TraceEvents.isTraceEventPerformanceMark(n)&&n.args.data?.detail){const e=Ct.renderObjectJson(JSON.parse(n.args.data?.detail));v.appendElementRow(wt(ft.details),e)}if(i.Types.TraceEvents.isSyntheticUserTiming(n)&&n.args?.data?.beginEvent.args.detail){const e=Ct.renderObjectJson(JSON.parse(n.args?.data?.beginEvent.args.detail));v.appendElementRow(wt(ft.details),e)}if(t.Meta.traceIsGeneric)return Ct.renderEventJson(n,v),v.fragment;if(i.Types.TraceEvents.isTraceEventV8Compile(n)){if(C=n.args.data?.url,C){const{lineNumber:e,columnNumber:t}=i.Helpers.Trace.getZeroIndexedLineAndColumnForEvent(n);v.appendLocationRow(wt(ft.script),C,e||0,t)}Boolean(n.args.data?.eager)&&v.appendTextRow(wt(ft.eagerCompile),!0);const e=Boolean(n.args.data?.streamed);v.appendTextRow(wt(ft.streamed),e+(e?"":`: ${n.args.data?.notStreamedReason||""}`)),n.args.data&&Ct.buildConsumeCacheDetails(n.args.data,v)}if(i.Types.Extensions.isSyntheticExtensionEntry(n))for(const[e,t]of n.args.properties||[])v.appendTextRow(e,t);const k=Boolean(t&&Ce.instance().recordingIsFresh(t));switch(n.name){case"GCEvent":case"MajorGC":case"MinorGC":{const e=y.usedHeapSizeBefore-y.usedHeapSizeAfter;v.appendTextRow(wt(ft.collected),c.NumberUtilities.bytesToString(e));break}case"ProfileCall":case"FunctionCall":{const e=await Ct.buildDetailsNodeForTraceEvent(n,rt(t,n),o,k,t);e&&v.appendElementRow(wt(ft.function),e);break}case"TimerFire":case"TimerInstall":case"TimerRemove":v.appendTextRow(wt(ft.timerId),w.timerId),"TimerInstall"===n.name&&(v.appendTextRow(wt(ft.timeout),e.TimeUtilities.millisToString(w.timeout)),v.appendTextRow(wt(ft.repeats),!w.singleShot));break;case"FireAnimationFrame":v.appendTextRow(wt(ft.callbackId),w.id);break;case"V8.CompileModule":v.appendLocationRow(wt(ft.module),y.fileName,0);break;case"V8.CompileScript":break;case"v8.produceModuleCache":C=w&&w.url,v.appendTextRow(wt(ft.compilationCacheSize),c.NumberUtilities.bytesToString(w.producedCacheSize));break;case"v8.produceCache":if(C=w&&w.url,C){const{lineNumber:e,columnNumber:t}=i.Helpers.Trace.getZeroIndexedLineAndColumnForEvent(n);v.appendLocationRow(wt(ft.script),C,e||0,t)}v.appendTextRow(wt(ft.compilationCacheSize),c.NumberUtilities.bytesToString(w.producedCacheSize));break;case"EvaluateScript":if(C=w&&w.url,C){const{lineNumber:e,columnNumber:t}=i.Helpers.Trace.getZeroIndexedLineAndColumnForEvent(n);v.appendLocationRow(wt(ft.script),C,e||0,t)}break;case"v8.wasm.streamFromResponseCallback":case"v8.wasm.compiledModule":case"v8.wasm.cachedModule":case"v8.wasm.moduleCacheHit":case"v8.wasm.moduleCacheInvalid":if(w){C=y.url,C&&v.appendTextRow(wt(ft.url),C);const e=y.producedCachedSize;e&&v.appendTextRow(wt(ft.producedCacheSize),e);const t=y.consumedCachedSize;t&&v.appendTextRow(wt(ft.consumedCacheSize),t)}break;case"Paint":{const e=w.clip;v.appendTextRow(wt(ft.location),wt(ft.sSCurlyBrackets,{PH1:e[0],PH2:e[1]}));const t=Ct.quadWidth(e),i=Ct.quadHeight(e);v.appendTextRow(wt(ft.dimensions),wt(ft.sSDimensions,{PH1:t,PH2:i}))}case"PaintSetup":case"Rasterize":case"ScrollLayer":g=wt(ft.layerRoot);break;case"PaintImage":case"Decode LazyPixelRef":case"Decode Image":case"Draw LazyPixelRef":if(g=wt(ft.ownerElement),C=i.Extras.URLForEntry.get(t,n),C){const e={tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0};v.appendElementRow(wt(ft.imageUrl),E.Linkifier.Linkifier.linkifyURL(C,e))}break;case"ParseAuthorStyleSheet":if(C=w.styleSheetUrl,C){const e={tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0};v.appendElementRow(wt(ft.stylesheetUrl),E.Linkifier.Linkifier.linkifyURL(C,e))}break;case"UpdateLayoutTree":{v.appendTextRow(wt(ft.elementsAffected),y.elementCount);const e=r.Settings.Settings.instance().createSetting("timeline-capture-selector-stats",!1);if(!e.get()){const t=document.createElement("span");t.textContent=wt(ft.sSelectorStatsInfo,{PH1:e.title()}),v.appendElementRow(wt(ft.selectorStatsTitle),t)}break}case"Layout":{const e=y.beginData;v.appendTextRow(wt(ft.nodesThatNeedLayout),wt(ft.sOfS,{PH1:e.dirtyObjects,PH2:e.totalObjects})),g=wt(ft.layoutRoot);break}case"ConsoleTime":v.appendTextRow(wt(ft.message),n.name);break;case"WebSocketCreate":case"WebSocketSendHandshakeRequest":case"WebSocketReceiveHandshakeResponse":case"WebSocketSend":case"WebSocketReceive":case"WebSocketDestroy":if(i.Types.TraceEvents.isWebSocketTraceEvent(n)){const e=s.DetailsView.buildRowsForWebSocketEvent(n,t);for(const{key:t,value:i}of e)v.appendTextRow(t,i)}break;case"EmbedderCallback":v.appendTextRow(wt(ft.callbackFunction),w.callbackName);break;case"Animation":"n"===n.ph&&v.appendTextRow(wt(ft.state),w.state);break;case"ParseHTML":{const e=y.beginData,t=e.startLine-1,i=y.endData?y.endData.endLine-1:void 0;C=e.url,C&&v.appendLocationRange(wt(ft.range),C,t,i);break}case"FireIdleCallback":v.appendTextRow(wt(ft.allottedTime),e.TimeUtilities.millisToString(w.allottedMilliseconds)),v.appendTextRow(wt(ft.invokedByTimeout),w.timedOut);case"RequestIdleCallback":case"CancelIdleCallback":v.appendTextRow(wt(ft.callbackId),w.id);break;case"EventDispatch":v.appendTextRow(wt(ft.type),w.type);break;case"largestContentfulPaint::Candidate":v.appendTextRow(wt(ft.type),String(w.type)),v.appendTextRow(wt(ft.size),String(w.size));case"firstPaint":case"firstContentfulPaint":case"MarkLoad":case"MarkDOMContent":{const r=Mt(n,t);v.appendTextRow(wt(ft.timestamp),e.TimeUtilities.preciseMillisToString(r,1)),i.Types.TraceEvents.isTraceEventMarkerEvent(n)&&v.appendElementRow(wt(ft.details),Ct.buildDetailsNodeForMarkerEvents(n));break}case"EventTiming":{const r=await Ct.buildDetailsNodeForTraceEvent(n,rt(t,n),o,k,t);if(r&&v.appendElementRow(wt(ft.details),r),i.Types.TraceEvents.isSyntheticInteractionEvent(n)){const t=e.TimeUtilities.formatMicroSecondsTime(n.inputDelay),i=e.TimeUtilities.formatMicroSecondsTime(n.mainThreadHandling),r=e.TimeUtilities.formatMicroSecondsTime(n.presentationDelay);v.appendTextRow(wt(ft.interactionID),n.interactionId),v.appendTextRow(wt(ft.inputDelay),t),v.appendTextRow(wt(ft.processingDuration),i),v.appendTextRow(wt(ft.presentationDelay),r)}break}case"LayoutShift":{if(!i.Types.TraceEvents.isSyntheticLayoutShift(n)){console.error("Unexpected type for LayoutShift event");break}const t=n,a=t.args.data,s=document.createElement("span"),o=u.XLink.XLink.create("https://web.dev/cls/",wt(ft.cumulativeLayoutShifts),void 0,void 0,"cumulative-layout-shifts"),l=u.XLink.XLink.create("https://web.dev/evolving-cls/",wt(ft.evolvedClsLink),void 0,void 0,"evolved-cls");if(s.appendChild(e.i18n.getFormatLocalizedString(yt,ft.sCLSInformation,{PH1:o,PH2:l})),v.appendElementRow(wt(ft.warning),s,!0),!a)break;v.appendTextRow(wt(ft.score),a.score.toPrecision(4)),v.appendTextRow(wt(ft.cumulativeScore),a.cumulative_score.toPrecision(4)),v.appendTextRow(wt(ft.currentClusterId),t.parsedData.sessionWindowData.id),v.appendTextRow(wt(ft.currentClusterScore),t.parsedData.sessionWindowData.cumulativeWindowScore.toPrecision(4)),v.appendTextRow(wt(ft.hadRecentInput),w.had_recent_input?wt(ft.yes):wt(ft.no));for(const e of w.impacted_nodes){const t=new X(e.old_rect),i=new X(e.new_rect),n=await r.Linkifier.Linkifier.linkify(t),a=await r.Linkifier.Linkifier.linkify(i);v.appendElementRow(wt(ft.movedFrom),n),v.appendElementRow(wt(ft.movedTo),a)}break}default:{const e=await Ct.buildDetailsNodeForTraceEvent(n,rt(t,n),o,k,t);e&&v.appendElementRow(wt(ft.details),e);break}}const x=p?.values()||[];for(const e of x)if(e){const t=await r.Linkifier.Linkifier.linkify(e);v.appendElementRow(g||wt(ft.relatedNode),t)}n[kt]&&(v.addSection(wt(ft.preview)),v.appendElementRow("",n[kt]));const P=i.Helpers.Trace.getZeroIndexedStackTraceForEvent(n);(b||S||P||t?.Invalidations.invalidationsForEvent.get(n))&&await Ct.generateCauses(n,v,t),a.Runtime.experiments.isEnabled("timeline-debug-mode")&&Ct.renderEventJson(n,v);const I={};if(l&&t&&Ct.aggregatedStatsForTraceEvent(I,t,n)){v.addSection(wt(ft.aggregatedTime));const e=Ct.generatePieChart(I,Ct.eventStyle(n).category,m);v.appendElementRow("",e)}return v.fragment}static statsForTimeRange(e,t,n){if(!e.length)return{idle:n-t};!function(e){if(e[It])return;const t={},n=[];let r=0;function a(e,i){let n=t[e];if(n||(n={time:[],value:[]},t[e]=n),n.time.length&&n.time[n.time.length-1]===i||r>i)return;const a=n.value.length>0?n.value[n.value.length-1]:0;n.value.push(a+i-r),n.time.push(i)}function s(e,t,i){e&&a(e,i),r=i,t&&a(t,i)}i.Helpers.Trace.forEachEvent(e,{onStartEvent:function(e){const{startTime:t}=i.Helpers.Timing.eventTimingsMilliSeconds(e),r=de(e.name)?.category.name||me().other.name,a=n.length?n[n.length-1]:null;r!==a&&s(a||null,r,t),n.push(r)},onEndEvent:function(e){const{endTime:t}=i.Helpers.Timing.eventTimingsMilliSeconds(e),r=n.pop(),a=n.length?n[n.length-1]:null;r!==a&&s(r||null,a||null,t||0)}});const o=e;o[It]=t}(e);const r=function(e,t){const i=Object.assign({},e);for(const e in t)i[e]-=t[e];return i}(s(n),s(t)),a=Object.values(r).reduce(((e,t)=>e+t),0);return r.idle=Math.max(0,n-t-a),r;function s(t){const i={},n=e[It];for(const e in n){const r=n[e],a=c.ArrayUtilities.upperBound(r.time,t,c.ArrayUtilities.DEFAULT_COMPARATOR);let s;if(0===a)s=0;else if(a===r.time.length)s=r.value[r.value.length-1];else{const e=r.time[a-1],i=r.time[a],n=r.value[a-1];s=n+(r.value[a]-n)*(t-e)/(i-e)}i[e]=s}return i}}static renderEventJson(e,t){t.addSection(wt(ft.traceEvent));const i={args:e.args,...e},n=Ct.renderObjectJson(i);t.appendElementRow("",n)}static renderObjectJson(e){const t=r.Settings.Settings.instance().moduleSetting("text-editor-indent").get().length,i=JSON.stringify(e,null,t).slice(0,1e4).replace(/{\n  /,"{ "),n=document.createElement("div"),a=n.attachShadow({mode:"open"});a.adoptedStyleSheets=[lt,st];const s=a.createChild("div");return s.classList.add("monospace","source-code"),s.textContent=i,x.CodeHighlighter.highlightNode(s,"text/javascript"),n}static stackTraceFromCallFrames(e){return{callFrames:e}}static async generateCauses(t,n,r){const{startTime:a}=i.Helpers.Timing.eventTimingsMilliSeconds(t);let s=wt(ft.initiatorStackTrace),o=wt(ft.stackTrace);switch(t.name){case"TimerFire":s=wt(ft.timerInstalled);break;case"FireAnimationFrame":s=wt(ft.animationFrameRequested);break;case"FireIdleCallback":s=wt(ft.idleCallbackRequested);break;case"UpdateLayoutTree":s=wt(ft.firstInvalidated),o=wt(ft.recalculationForced);break;case"Layout":s=wt(ft.firstLayoutInvalidation),o=wt(ft.layoutForced)}const l=i.Helpers.Trace.getZeroIndexedStackTraceForEvent(t);l&&l.length&&(n.addSection(o),n.createChildStackTraceElement(Ct.stackTraceFromCallFrames(l)));const c=r.Initiators.eventToInitiator.get(t),d=r.Initiators.initiatorToEvents.get(t),h=r.Invalidations.invalidationsForEvent.get(t);if(c){const t=i.Helpers.Trace.getZeroIndexedStackTraceForEvent(c);t&&(n.addSection(s),n.createChildStackTraceElement(Ct.stackTraceFromCallFrames(t.map((e=>({...e,scriptId:String(e.scriptId)}))))));const r=this.createEntryLink(c);n.appendElementRow(wt(ft.initiatedBy),r);const{startTime:o}=i.Helpers.Timing.eventTimingsMilliSeconds(c),l=a-o;n.appendTextRow(wt(ft.pendingFor),e.TimeUtilities.preciseMillisToString(l,1))}if(d){const e=document.createElement("div");d.map(((t,i)=>{e.appendChild(this.createEntryLink(t)),i<d.length-1&&e.append(" ")})),n.appendElementRow(ft.initiatorFor,e)}h&&h.length&&(n.addSection(wt(ft.invalidations)),await Ct.generateInvalidationsList(h,n))}static createEntryLink(e){const t=document.createElement("span"),i=h.TraceBounds.BoundsManager.instance().state();if(!i)return console.error("Tried to link to an entry without any traceBoundsState. This should never happen."),t;const n=i.micro.minimapTraceBounds.min>e.ts+(e.dur||0)||i.micro.minimapTraceBounds.max<e.ts,r=Ne.activeManager()?.getEntriesFilter().inEntryInvisible(e);return n||(t.classList.add("devtools-link"),u.ARIAUtils.markAsLink(t),t.tabIndex=0,t.addEventListener("click",(()=>{nr.instance().select(vt.fromTraceEvent(e))})),t.addEventListener("keydown",(t=>{"Enter"===t.key&&(nr.instance().select(vt.fromTraceEvent(e)),t.consume(!0))}))),t.textContent=r?this.eventTitle(e)+" "+wt(ft.entryIsHidden):n?this.eventTitle(e)+" "+wt(ft.outsideBreadcrumbRange):this.eventTitle(e),t}static async generateInvalidationsList(e,t){const{groupedByReason:i,backendNodeIds:r}=s.DetailsView.generateInvalidationsList(e);let a=null;const o=n.TargetManager.TargetManager.instance().primaryPageTarget(),l=o?.model(n.DOMModel.DOMModel);l&&(a=await l.pushNodesByBackendIdsToFrontend(r)),Object.keys(i).forEach((e=>{Ct.generateInvalidationsForReason(e,i[e],a,t)}))}static generateInvalidationsForReason(t,a,s,o){function l(e){const t=e.args.data.nodeId&&s?s.get(e.args.data.nodeId):null;if(t){const e=document.createElement("span");return r.Linkifier.Linkifier.linkify(t).then((t=>e.appendChild(t))),e}if(e.args.data.nodeName){const t=document.createElement("span");return t.textContent=e.args.data.nodeName,t}const i=document.createElement("span");return u.UIUtils.createTextChild(i,wt(ft.UnknownNode)),i}const c=new Set;for(const r of a){const a=i.Helpers.Trace.getZeroIndexedStackTraceForEvent(r);let s=null;const d=a?.at(0);d&&(s=o.linkifier()?.maybeLinkifyScriptLocation(n.TargetManager.TargetManager.instance().rootTarget(),d.scriptId,d.url,d.lineNumber)||null);const h=l(r),m=s?e.i18n.getFormatLocalizedString(yt,ft.invalidationWithCallFrame,{PH1:h,PH2:s}):h,p="string"==typeof m?m:m.innerText;c.has(p)||(c.add(p),o.appendElementRow(t,m))}}static aggregatedStatsForTraceEvent(e,t,n){const r=t.Renderer?.allTraceEntries||[],{startTime:a,endTime:s}=i.Helpers.Timing.eventTimingsMilliSeconds(n);const o=c.ArrayUtilities.binaryIndexOf(r,a,(function(e,t){const{startTime:n}=i.Helpers.Timing.eventTimingsMilliSeconds(t);return e-n}));if(o<0)return!1;let l=!1;if(s)for(let t=o;t<r.length;t++){const a=r[t],{startTime:c,selfTime:d}=i.Helpers.Timing.eventTimingsMilliSeconds(a);if(c>=s)break;if(!a.selfTime)continue;if(a.tid!==n.tid)continue;t>o&&(l=!0);const h=Ct.eventStyle(a).category.name;e[h]=(e[h]||0)+d}if(i.Types.TraceEvents.isAsyncPhase(n.ph)){if(s){let t=0;for(const i in e)t+=e[i];e.idle=Math.max(0,s-a-t)}return!1}return l}static async buildPicturePreviewContent(e,t,i){const r=e.LayerTree.paintsToSnapshots.get(t);if(!r)return null;const a=i.model(n.PaintProfiler.PaintProfilerModel);if(!a)return null;const s=await a.loadSnapshot(r.args.snapshot.skp64);if(!s)return null;const o={snapshot:s,rect:r.args.snapshot.params?.layer_rect};if(!o)return null;const l=o.snapshot.replay();o.snapshot.release();const c=await l;if(!c)return null;const d=document.createElement("div"),h=d.attachShadow({mode:"open"});h.adoptedStyleSheets=[ot];const m=h.createChild("div");m.classList.add("image-preview-container","vbox","link");const p=m.createChild("img");p.src=c,p.alt=E.ImagePreview.ImagePreview.defaultAltTextForImageURL(c);return m.createChild("a").textContent=wt(ft.paintProfiler),u.ARIAUtils.markAsLink(m),m.tabIndex=0,m.addEventListener("click",(()=>nr.instance().select(vt.fromTraceEvent(t))),!1),m.addEventListener("keydown",(e=>{"Enter"===e.key&&(nr.instance().select(vt.fromTraceEvent(t)),e.consume(!0))})),d}static createEventDivider(t,n){const r=document.createElement("div");r.classList.add("resources-event-divider");const{startTime:a}=i.Helpers.Timing.eventTimingsMilliSeconds(t),s=e.TimeUtilities.millisToString(a-n);u.Tooltip.Tooltip.install(r,wt(ft.sAtS,{PH1:Ct.eventTitle(t),PH2:s}));const o=Ct.markerStyleForEvent(t);return o.tall&&(r.style.backgroundColor=o.color),r}static visibleEventsFilter(){return new C.TimelineModelFilter.TimelineVisibleEventsFilter(ve())}static categories(){return me()}static generatePieChart(t,i,n){let r=0;for(const e in t)r+=t[e];const a=document.createElement("div");a.classList.add("timeline-details-view-pie-chart-wrapper"),a.classList.add("hbox");const s=new p.PieChart.PieChart,o=[];function l(e,t,i,n){i&&o.push({value:i,color:n,title:t})}if(i){n&&l(i.name,wt(ft.sSelf,{PH1:i.title}),n,i.getCSSValue());const e=t[i.name]-(n||0);e>0&&l(i.name,wt(ft.sChildren,{PH1:i.title}),e,i.getCSSValue())}for(const e in me()){const n=me()[e];e!==i?.name&&l(n.name,n.title,t[n.name],n.getCSSValue())}s.data={chartName:wt(ft.timeSpentInRendering),size:110,formatter:t=>e.TimeUtilities.preciseMillisToString(t),showLegend:!0,total:r,slices:o};return a.createChild("div","vbox").appendChild(s),a}static generateDetailsContentForFrame(e,t,i){const n=new Pt(null,null);n.addSection(wt(ft.frame));const r=Ct.frameDuration(e);if(n.appendElementRow(wt(ft.duration),r),t&&i){const e=document.createElement("div");e.classList.add("timeline-filmstrip-preview"),u.UIUtils.loadImage(i.screenshotEvent.args.dataUri).then((t=>t&&e.appendChild(t))),n.appendElementRow("",e),e.addEventListener("click",function(e,t){p.FilmStripView.Dialog.fromFilmStrip(e,t.index)}.bind(null,t,i),!1)}return n.fragment}static frameDuration(t){const n=i.Helpers.Timing.microSecondsToMilliseconds(t.startTimeOffset),r=i.Helpers.Timing.microSecondsToMilliseconds(i.Types.Timing.MicroSeconds(t.endTime-t.startTime)),a=wt(ft.sAtSParentheses,{PH1:e.TimeUtilities.millisToString(r,!0),PH2:e.TimeUtilities.millisToString(n,!0)});return e.i18n.getFormatLocalizedString(yt,ft.emptyPlaceholder,{PH1:a})}static quadWidth(e){return Math.round(Math.sqrt(Math.pow(e[0]-e[2],2)+Math.pow(e[1]-e[3],2)))}static quadHeight(e){return Math.round(Math.sqrt(Math.pow(e[0]-e[6],2)+Math.pow(e[1]-e[7],2)))}static eventDispatchDesciptors(){if(bt)return bt;const e="hsl(40,100%,80%)",t="hsl(40,100%,50%)";return bt=[new xt(1,e,["mousemove","mouseenter","mouseleave","mouseout","mouseover"]),new xt(1,e,["pointerover","pointerout","pointerenter","pointerleave","pointermove"]),new xt(2,"hsl(90,100%,40%)",["wheel"]),new xt(3,t,["click","mousedown","mouseup"]),new xt(3,t,["touchstart","touchend","touchmove","touchcancel"]),new xt(3,t,["pointerdown","pointerup","pointercancel","gotpointercapture","lostpointercapture"]),new xt(3,"hsl(256,100%,75%)",["keydown","keyup","keypress"])],bt}static markerStyleForEvent(e){const t=[6,4],n=Ct.eventTitle(e);if("navigationStart"!==e.name&&i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.Console)||i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.UserTiming))return{title:n,dashStyle:t,lineWidth:.5,color:i.Helpers.Trace.eventHasCategory(e,i.Types.TraceEvents.Categories.Console)?"purple":"orange",tall:!1,lowPriority:!1};let r=!1,a="grey";switch(e.name){case"navigationStart":a="#FF9800",r=!0;break;case"FrameStartedLoading":a="green",r=!0;break;case"MarkDOMContent":a="#0867CB",r=!0;break;case"MarkLoad":a="#B31412",r=!0;break;case"firstPaint":a="#228847",r=!0;break;case"firstContentfulPaint":a="#1A6937",r=!0;break;case"largestContentfulPaint::Candidate":a="#1A3422",r=!0;break;case"TimeStamp":a="orange"}return{title:n,dashStyle:t,lineWidth:.5,color:a,tall:r,lowPriority:!1}}static colorForId(e){return St||(St=new r.Color.Generator({min:30,max:330,count:void 0},{min:50,max:80,count:3},85),St.setColorForID("","#f2ecdc")),St.colorForID(e)}static displayNameForFrame(e,t=80){const i=e.url;return r.ParsedURL.schemeIs(i,"about:")?`"${c.StringUtilities.trimMiddle(e.name,t)}"`:e.url.slice(0,t)}}const Et=Symbol("aggregatedStats"),kt=Symbol("previewElement");class xt{priority;color;eventTypes;constructor(e,t,i){this.priority=e,this.color=t,this.eventTypes=i}}class Pt{fragment;linkifierInternal;target;element;tableElement;constructor(e,t){this.fragment=document.createDocumentFragment(),this.linkifierInternal=t,this.target=e,this.element=document.createElement("div"),this.element.classList.add("timeline-details-view-block"),this.tableElement=this.element.createChild("div","vbox timeline-details-chip-body"),this.fragment.appendChild(this.element)}addSection(e,t){if(this.tableElement.hasChildNodes()?(this.element=document.createElement("div"),this.element.classList.add("timeline-details-view-block"),this.fragment.appendChild(this.element)):this.element.removeChildren(),e){const i=this.element.createChild("div","timeline-details-chip-title");t&&(i.createChild("div").style.backgroundColor=t),u.UIUtils.createTextChild(i,e)}this.tableElement=this.element.createChild("div","vbox timeline-details-chip-body"),this.fragment.appendChild(this.element)}linkifier(){return this.linkifierInternal}appendTextRow(e,t){const i=this.tableElement.createChild("div","timeline-details-view-row");i.createChild("div","timeline-details-view-row-title").textContent=e,i.createChild("div","timeline-details-view-row-value").textContent=t.toString()}appendElementRow(e,t,i,n){const r=this.tableElement.createChild("div","timeline-details-view-row");r.setAttribute("data-row-title",e),i&&r.classList.add("timeline-details-warning"),n&&r.classList.add("timeline-details-stack-values");r.createChild("div","timeline-details-view-row-title").textContent=e;const a=r.createChild("div","timeline-details-view-row-value");t instanceof Node?a.appendChild(t):u.UIUtils.createTextChild(a,t||"")}appendLocationRow(e,t,i,n){if(!this.linkifierInternal)return;const r={tabStop:!0,columnNumber:n,showColumnNumber:!0,inlineFrameIndex:0},a=this.linkifierInternal.maybeLinkifyScriptLocation(this.target,null,t,i,r);a&&this.appendElementRow(e,a)}appendLocationRange(e,t,i,n){if(!this.linkifierInternal||!this.target)return;const r=document.createElement("span"),a=this.linkifierInternal.maybeLinkifyScriptLocation(this.target,null,t,i,{tabStop:!0,inlineFrameIndex:0});a&&(r.appendChild(a),u.UIUtils.createTextChild(r,c.StringUtilities.sprintf(" [%s…%s]",i+1,(n||0)+1||"")),this.appendElementRow(e,r))}createChildStackTraceElement(e){if(!this.linkifierInternal)return;const t=this.tableElement.createChild("div","timeline-details-view-row timeline-details-stack-values"),i=E.JSPresentationUtils.buildStackTracePreviewContents(this.target,this.linkifierInternal,{stackTrace:e,tabStops:!0,showColumnNumber:!0});t.appendChild(i.element)}}const It=Symbol("categoryBreakdownCache");function Mt(e,t){if(!t){const{startTime:t}=i.Helpers.Timing.eventTimingsMilliSeconds(e);return t}const n=i.Helpers.Timing.timeStampForEventAdjustedByClosestNavigation(e,t.Meta.traceBounds,t.Meta.navigationsByNavigationId,t.Meta.navigationsByFrameId);return i.Helpers.Timing.microSecondsToMilliseconds(n)}function Ft(e,t){const{KnownEventName:n}=i.Types.TraceEvents;if("TimeStamp"===t.name)return!0;if(i.Types.TraceEvents.isTraceEventFirstContentfulPaint(t)||i.Types.TraceEvents.isTraceEventFirstPaint(t))return t.args.frame===e.Meta.mainFrameId;if(i.Types.TraceEvents.isTraceEventMarkDOMContent(t)||i.Types.TraceEvents.isTraceEventMarkLoad(t)||i.Types.TraceEvents.isTraceEventLargestContentfulPaintCandidate(t)){if(!t.args.data)return!1;const{isOutermostMainFrame:e,isMainFrame:i}=t.args.data;return void 0!==e?e:Boolean(i)}return!1}var Lt=Object.freeze({__proto__:null,TimelineUIUtils:Ct,aggregatedStatsKey:Et,previewElementSymbol:kt,EventDispatchTypeDescriptor:xt,TimelineDetailsContentHelper:Pt,categoryBreakdownCacheSymbol:It,timeStampForEventAdjustedForClosestNavigationIfPossible:Mt,isMarkerEvent:Ft});class Rt extends C.TimelineModelFilter.TimelineModelFilter{#R=i.Types.Timing.MilliSeconds(0);constructor(){super()}setMinimumRecordDuration(e){this.#R=e}accept(e){const{duration:t}=i.Helpers.Timing.eventTimingsMilliSeconds(e);return t>=this.#R}}class Dt extends C.TimelineModelFilter.TimelineModelFilter{constructor(){super()}accept(e){return!Ct.eventStyle(e).category.hidden}}class At extends C.TimelineModelFilter.TimelineModelFilter{regExpInternal;constructor(e){super(),this.setRegExp(e||null)}setRegExp(e){this.regExpInternal=e}regExp(){return this.regExpInternal}accept(e,t){return!this.regExpInternal||Ct.testContentMatching(e,this.regExpInternal,t)}}var Nt=Object.freeze({__proto__:null,IsLong:Rt,Category:Dt,TimelineRegExp:At});const Bt={performance:"Performance",selfTime:"Self Time",totalTime:"Total Time",activity:"Activity",selectItemForDetails:"Select item for details.",fms:"{PH1} ms",percentPlaceholder:"{PH1} %",chromeExtensionsOverhead:"[`Chrome` extensions overhead]",vRuntime:"[`V8` Runtime]",unattributed:"[unattributed]",page:"Page",noGrouping:"No Grouping",groupByActivity:"Group by Activity",groupByCategory:"Group by Category",groupByDomain:"Group by Domain",groupByFrame:"Group by Frame",groupBySubdomain:"Group by Subdomain",groupByUrl:"Group by URL",groupBy:"Group by",heaviestStack:"Heaviest stack",showHeaviestStack:"Show Heaviest stack",hideHeaviestStack:"Hide Heaviest stack",heaviestStackShown:"Heaviest stack sidebar shown",heaviestStackHidden:"Heaviest stack sidebar hidden",timelineStack:"Timeline Stack",matchCase:"Match Case",useRegularExpression:"Use Regular Expression",matchWholeWord:"Match whole word"},Ht=e.i18n.registerUIStrings("panels/timeline/TimelineTreeView.ts",Bt),Ut=e.i18n.getLocalizedString.bind(void 0,Ht);class Wt extends u.Widget.VBox{#D;searchResults;linkifier;dataGrid;lastHoveredProfileNode;textFilterInternal;taskFilter;startTime;endTime;splitWidget;detailsView;searchableView;currentThreadSetting;lastSelectedNodeInternal;root;currentResult;textFilterUI;caseSensitiveButton;regexButton;matchWholeWord;#A=null;constructor(){super(),this.#D=null,this.element.classList.add("timeline-tree-view"),this.searchResults=[]}#N(e){const t=Ct.eventTitle(e)||e.name;return this.#A?t+":@"+i.Extras.URLForEntry.get(this.#A,e):t}setSearchableView(e){this.searchableView=e}setModelWithEvents(e,t=null){this.#A=t,this.#D=e}traceParseData(){return this.#A}init(){this.linkifier=new E.Linkifier.Linkifier,this.taskFilter=new C.TimelineModelFilter.ExclusiveNameFilter(["RunTask"]),this.textFilterInternal=new At,this.currentThreadSetting=r.Settings.Settings.instance().createSetting("timeline-tree-current-thread",0),this.currentThreadSetting.addChangeListener(this.refreshTree,this);const e=[];this.populateColumns(e),this.splitWidget=new u.SplitWidget.SplitWidget(!0,!0,"timeline-tree-view-details-split-widget");const t=new u.Widget.VBox,i=new u.Toolbar.Toolbar("",t.element);i.makeWrappable(!0),this.populateToolbar(i),this.dataGrid=new k.SortableDataGrid.SortableDataGrid({displayName:Ut(Bt.performance),columns:e,refreshCallback:void 0,editCallback:void 0,deleteCallback:void 0}),this.dataGrid.addEventListener("SortingChanged",this.sortingChanged,this),this.dataGrid.element.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.dataGrid.setResizeMethod("last"),this.dataGrid.setRowContextMenuCallback(this.onContextMenu.bind(this)),this.dataGrid.asWidget().show(t.element),this.dataGrid.addEventListener("SelectedNode",this.updateDetailsForSelection,this),this.detailsView=new u.Widget.VBox,this.detailsView.element.classList.add("timeline-details-view","timeline-details-view-body"),this.splitWidget.setMainWidget(t),this.splitWidget.setSidebarWidget(this.detailsView),this.splitWidget.hideSidebar(),this.splitWidget.show(this.element),this.splitWidget.addEventListener("ShowModeChanged",this.onShowModeChanged,this),this.lastSelectedNodeInternal}lastSelectedNode(){return this.lastSelectedNodeInternal}updateContents(e){this.setRange(e.startTime,e.endTime)}setRange(e,t){this.startTime=e,this.endTime=t,this.refreshTree()}filters(){return[this.taskFilter,this.textFilterInternal,...be.instance().activeFilters()]}filtersWithoutTextFilter(){return[this.taskFilter,...be.instance().activeFilters()]}textFilter(){return this.textFilterInternal}exposePercentages(){return!1}populateToolbar(e){this.caseSensitiveButton=new u.Toolbar.ToolbarToggle(Ut(Bt.matchCase)),this.caseSensitiveButton.setText("Aa"),this.caseSensitiveButton.addEventListener("Click",(()=>{this.#B(this.caseSensitiveButton)}),this),e.appendToolbarItem(this.caseSensitiveButton),this.regexButton=new u.Toolbar.ToolbarToggle(Ut(Bt.useRegularExpression)),this.regexButton.setText(".*"),this.regexButton.addEventListener("Click",(()=>{this.#B(this.regexButton)}),this),e.appendToolbarItem(this.regexButton),this.matchWholeWord=new u.Toolbar.ToolbarToggle(Ut(Bt.matchWholeWord),"match-whole-word"),this.matchWholeWord.addEventListener("Click",(()=>{this.#B(this.matchWholeWord)}),this),e.appendToolbarItem(this.matchWholeWord);const t=new u.Toolbar.ToolbarFilter;this.textFilterUI=t,t.addEventListener("TextChanged",this.#H,this),e.appendToolbarItem(t)}selectedEvents(){return this.#D||[]}onHover(e){}appendContextMenuItems(e,t){}selectProfileNode(e,t){const i=[];let n=e;for(;n;n=n.parent)i.push(n);for(let e=i.length-1;e>0;--e){const t=this.dataGridNodeForTreeNode(i[e]);t&&t.dataGrid&&t.expand()}const r=this.dataGridNodeForTreeNode(e);r&&r.dataGrid&&(r.reveal(),r.select(t))}refreshTree(){if(!this.element.parentElement)return;if(this.linkifier.reset(),this.dataGrid.rootNode().removeChildren(),!this.#A)return void this.updateDetailsForSelection();this.root=this.buildTree();const e=this.root.children();let t=0,i=0;const n=this.root.totalTime-this.root.selfTime;for(const n of e.values())t=Math.max(t,n.selfTime),i=Math.max(i,n.totalTime);for(const r of e.values()){const e=new Vt(r,n,t,i,this);this.dataGrid.insertChild(e)}this.sortingChanged(),this.updateDetailsForSelection(),this.searchableView&&this.searchableView.refreshSearch();const r=this.dataGrid.rootNode();r.children.length>0&&r.children[0].select(!0)}buildTree(){throw new Error("Not Implemented")}buildTopDownTree(e,t){return new C.TimelineProfileTree.TopDownRootNode(this.selectedEvents(),this.filters(),this.startTime,this.endTime,e,t)}populateColumns(e){e.push({id:"self",title:Ut(Bt.selfTime),width:"120px",fixedWidth:!0,sortable:!0}),e.push({id:"total",title:Ut(Bt.totalTime),width:"120px",fixedWidth:!0,sortable:!0}),e.push({id:"activity",title:Ut(Bt.activity),disclosure:!0,sortable:!0})}sortingChanged(){const e=this.dataGrid.sortColumnId();if(!e)return;let t;const i=(e,t)=>{const i=t,n=e.profileNode.event,r=i.profileNode.event;if(!n||!r)return 0;if(!this.#A)return 0;const a=this.#N(n),s=this.#N(r);return a.localeCompare(s)};switch(e){case"start-time":t=function(e,t){const i=t,n=e.profileNode.event,r=i.profileNode.event;if(!n||!r)return 0;return n.ts-r.ts};break;case"self":t=function(e,t){const i=t;return e.profileNode.selfTime-i.profileNode.selfTime};break;case"total":t=function(e,t){const i=t;return e.profileNode.totalTime-i.profileNode.totalTime};break;case"activity":t=i;break;default:return void console.assert(!1,"Unknown sort field: "+e)}this.dataGrid.sortNodes(t,!this.dataGrid.isSortOrderAscending())}#H(){const e=this.textFilterUI&&this.textFilterUI.value(),t=void 0!==this.caseSensitiveButton&&this.caseSensitiveButton.toggled(),i=void 0!==this.regexButton&&this.regexButton.toggled(),n=void 0!==this.matchWholeWord&&this.matchWholeWord.toggled();this.textFilterInternal.setRegExp(e?c.StringUtilities.createSearchRegex(e,t,i,n):null),this.refreshTree()}#B(e){e&&e.setToggled(!e.toggled()),this.#H()}onShowModeChanged(){"OnlyMain"!==this.splitWidget.showMode()&&(this.lastSelectedNodeInternal=void 0,this.updateDetailsForSelection())}updateDetailsForSelection(){const e=this.dataGrid.selectedNode?this.dataGrid.selectedNode.profileNode:null;if(e===this.lastSelectedNodeInternal)return;if(this.lastSelectedNodeInternal=e,"OnlyMain"===this.splitWidget.showMode())return;if(this.detailsView.detachChildWidgets(),this.detailsView.element.removeChildren(),e&&this.showDetailsForNode(e))return;const t=this.detailsView.element.createChild("div","full-widget-dimmed-banner");u.UIUtils.createTextChild(t,Ut(Bt.selectItemForDetails))}showDetailsForNode(e){return!1}onMouseMove(e){const t=e.target&&e.target instanceof Node?this.dataGrid.dataGridNodeFromNode(e.target):null,i=t&&t._profileNode;i!==this.lastHoveredProfileNode&&(this.lastHoveredProfileNode=i,this.onHover(i))}onContextMenu(e,t){const i=t;i.linkElement&&e.appendApplicableItems(i.linkElement);const n=i.profileNode;n&&this.appendContextMenuItems(e,n)}dataGridNodeForTreeNode(e){return _t.get(e)||null}onSearchCanceled(){this.searchResults=[],this.currentResult=0}performSearch(e,t,i){if(this.searchResults=[],this.currentResult=0,!this.root)return;const n=e.toSearchRegex();this.searchResults=this.root.searchTree((e=>Ct.testContentMatching(e,n.regex,this.#A||void 0))),this.searchableView.updateSearchMatchesCount(this.searchResults.length)}jumpToNextSearchResult(){this.searchResults.length&&void 0!==this.currentResult&&(this.selectProfileNode(this.searchResults[this.currentResult],!1),this.currentResult=c.NumberUtilities.mod(this.currentResult+1,this.searchResults.length))}jumpToPreviousSearchResult(){this.searchResults.length&&void 0!==this.currentResult&&(this.selectProfileNode(this.searchResults[this.currentResult],!1),this.currentResult=c.NumberUtilities.mod(this.currentResult-1,this.searchResults.length))}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!0}}class Ot extends k.SortableDataGrid.SortableDataGridNode{populated;profileNode;treeView;grandTotalTime;maxSelfTime;maxTotalTime;linkElement;constructor(e,t,i,n,r){super(null,!1),this.populated=!1,this.profileNode=e,this.treeView=r,this.grandTotalTime=t,this.maxSelfTime=i,this.maxTotalTime=n,this.linkElement=null}createCell(e){return"activity"===e?this.createNameCell(e):this.createValueCell(e)||super.createCell(e)}createNameCell(e){const t=this.createTD(e),n=t.createChild("div","name-container"),r=n.createChild("div","activity-icon-container"),a=r.createChild("div","activity-icon"),s=n.createChild("div","activity-name"),o=this.profileNode.event;if(this.profileNode.isGroupNode()){const e=this.treeView.displayInfoForGroupNode(this.profileNode);s.textContent=e.name,a.style.backgroundColor=e.color,e.icon&&r.insertBefore(e.icon,a)}else if(o){s.textContent=Ct.eventTitle(o);const e=this.treeView.traceParseData(),t=e?rt(e,o):null,r=this.treeView.linkifier,l=Boolean(e&&Ce.instance().recordingIsFresh(e));this.linkElement=Ct.linkifyTopCallFrame(o,t,r,l),this.linkElement&&n.createChild("div","activity-link").appendChild(this.linkElement);const c=Ct.eventStyle(o).category;u.ARIAUtils.setLabel(a,c.title),a.style.backgroundColor=c.getComputedColorValue(),i.Types.Extensions.isSyntheticExtensionEntry(o)&&(a.style.backgroundColor=P.ExtensionUI.extensionEntryColor(o))}return t}createValueCell(e){if("self"!==e&&"total"!==e&&"start-time"!==e)return null;let t,n,r,a=!1;switch(e){case"start-time":{r=this.profileNode.event;const e=this.treeView.traceParseData();if(!e)throw new Error("Unable to load trace data for tree view");const n=r&&i.Helpers.Timing.eventTimingsMilliSeconds(r);t=(n?.startTime??0)-i.Helpers.Timing.microSecondsToMilliseconds(e.Meta.traceBounds.min)}break;case"self":t=this.profileNode.selfTime,n=this.maxSelfTime,a=!0;break;case"total":t=this.profileNode.totalTime,n=this.maxTotalTime,a=!0;break;default:return null}const s=this.createTD(e);s.className="numeric-column",s.setAttribute("title",Ut(Bt.fms,{PH1:t.toFixed(4)}));const o=s.createChild("div");return o.createChild("span").textContent=Ut(Bt.fms,{PH1:t.toFixed(1)}),a&&this.treeView.exposePercentages()&&(o.createChild("span","percent-column").textContent=Ut(Bt.percentPlaceholder,{PH1:(t/this.grandTotalTime*100).toFixed(1)})),n&&(o.classList.add("background-percent-bar"),s.createChild("div","background-bar-container").createChild("div","background-bar").style.width=(100*t/n).toFixed(1)+"%"),s}}class Vt extends Ot{constructor(e,t,i,n,r){super(e,t,i,n,r),this.setHasChildren(this.profileNode.hasChildren()),_t.set(e,this)}populate(){if(!this.populated&&(this.populated=!0,this.profileNode.children))for(const e of this.profileNode.children().values()){const t=new Vt(e,this.grandTotalTime,this.maxSelfTime,this.maxTotalTime,this.treeView);this.insertChildOrdered(t)}}}const _t=new WeakMap;class zt extends Wt{groupBySetting;stackView;executionContextNamesByOrigin=new Map;constructor(){super(),this.groupBySetting=r.Settings.Settings.instance().createSetting("timeline-tree-group-by",zt.GroupBy.None),this.groupBySetting.addChangeListener(this.refreshTree.bind(this)),this.init(),this.stackView=new qt(this),this.stackView.addEventListener("SelectionChanged",this.onStackViewSelectionChanged,this)}setGroupBySettingForTests(e){this.groupBySetting.set(e)}updateContents(e){this.updateExtensionResolver(),super.updateContents(e);const t=this.dataGrid.rootNode();t.children.length&&t.children[0].select(!0)}updateExtensionResolver(){this.executionContextNamesByOrigin=new Map;for(const e of n.TargetManager.TargetManager.instance().models(n.RuntimeModel.RuntimeModel))for(const t of e.executionContexts())this.executionContextNamesByOrigin.set(t.origin,t.name)}beautifyDomainName(e){return zt.isExtensionInternalURL(e)?e=Ut(Bt.chromeExtensionsOverhead):zt.isV8NativeURL(e)?e=Ut(Bt.vRuntime):e.startsWith("chrome-extension")&&(e=this.executionContextNamesByOrigin.get(e)||e),e}displayInfoForGroupNode(e){const t=me(),i=e.id&&e.event?Ct.eventColor(e.event):t.other.color,n=Ut(Bt.unattributed),r="symbol"==typeof e.id?void 0:e.id;switch(this.groupBySetting.get()){case zt.GroupBy.Category:{const e=r&&he(r)?t[r]||t.other:{title:n,color:n};return{name:e.title,color:e.color,icon:void 0}}case zt.GroupBy.Domain:case zt.GroupBy.Subdomain:return{name:(r?this.beautifyDomainName(r):void 0)||n,color:i,icon:void 0};case zt.GroupBy.EventName:if(!e.event)throw new Error("Unable to find event for group by operation");return{name:Ct.eventTitle(e.event),color:i,icon:void 0};case zt.GroupBy.URL:break;case zt.GroupBy.Frame:{const e=r?this.traceParseData()?.PageFrames.frames.get(r):void 0;return{name:e?Ct.displayNameForFrame(e):Ut(Bt.page),color:i,icon:void 0}}default:console.assert(!1,"Unexpected grouping type")}return{name:r||n,color:i,icon:void 0}}populateToolbar(e){super.populateToolbar(e);const t=zt.GroupBy,i=[{label:Ut(Bt.noGrouping),value:t.None},{label:Ut(Bt.groupByActivity),value:t.EventName},{label:Ut(Bt.groupByCategory),value:t.Category},{label:Ut(Bt.groupByDomain),value:t.Domain},{label:Ut(Bt.groupByFrame),value:t.Frame},{label:Ut(Bt.groupBySubdomain),value:t.Subdomain},{label:Ut(Bt.groupByUrl),value:t.URL}];e.appendToolbarItem(new u.Toolbar.ToolbarSettingComboBox(i,this.groupBySetting,Ut(Bt.groupBy))),e.appendSpacer(),e.appendToolbarItem(this.splitWidget.createShowHideSidebarButton(Ut(Bt.showHeaviestStack),Ut(Bt.hideHeaviestStack),Ut(Bt.heaviestStackShown),Ut(Bt.heaviestStackHidden)))}buildHeaviestStack(e){console.assert(Boolean(e.parent),"Attempt to build stack for tree root");let t=[];for(let i=e;i&&i.parent;i=i.parent)t.push(i);t=t.reverse();for(let i=e;i&&i.children()&&i.children().size;){const e=Array.from(i.children().values());i=e.reduce(((e,t)=>e.totalTime>t.totalTime?e:t)),t.push(i)}return t}exposePercentages(){return!0}onStackViewSelectionChanged(){const e=this.stackView.selectedTreeNode();e&&this.selectProfileNode(e,!0)}showDetailsForNode(e){const t=this.buildHeaviestStack(e);return this.stackView.setStack(t,e),this.stackView.show(this.detailsView.element),!0}groupingFunction(e){const t=zt.GroupBy;switch(e){case t.None:return null;case t.EventName:return e=>Ct.eventStyle(e).title;case t.Category:return e=>Ct.eventStyle(e).category.name;case t.Subdomain:return this.domainByEvent.bind(this,!1);case t.Domain:return this.domainByEvent.bind(this,!0);case t.URL:return e=>{const t=this.traceParseData();return t?i.Extras.URLForEntry.get(t,e)??"":""};case t.Frame:return e=>i.Helpers.Trace.frameIDForEvent(e)||this.traceParseData()?.Meta.mainFrameId||"";default:return console.assert(!1,`Unexpected aggregation setting: ${e}`),null}}domainByEvent(e,t){const n=this.traceParseData();if(!n)return"";const a=i.Extras.URLForEntry.get(n,t);if(!a)return"";if(zt.isExtensionInternalURL(a))return zt.extensionInternalPrefix;if(zt.isV8NativeURL(a))return zt.v8NativePrefix;const s=r.ParsedURL.ParsedURL.fromString(a);if(!s)return"";if("chrome-extension"===s.scheme)return s.scheme+"://"+s.host;if(!e)return s.host;if(/^[.0-9]+$/.test(s.host))return s.host;const o=/([^.]*\.)?[^.]*$/.exec(s.host);return o&&o[0]||""}static isExtensionInternalURL(e){return e.startsWith(zt.extensionInternalPrefix)}static isV8NativeURL(e){return e.startsWith(zt.v8NativePrefix)}static extensionInternalPrefix="extensions::";static v8NativePrefix="native "}!function(e){let t;!function(e){e.None="None",e.EventName="EventName",e.Category="Category",e.Domain="Domain",e.Subdomain="Subdomain",e.URL="URL",e.Frame="Frame"}(t=e.GroupBy||(e.GroupBy={}))}(zt||(zt={}));class Gt extends zt{constructor(){super(),this.dataGrid.markColumnAsSortedBy("total",k.DataGrid.Order.Descending)}buildTree(){const e=this.groupBySetting.get();return this.buildTopDownTree(!1,this.groupingFunction(e))}}class jt extends zt{constructor(){super(),this.dataGrid.markColumnAsSortedBy("self",k.DataGrid.Order.Descending)}buildTree(){return new C.TimelineProfileTree.BottomUpRootNode(this.selectedEvents(),this.textFilter(),this.filtersWithoutTextFilter(),this.startTime,this.endTime,this.groupingFunction(this.groupBySetting.get()))}}class qt extends(r.ObjectWrapper.eventMixin(u.Widget.VBox)){treeView;dataGrid;constructor(e){super();this.element.createChild("div","timeline-stack-view-header").textContent=Ut(Bt.heaviestStack),this.treeView=e;const t=[{id:"total",title:Ut(Bt.totalTime),fixedWidth:!0,width:"110px"},{id:"activity",title:Ut(Bt.activity)}];this.dataGrid=new k.ViewportDataGrid.ViewportDataGrid({displayName:Ut(Bt.timelineStack),columns:t,deleteCallback:void 0,editCallback:void 0,refreshCallback:void 0}),this.dataGrid.setResizeMethod("last"),this.dataGrid.addEventListener("SelectedNode",this.onSelectionChanged,this),this.dataGrid.asWidget().show(this.element)}setStack(e,t){const i=this.dataGrid.rootNode();i.removeChildren();let n=null;const r=Math.max.apply(Math,e.map((e=>e.totalTime)));for(const a of e){const e=new Ot(a,r,r,r,this.treeView);i.appendChild(e),a===t&&(n=e)}n&&n.revealAndSelect()}selectedTreeNode(){const e=this.dataGrid.selectedNode;return e&&e.profileNode}onSelectionChanged(){this.dispatchEventToListeners("SelectionChanged")}}var $t=Object.freeze({__proto__:null,TimelineTreeView:Wt,GridNode:Ot,TreeGridNode:Vt,get AggregatedTimelineTreeView(){return zt},CallTreeTimelineTreeView:Gt,BottomUpTimelineTreeView:jt,TimelineStackView:qt});const Jt={startTime:"Start Time",durationFilter:"Duration filter",Dms:"{PH1} ms",all:"All"},Kt=e.i18n.registerUIStrings("panels/timeline/EventsTimelineTreeView.ts",Jt),Yt=e.i18n.getLocalizedString.bind(void 0,Kt);class Xt extends Wt{filtersControl;delegate;currentTree;constructor(e){super(),this.filtersControl=new Zt,this.filtersControl.addEventListener("FilterChanged",this.onFilterChanged,this),this.init(),this.delegate=e,this.dataGrid.markColumnAsSortedBy("start-time",k.DataGrid.Order.Ascending),this.splitWidget.showBoth()}filters(){return[...super.filters(),...this.filtersControl.filters()]}updateContents(e){super.updateContents(e),vt.isTraceEventSelection(e.object)&&this.selectEvent(e.object,!0)}buildTree(){return this.currentTree=this.buildTopDownTree(!0,null),this.currentTree}onFilterChanged(){const e=this.lastSelectedNode(),t=e&&e.event;this.refreshTree(),t&&this.selectEvent(t,!1)}findNodeWithEvent(e){if("RunTask"===e.name)return null;const t=[this.currentTree.children().values()];for(;t.length;){const{done:i,value:n}=t[t.length-1].next();if(i)t.pop();else{if(n.event===e)return n;t.push(n.children().values())}}return null}selectEvent(e,t){const i=this.findNodeWithEvent(e);if(i&&(this.selectProfileNode(i,!1),t)){const e=this.dataGridNodeForTreeNode(i);e&&e.expand()}}populateColumns(e){e.push({id:"start-time",title:Yt(Jt.startTime),width:"80px",fixedWidth:!0,sortable:!0}),super.populateColumns(e),e.filter((e=>e.fixedWidth)).forEach((e=>{e.width="80px"}))}populateToolbar(e){super.populateToolbar(e),this.filtersControl.populateToolbar(e)}showDetailsForNode(e){const t=this.traceParseData();if(!t)return!1;const i=e.event;return!!i&&(Ct.buildTraceEventDetails(t,i,this.linkifier,!1).then((e=>this.detailsView.element.appendChild(e))),!0)}onHover(e){this.delegate.highlightEvent(e&&e.event)}}class Zt extends r.ObjectWrapper.ObjectWrapper{categoryFilter;durationFilter;filtersInternal;constructor(){super(),this.categoryFilter=new Dt,this.durationFilter=new Rt,this.filtersInternal=[this.categoryFilter,this.durationFilter]}filters(){return this.filtersInternal}populateToolbar(e){const t=new u.Toolbar.ToolbarComboBox(function(){const e=t.selectedOption().value,n=parseInt(e,10);this.durationFilter.setMinimumRecordDuration(i.Types.Timing.MilliSeconds(n)),this.notifyFiltersChanged()}.bind(this),Yt(Jt.durationFilter));for(const e of Zt.durationFilterPresetsMs)t.addOption(t.createOption(e?`≥ ${Yt(Jt.Dms,{PH1:e})}`:Yt(Jt.all),String(e)));e.appendToolbarItem(t);const n=new Map,r=me();for(const t in r){const i=r[t];if(!i.visible)continue;const s=new u.Toolbar.ToolbarCheckbox(i.title,void 0,a.bind(this,t));s.setChecked(!0),s.inputElement.style.backgroundColor=i.color,n.set(i.name,s),e.appendToolbarItem(s)}function a(e){const t=me(),i=n.get(e);t[e].hidden=!i||!i.checked(),this.notifyFiltersChanged()}}notifyFiltersChanged(){this.dispatchEventToListeners("FilterChanged")}static durationFilterPresetsMs=[0,1,15]}var Qt=Object.freeze({__proto__:null,EventsTimelineTreeView:Xt,Filters:Zt});class ei extends u.SplitWidget.SplitWidget{showPaintProfilerCallback;rightSplitWidget;layerViewHost;layers3DView;frameLayerTree;updateWhenVisible;constructor(e){super(!0,!1,"timeline-layers-view"),this.showPaintProfilerCallback=e,this.element.classList.add("timeline-layers-view"),this.rightSplitWidget=new u.SplitWidget.SplitWidget(!0,!0,"timeline-layers-view-details"),this.rightSplitWidget.element.classList.add("timeline-layers-view-properties"),this.setMainWidget(this.rightSplitWidget);const t=new u.Widget.VBox;this.setSidebarWidget(t),this.layerViewHost=new I.LayerViewHost.LayerViewHost;const i=new I.LayerTreeOutline.LayerTreeOutline(this.layerViewHost);t.element.appendChild(i.element),this.layers3DView=new I.Layers3DView.Layers3DView(this.layerViewHost),this.layers3DView.addEventListener("PaintProfilerRequested",this.onPaintProfilerRequested,this),this.rightSplitWidget.setMainWidget(this.layers3DView);const n=new I.LayerDetailsView.LayerDetailsView(this.layerViewHost);this.rightSplitWidget.setSidebarWidget(n),n.addEventListener("PaintProfilerRequested",this.onPaintProfilerRequested,this)}showLayerTree(e){this.frameLayerTree=e,this.isShowing()?this.update():this.updateWhenVisible=!0}wasShown(){this.updateWhenVisible&&(this.updateWhenVisible=!1,this.update())}async onPaintProfilerRequested(e){const t=e.data,i=await this.layers3DView.snapshotForSelection(t);i&&this.showPaintProfilerCallback(i.snapshot)}update(){this.frameLayerTree&&this.frameLayerTree.layerTreePromise().then((e=>this.layerViewHost.setLayerTree(e)))}}var ti=Object.freeze({__proto__:null,TimelineLayersView:ei});const ii=new CSSStyleSheet;ii.replaceSync(".paint-profiler-image-view{overflow:hidden}.paint-profiler-image-view .paint-profiler-image-container{transform-origin:0 0}.paint-profiler-image-view .paint-profiler-image-container div{border-color:1px solid var(--sys-color-divider);border-style:solid;z-index:100;position:absolute;top:0;left:0}.paint-profiler-image-view img{border:solid 1px var(--sys-color-inverse-surface)}\n/*# sourceURL=timelinePaintProfiler.css */\n");class ni extends u.SplitWidget.SplitWidget{logAndImageSplitWidget;imageView;paintProfilerView;logTreeView;needsUpdateWhenVisible;pendingSnapshot;event;paintProfilerModel;lastLoadedSnapshot;#U;constructor(e){super(!1,!1),this.element.classList.add("timeline-paint-profiler-view"),this.setSidebarSize(60),this.setResizable(!1),this.#U=e,this.logAndImageSplitWidget=new u.SplitWidget.SplitWidget(!0,!1),this.logAndImageSplitWidget.element.classList.add("timeline-paint-profiler-log-split"),this.setMainWidget(this.logAndImageSplitWidget),this.imageView=new ri,this.logAndImageSplitWidget.setMainWidget(this.imageView),this.paintProfilerView=new I.PaintProfilerView.PaintProfilerView(this.imageView.showImage.bind(this.imageView)),this.paintProfilerView.addEventListener("WindowChanged",this.onWindowChanged,this),this.setSidebarWidget(this.paintProfilerView),this.logTreeView=new I.PaintProfilerView.PaintProfilerCommandLogView,this.logAndImageSplitWidget.setSidebarWidget(this.logTreeView),this.needsUpdateWhenVisible=!1,this.pendingSnapshot=null,this.event=null,this.paintProfilerModel=null,this.lastLoadedSnapshot=null}wasShown(){super.wasShown(),this.needsUpdateWhenVisible&&(this.needsUpdateWhenVisible=!1,this.update())}setSnapshot(e){this.releaseSnapshot(),this.pendingSnapshot=e,this.event=null,this.updateWhenVisible()}#W(e){const t=e.args.tileData;if(!t)return!1;const i=this.#U.Frames.framesById[t.sourceFrameNumber];return!(!i||!i.layerTree)}setEvent(e,t){if(this.releaseSnapshot(),this.paintProfilerModel=e,this.pendingSnapshot=null,this.event=t,this.updateWhenVisible(),i.Types.TraceEvents.isTraceEventPaint(t)){const e=this.#U.LayerTree.paintsToSnapshots.get(t);return Boolean(e)}return!!i.Types.TraceEvents.isTraceEventRasterTask(t)&&this.#W(t)}updateWhenVisible(){this.isShowing()?this.update():this.needsUpdateWhenVisible=!0}async#O(e){const t=e.args.tileData;if(!t)return null;if(!t.tileId.id_ref)return null;const i=n.TargetManager.TargetManager.instance().rootTarget();if(!i)return null;const r=this.#U.Frames.framesById[t.sourceFrameNumber];if(!r||!r.layerTree)return null;const a=new C.TracingLayerTree.TracingFrameLayerTree(i,r.layerTree),s=await a.layerTreePromise();return s?s.pictureForRasterTile(t.tileId.id_ref):null}update(){let e;if(this.logTreeView.setCommandLog([]),this.paintProfilerView.setSnapshotAndLog(null,[],null),this.pendingSnapshot)e=Promise.resolve({rect:null,snapshot:this.pendingSnapshot});else if(this.event&&this.paintProfilerModel&&i.Types.TraceEvents.isTraceEventPaint(this.event)){const t=this.#U.LayerTree.paintsToSnapshots.get(this.event);if(t){const i=t.args.snapshot.skp64;e=this.paintProfilerModel.loadSnapshot(i).then((e=>e&&{rect:null,snapshot:e}))}else e=Promise.resolve(null)}else{if(!this.event||!i.Types.TraceEvents.isTraceEventRasterTask(this.event))return void console.assert(!1,"Unexpected event type or no snapshot");e=this.#O(this.event)}function t(e,t,i){this.logTreeView.setCommandLog(i||[]),this.paintProfilerView.setSnapshotAndLog(e,i||[],t)}e.then((e=>{if(this.releaseSnapshot(),!e)return void this.imageView.showImage();const i=e.snapshot;this.lastLoadedSnapshot=i,this.imageView.setMask(e.rect),i.commandLog().then((n=>t.call(this,i,e.rect,n||[])))}))}releaseSnapshot(){this.lastLoadedSnapshot&&(this.lastLoadedSnapshot.release(),this.lastLoadedSnapshot=null)}onWindowChanged(){this.logTreeView.updateWindow(this.paintProfilerView.selectionWindow())}}class ri extends u.Widget.Widget{imageContainer;imageElement;maskElement;transformController;maskRectangle;constructor(){super(!0),this.contentElement.classList.add("fill","paint-profiler-image-view"),this.imageContainer=this.contentElement.createChild("div","paint-profiler-image-container"),this.imageElement=this.imageContainer.createChild("img"),this.maskElement=this.imageContainer.createChild("div"),this.imageElement.addEventListener("load",this.updateImagePosition.bind(this),!1),this.transformController=new I.TransformController.TransformController(this.contentElement,!0),this.transformController.addEventListener("TransformChanged",this.updateImagePosition,this)}onResize(){this.imageElement.src&&this.updateImagePosition()}updateImagePosition(){const e=this.imageElement.naturalWidth,t=this.imageElement.naturalHeight,i=this.contentElement.clientWidth,n=this.contentElement.clientHeight,r=.1*i,a=.1*n,s=(i-r)/e,o=(n-a)/t,l=Math.min(s,o);if(this.maskRectangle){const i=this.maskElement.style;i.width=e+"px",i.height=t+"px",i.borderLeftWidth=this.maskRectangle.x+"px",i.borderTopWidth=this.maskRectangle.y+"px",i.borderRightWidth=e-this.maskRectangle.x-this.maskRectangle.width+"px",i.borderBottomWidth=t-this.maskRectangle.y-this.maskRectangle.height+"px"}this.transformController.setScaleConstraints(.5,10/l);let c=(new WebKitCSSMatrix).scale(this.transformController.scale(),this.transformController.scale()).translate(i/2,n/2).scale(l,l).translate(-e/2,-t/2);const d=u.Geometry.boundsForTransformedPoints(c,[0,0,0,e,t,0]);this.transformController.clampOffsets(r-d.maxX,i-r-d.minX,a-d.maxY,n-a-d.minY),c=(new WebKitCSSMatrix).translate(this.transformController.offsetX(),this.transformController.offsetY()).multiply(c),this.imageContainer.style.webkitTransform=c.toString()}showImage(e){this.imageContainer.classList.toggle("hidden",!e),e&&(this.imageElement.src=e)}setMask(e){this.maskRectangle=e,this.maskElement.classList.toggle("hidden",!e)}wasShown(){super.wasShown(),this.registerCSSFiles([ii])}}var ai=Object.freeze({__proto__:null,TimelinePaintProfilerView:ni,TimelinePaintImageView:ri});const si={selectorStats:"Selector Stats",elapsed:"Elapsed (ms)",rejectPercentage:"% of Slow-Path Non-Matches",rejectPercentageExplanation:"The percentage of non-matching nodes (Match Attempts - Match Count) that couldn't be quickly ruled out by the bloom filter. Lower is better.",matchAttempts:"Match Attempts",matchCount:"Match Count",selector:"Selector",styleSheetId:"Style Sheet",copyTable:"Copy Table",unableToLink:"Unable to link",unableToLinkViaStyleSheetId:"Unable to link via {PH1}",tableCopiedToClipboard:"Table copied to clipboard",totalForAllSelectors:"(Totals for all selectors)",lineNumber:"Line {PH1}:{PH2}"},oi=e.i18n.registerUIStrings("panels/timeline/TimelineSelectorStatsView.ts",si),li=e.i18n.getLocalizedString.bind(void 0,oi);class ci extends u.Widget.VBox{#V;#_;#t=null;#z=null;constructor(e){super(),this.#V=new M.DataGridController.DataGridController,this.#_=new Map,this.#t=e,this.#V.data={label:li(si.selectorStats),showScrollbar:!0,autoScrollToBottom:!1,initialSort:{columnId:"elapsed (us)",direction:"DESC"},columns:[{id:"elapsed (us)",title:li(si.elapsed),sortable:!0,widthWeighting:1,visible:!0,hideable:!0,styles:{"text-align":"right"}},{id:"match_attempts",title:li(si.matchAttempts),sortable:!0,widthWeighting:1,visible:!0,hideable:!0,styles:{"text-align":"right"}},{id:"match_count",title:li(si.matchCount),sortable:!0,widthWeighting:1,visible:!0,hideable:!0,styles:{"text-align":"right"}},{id:"reject_percentage",title:li(si.rejectPercentage),titleElement:L.html`<span title=${li(si.rejectPercentageExplanation)}>${li(si.rejectPercentage)}</span>`,sortable:!0,widthWeighting:1,visible:!0,hideable:!0,styles:{"text-align":"right"}},{id:"selector",title:li(si.selector),sortable:!0,widthWeighting:3,visible:!0,hideable:!0},{id:"style_sheet_id",title:li(si.styleSheetId),sortable:!0,widthWeighting:1.5,visible:!0,hideable:!0}],rows:[],contextMenus:{bodyRow:(e,t,i,r)=>{e.defaultSection().appendItem(li(si.copyTable),(()=>{const e=[],i=t.map((e=>e.title));e.push(i.join("\t"));for(const t of r){const i=t.cells.map((e=>{if("style_sheet_id"===e.columnId){const t=li(si.unableToLink);let i="";const r=n.TargetManager.TargetManager.instance().primaryPageTarget(),a=r?.model(n.CSSModel.CSSModel);if(a){const t=a.styleSheetHeaderForId(e.value);t&&(i=t.resourceURL().toString())}return i?i.toString():t}return String(e.value)}));e.push(i.join("\t"))}const a=e.join("\n");navigator.clipboard.writeText(a),u.ARIAUtils.alert(li(si.tableCopiedToClipboard))}))}}},this.contentElement.appendChild(this.#V)}setEvent(e){if(!this.#t)return!1;if(this.#z===e)return!1;this.#z=e;const t=this.#t.SelectorStats.dataForUpdateLayoutEvent.get(e);if(!t)return this.#V.data={...this.#V.data,rows:[]},!1;const i=t.timings;return this.createRowsForTable(i).then((e=>{this.#V.data={...this.#V.data,rows:e}})),!0}setAggregatedEvents(e){const t=[],i=new Map;if(!this.#t)return;const n={"elapsed (us)":0,match_attempts:0,match_count:0,fast_reject_count:0};if(!Array.isArray(this.#z)||this.#z.length!==e.length||!e.every(((e,t)=>e===this.#z[t]))){this.#z=e;for(let t=0;t<e.length;t++){const r=e[t],a=r?this.#t.SelectorStats.dataForUpdateLayoutEvent.get(r):void 0;if(a){const e=a.timings;for(const t of e){const e=t.selector+"_"+t.style_sheet_id,r=i.get(e);void 0!==r?(r["elapsed (us)"]+=t["elapsed (us)"],r.fast_reject_count+=t.fast_reject_count,r.match_attempts+=t.match_attempts,r.match_count+=t.match_count):i.set(e,structuredClone(t)),n["elapsed (us)"]+=t["elapsed (us)"],n.match_attempts+=t.match_attempts,n.match_count+=t.match_count,n.fast_reject_count+=t.fast_reject_count}}}i.size>0?(i.forEach((e=>{t.push(e)})),i.clear(),t.unshift({"elapsed (us)":n["elapsed (us)"],fast_reject_count:n.fast_reject_count,match_attempts:n.match_attempts,match_count:n.match_count,selector:li(si.totalForAllSelectors),style_sheet_id:"n/a"}),this.createRowsForTable(t).then((e=>{this.#V.data={...this.#V.data,rows:e}}))):this.#V.data={...this.#V.data,rows:[]}}}async createRowsForTable(e){const t=n.TargetManager.TargetManager.instance().primaryPageTarget(),i=t?.model(n.CSSModel.CSSModel);if(!i)return[];const r=await Promise.all(e.map((async e=>{const t=e.style_sheet_id,n=e.selector.trim(),r=e["elapsed (us)"]/1e3,a=e.match_attempts-e.match_count,s=100*(a?e.fast_reject_count/a:1),o="n/a"===t?null:await async function(e,t,i,n){if(!e)return;const r=e.styleSheetHeaderForId(t);if(!r||!r.resourceURL())return;const a=JSON.stringify({selectorText:i,styleSheetId:t});let s=n.get(a);if(!s){const r=await e.agent.invoke_getLocationForSelector({styleSheetId:t,selectorText:i});if(r.getError()||!r.ranges)return;s=r.ranges,n.set(a,s)}return s.map((e=>({url:r.resourceURL(),lineNumber:e.startLine,columnNumber:e.startColumn,linkText:li(si.lineNumber,{PH1:e.startLine+1,PH2:e.startColumn+1})})))}(i,t,n,this.#_);return{cells:[{columnId:"elapsed (us)",value:r,renderer:()=>L.html`${r.toFixed(3)}`},{columnId:"reject_percentage",value:s,renderer:()=>L.html`${s.toFixed(1)}`},{columnId:"match_attempts",value:e.match_attempts},{columnId:"match_count",value:e.match_count},{columnId:"selector",title:e.selector,value:e.selector},{columnId:"style_sheet_id",value:e.style_sheet_id,renderer:()=>null===o?L.html`<span></span>`:void 0===o?L.html`<span title=${li(si.unableToLinkViaStyleSheetId,{PH1:e.style_sheet_id})} aria-label=${li(si.unableToLinkViaStyleSheetId,{PH1:e.style_sheet_id})}>${li(si.unableToLink)}</span>`:L.html`
              ${o.map(((e,t)=>t!==o.length-1?L.html`<${F.Linkifier.Linkifier.litTagName} .data=${e}></${F.Linkifier.Linkifier.litTagName}>
                    <a>, </a>`:L.html`<${F.Linkifier.Linkifier.litTagName} .data=${e}></${F.Linkifier.Linkifier.litTagName}>`))}
              `}]}})));return r}}const di={summary:"Summary",bottomup:"Bottom-Up",callTree:"Call Tree",eventLog:"Event Log",paintProfiler:"Paint Profiler",layers:"Layers",rangeSS:"Range:  {PH1} – {PH2}",selectorStats:"Selector Stats"},hi=e.i18n.registerUIStrings("panels/timeline/TimelineDetailsView.ts",di),mi=e.i18n.getLocalizedString.bind(void 0,hi);class pi extends u.Widget.VBox{detailsLinkifier;tabbedPane;defaultDetailsWidget;defaultDetailsContentElement;rangeDetailViews;#D;lazyPaintProfilerView;lazyLayersView;preferredTabId;selection;updateContentsScheduled;lazySelectorStatsView;#U=null;#G=null;#j;#x=this.#P.bind(this);constructor(e){super(),this.element.classList.add("timeline-details"),this.detailsLinkifier=new E.Linkifier.Linkifier,this.tabbedPane=new u.TabbedPane.TabbedPane,this.tabbedPane.show(this.element),this.defaultDetailsWidget=new u.Widget.VBox,this.defaultDetailsWidget.element.classList.add("timeline-details-view"),this.defaultDetailsContentElement=this.defaultDetailsWidget.element.createChild("div","timeline-details-view-body vbox"),this.appendTab(ui.Details,mi(di.summary),this.defaultDetailsWidget),this.setPreferredTab(ui.Details),this.rangeDetailViews=new Map,this.updateContentsScheduled=!1;const t=new jt;this.appendTab(ui.BottomUp,mi(di.bottomup),t),this.rangeDetailViews.set(ui.BottomUp,t);const i=new Gt;this.appendTab(ui.CallTree,mi(di.callTree),i),this.rangeDetailViews.set(ui.CallTree,i);const n=new Xt(e);this.appendTab(ui.EventLog,mi(di.eventLog),n),this.rangeDetailViews.set(ui.EventLog,n),this.#j=new s.NetworkRequestDetails.NetworkRequestDetails(this.detailsLinkifier),this.tabbedPane.addEventListener(u.TabbedPane.Events.TabSelected,this.tabSelected,this),h.TraceBounds.onChange(this.#x),this.lazySelectorStatsView=null}selectorStatsView(){return this.lazySelectorStatsView||(this.lazySelectorStatsView=new ci(this.#U)),this.lazySelectorStatsView}getDetailsContentElementForTest(){return this.defaultDetailsContentElement}async#P(e){"MINIMAP_BOUNDS"===e.updateType&&this.selection&&await this.setSelection(this.selection),"RESET"!==e.updateType&&"VISIBLE_WINDOW"!==e.updateType||this.selection||this.scheduleUpdateContentsFromWindow()}async setModel(e,t){this.#U!==e&&(this.lazySelectorStatsView=null,this.#U=e),e&&(this.#G=i.Extras.FilmStrip.fromTraceData(e)),this.#D=t,this.tabbedPane.closeTabs([ui.PaintProfiler,ui.LayerViewer],!1);for(const i of this.rangeDetailViews.values())i.setModelWithEvents(t,e);this.lazyPaintProfilerView=null,this.lazyLayersView=null,await this.setSelection(null)}setContent(e){const t=this.tabbedPane.otherTabs(ui.Details);for(let e=0;e<t.length;++e)this.rangeDetailViews.has(t[e])||this.tabbedPane.closeTab(t[e]);this.defaultDetailsContentElement.removeChildren(),this.defaultDetailsContentElement.appendChild(e)}updateContents(){const e=this.rangeDetailViews.get(this.tabbedPane.selectedTabId||"");if(e){const t=h.TraceBounds.BoundsManager.instance().state();if(!t)return;const i=t.milli.timelineTraceWindow;e.updateContents(this.selection||vt.fromRange(i.min,i.max))}}appendTab(e,t,i,n){this.tabbedPane.appendTab(e,t,i,void 0,void 0,n),this.preferredTabId!==this.tabbedPane.selectedTabId&&this.tabbedPane.selectTab(e)}headerElement(){return this.tabbedPane.headerElement()}setPreferredTab(e){this.preferredTabId=e}scheduleUpdateContentsFromWindow(e=!1){this.#U?e?this.updateContentsFromWindow():this.updateContentsScheduled||(this.updateContentsScheduled=!0,setTimeout((()=>{this.updateContentsScheduled=!1,this.updateContentsFromWindow()}),100)):this.setContent(u.Fragment.html`<div/>`)}updateContentsFromWindow(){const e=h.TraceBounds.BoundsManager.instance().state();if(!e)return;const t=e.milli.timelineTraceWindow;this.updateSelectedRangeStats(t.min,t.max),this.updateContents()}#q(e){if(!this.#G)return null;const t=e.idle?e.startTime:e.endTime,n=i.Extras.FilmStrip.frameClosestToTimestamp(this.#G,t);if(!n)return null;return i.Helpers.Timing.microSecondsToMilliseconds(n.screenshotEvent.ts)-e.endTime<10?n:null}async setSelection(e){if(!this.#U)return;if(this.detailsLinkifier.reset(),this.selection=e,!this.selection)return void this.scheduleUpdateContentsFromWindow(!0);const t=this.selection.object;if(vt.isSyntheticNetworkRequestDetailsEventSelection(t)){const e=t,i=rt(this.#U,e);await this.#j.setData(e,i),this.setContent(this.#j)}else if(vt.isTraceEventSelection(t)){const e=t,i=await Ct.buildTraceEventDetails(this.#U,e,this.detailsLinkifier,!0);this.appendDetailsTabsForTraceEventAndShowDetails(e,i)}else if(vt.isFrameObject(t)){const e=t,i=this.#q(e);this.setContent(Ct.generateDetailsContentForFrame(e,this.#G,i));const r=n.TargetManager.TargetManager.instance().rootTarget();if(e.layerTree&&r){const t=new C.TracingLayerTree.TracingFrameLayerTree(r,e.layerTree),i=this.layersView();i.showLayerTree(t),this.tabbedPane.hasTab(ui.LayerViewer)||this.appendTab(ui.LayerViewer,mi(di.layers),i)}}else vt.isRangeSelection(t)&&this.updateSelectedRangeStats(this.selection.startTime,this.selection.endTime);this.updateContents()}tabSelected(e){e.data.isUserGesture&&(this.setPreferredTab(e.data.tabId),this.updateContents())}layersView(){return this.lazyLayersView||(this.lazyLayersView=new ei(this.showSnapshotInPaintProfiler.bind(this))),this.lazyLayersView}paintProfilerView(){return this.lazyPaintProfilerView?this.lazyPaintProfilerView:this.#U?(this.lazyPaintProfilerView=new ni(this.#U),this.lazyPaintProfilerView):null}showSnapshotInPaintProfiler(e){const t=this.paintProfilerView();t&&(t.setSnapshot(e),this.tabbedPane.hasTab(ui.PaintProfiler)||this.appendTab(ui.PaintProfiler,mi(di.paintProfiler),t,!0),this.tabbedPane.selectTab(ui.PaintProfiler,!0))}showSelectorStatsForIndividualEvent(e){this.showAggregatedSelectorStats([e])}showAggregatedSelectorStats(e){const t=this.selectorStatsView();t.setAggregatedEvents(e),this.tabbedPane.hasTab(ui.SelectorStats)||this.appendTab(ui.SelectorStats,mi(di.selectorStats),t)}appendDetailsTabsForTraceEventAndShowDetails(e,t){this.setContent(t),(i.Types.TraceEvents.isTraceEventPaint(e)||i.Types.TraceEvents.isTraceEventRasterTask(e))&&this.showEventInPaintProfiler(e),i.Types.TraceEvents.isTraceEventUpdateLayoutTree(e)&&this.showSelectorStatsForIndividualEvent(e)}showEventInPaintProfiler(e){const t=n.TargetManager.TargetManager.instance().models(n.PaintProfiler.PaintProfilerModel)[0];if(!t)return;const i=this.paintProfilerView();if(!i)return;i.setEvent(t,e)&&(this.tabbedPane.hasTab(ui.PaintProfiler)||this.appendTab(ui.PaintProfiler,mi(di.paintProfiler),i))}updateSelectedRangeStats(t,n){if(!this.#D||!this.#U)return;const a=i.Helpers.Timing.traceWindowMilliSeconds(this.#U.Meta.traceBounds).min,s=Ct.statsForTimeRange(this.#D,t,n),o=t-a,l=n-a,c=new Pt(null,null);c.addSection(mi(di.rangeSS,{PH1:e.TimeUtilities.millisToString(o),PH2:e.TimeUtilities.millisToString(l)}));const d=Ct.generatePieChart(s);c.appendElementRow("",d),this.setContent(c.fragment);const h=r.Settings.Settings.instance().createSetting("timeline-capture-selector-stats",!1).get();if(this.#D&&h){const e=i.Helpers.Trace.findUpdateLayoutTreeEvents(this.#D,i.Helpers.Timing.millisecondsToMicroseconds(t),i.Helpers.Timing.millisecondsToMicroseconds(n));e.length>0&&this.showAggregatedSelectorStats(e)}}}var ui;!function(e){e.Details="details",e.EventLog="event-log",e.CallTree="call-tree",e.BottomUp="bottom-up",e.PaintProfiler="paint-profiler",e.LayerViewer="layer-viewer",e.SelectorStats="selector-stats"}(ui||(ui={}));var gi=Object.freeze({__proto__:null,TimelineDetailsView:pi,get Tab(){return ui}});function vi(e,t,i,n){const r=[...fi(e,t,e.Initiators.eventToInitiator),...yi(t,e.Initiators.initiatorToEvents)];return r.forEach((t=>function(e,t,i,n){if(i.includes(e.event)){let i=n.Renderer.entryToNode.get(e.event)?.parent;for(;i?.entry&&!t.includes(i?.entry);)i=i.parent??void 0;e.event=i?.entry??e.event,e.isEntryHidden=!0}if(i.includes(e.initiator)){let i=n.Renderer.entryToNode.get(e.initiator)?.parent;for(;i?.entry&&!t.includes(i?.entry);)i=i.parent??void 0;e.initiator=i?.entry??e.initiator,e.isInitiatorHidden=!0}return e}(t,n,i,e))),r}function Ti(e,t){return fi(e,t,e.NetworkRequests.eventToInitiator)}function fi(e,t,n){const r=[];let a=t;for(;a;){const t=n.get(a);if(t){r.push({event:a,initiator:t}),a=t;continue}if(!i.Types.TraceEvents.isSyntheticTraceEntry(a)){a=null;break}const s=e.Renderer.entryToNode.get(a);if(!s){a=null;break}a=s.parent?.entry||null}return r}function yi(e,t){const i=[],n=t.get(e);return n&&n.forEach((t=>{i.push({event:t,initiator:e})})),i}var wi=Object.freeze({__proto__:null,initiatorsDataToDraw:vi,initiatorsDataToDrawForNetwork:Ti});const bi={onIgnoreList:"On ignore list",mainS:"Main — {PH1}",main:"Main",frameS:"Frame — {PH1}",workerS:"`Worker` — {PH1}",workerSS:"`Worker`: {PH1} — {PH2}",dedicatedWorker:"Dedicated `Worker`",anonymous:"(anonymous)",threadS:"Thread {PH1}",raster:"Raster",threadPool:"Thread Pool",rasterizerThreadS:"Rasterizer Thread {PH1}",threadPoolThreadS:"Thread Pool Worker {PH1}",bidderWorkletS:"Bidder Worklet — {PH1}",bidderWorklet:"Bidder Worklet",sellerWorklet:"Seller Worklet",unknownWorklet:"Auction Worklet",workletService:"Auction Worklet Service",sellerWorkletS:"Seller Worklet — {PH1}",unknownWorkletS:"Auction Worklet — {PH1}",workletServiceS:"Auction Worklet Service — {PH1}",eventDispatchS:"Event: {PH1}"},Si=e.i18n.registerUIStrings("panels/timeline/ThreadAppender.ts",bi),Ci=e.i18n.getLocalizedString.bind(void 0,Si);class Ei{appenderName="Thread";#M;#e;#t;#$=[];#J;#K;#Y;#X;#Z=!1;#Q=!1;threadType="MAIN_THREAD";isOnMainFrame;#ee=a.Runtime.experiments.isEnabled("timeline-show-all-events");#te="";#ie=null;constructor(e,t,i,n,a,s){this.#e=e,this.#M=new r.Color.Generator({min:30,max:330,count:void 0},{min:50,max:80,count:3},85),this.#M.setColorForID("","#f2ecdc"),this.#t=t,this.#K=i,this.#Y=n;const o="CPU_PROFILE"===s?this.#t.Samples?.profilesInProcess.get(i)?.get(n)?.profileCalls:this.#t.Renderer?.processes.get(i)?.threads?.get(n)?.entries,l="CPU_PROFILE"===s?this.#t.Samples?.profilesInProcess.get(i)?.get(n)?.profileTree:this.#t.Renderer?.processes.get(i)?.threads?.get(n)?.tree;if(!o||!l)throw new Error(`Could not find data for thread with id ${n} in process with id ${i}`);this.#$=o,this.#J=l,this.#X=a||Ci(bi.threadS,{PH1:n}),this.isOnMainFrame=Boolean(this.#t.Renderer?.processes.get(i)?.isOnMainFrame),this.threadType=s,this.#t.AuctionWorklets.worklets.has(i)&&(this.appenderName="Thread_AuctionWorklet"),this.#te=this.#t.Renderer?.processes.get(this.#K)?.url||""}processId(){return this.#K}threadId(){return this.#Y}appendTrackAtLevel(e,t=!1){return 0===this.#$.length?e:(this.#Z=t,this.#ne(e))}setHeaderNestingLevel(e){this.#ie=e}#re(e){this.#Q||("RASTERIZER"===this.threadType||"THREAD_POOL"===this.threadType?this.#ae(e,this.threadType):this.#i(e),this.#Q=!0)}setHeaderAppended(e){this.#Q=e}headerAppended(){return this.#Q}#i(e){const t=U({shareHeaderLine:!1,collapsible:this.#$.length>0});null!==this.#ie&&(t.nestingLevel=this.#ie);const i=W(this.#se(),e,this.trackName(),t,!0,this.#Z,!0);this.#e.registerTrackForGroup(i,this)}#se(){switch(this.threadType){case"MAIN_THREAD":return this.isOnMainFrame?"thread.main":"thread.frame";case"WORKER":return"thread.worker";case"RASTERIZER":return"thread.rasterizer";case"AUCTION_WORKLET":return"thread.auction-worklet";case"OTHER":return"thread.other";case"CPU_PROFILE":return"thread.cpu-profile";case"THREAD_POOL":return"thread.pool";default:return null}}#ae(e,t){const i=this.#e.getCurrentTrackCountForThreadType(t);if(0===i){const t=U({shareHeaderLine:!1,collapsible:this.#$.length>0}),i=W(null,e,this.trackName(),t,!1,this.#Z);this.#e.getFlameChartTimelineData().groups.push(i)}const n=U({padding:2,nestingLevel:1,collapsible:!1}),r="RASTERIZER"===this.threadType?Ci(bi.rasterizerThreadS,{PH1:i+1}):Ci(bi.threadPoolThreadS,{PH1:i+1}),a=W(this.#se(),e,r,n,!0,this.#Z);this.#e.registerTrackForGroup(a,this)}trackName(){let e=null;switch(this.threadType){case"MAIN_THREAD":e=this.isOnMainFrame?Ci(bi.mainS,{PH1:this.#te}):Ci(bi.frameS,{PH1:this.#te});break;case"CPU_PROFILE":e=Ci(bi.main);break;case"WORKER":e=this.#oe();break;case"RASTERIZER":e=Ci(bi.raster);break;case"THREAD_POOL":e=Ci(bi.threadPool);break;case"OTHER":break;case"AUCTION_WORKLET":e=this.#le();break;default:return c.assertNever(this.threadType,`Unknown thread type: ${this.threadType}`)}let t="";return this.#t.Meta.traceIsGeneric&&(t+=` (${this.threadId()})`),(e||this.#X)+t}getUrl(){return this.#te}getEntries(){return this.#$}#le(){const e=this.#t.AuctionWorklets.worklets.get(this.#K);if(!e)return Ci(bi.unknownWorklet);const t=e.host?`https://${e.host}`:"",i=t.length>0,n=e.args.data.utilityThread.tid===this.#Y,r=e.args.data.v8HelperThread.tid===this.#Y;if(n)return i?Ci(bi.workletServiceS,{PH1:t}):Ci(bi.workletService);if(r)switch(e.type){case"seller":return i?Ci(bi.sellerWorkletS,{PH1:t}):Ci(bi.sellerWorklet);case"bidder":return i?Ci(bi.bidderWorkletS,{PH1:t}):Ci(bi.bidderWorklet);case"unknown":return i?Ci(bi.unknownWorkletS,{PH1:t}):Ci(bi.unknownWorklet);default:c.assertNever(e.type,`Unexpected Auction Worklet Type ${e.type}`)}return i?Ci(bi.unknownWorkletS,{PH1:t}):Ci(bi.unknownWorklet)}#oe(){const e=this.#t.Renderer?.processes.get(this.#K)?.url||"",t=this.#t.Workers.workerIdByThread.get(this.#Y),i=t?this.#t.Workers.workerURLById.get(t):e;let r=i?Ci(bi.workerS,{PH1:i}):Ci(bi.dedicatedWorker);const a=void 0!==t&&n.TargetManager.TargetManager.instance().targetById(t);return a&&(r=Ci(bi.workerSS,{PH1:a.name(),PH2:e})),r}#ne(e){return this.#ce(this.#J.roots,e)}#ce(e,t,i=!1){const n=Ne.activeManager()?.getEntriesFilter().invisibleEntries()??[];let r=t;for(const a of e){let e=t;const s=a.entry,o=this.isIgnoreListedEntry(s);!n.includes(s)&&(this.#e.entryIsVisibleInTimeline(s)||this.#ee)&&!(o&&i)&&(this.#de(s,t),e++);const l=this.#ce(a.children,e,o);r=Math.max(l,r)}return r}#de(e,t){this.#re(t);const i=this.#e.appendEventAtLevel(e,t,this);this.#he(e,i)}#he(e,t){const n=this.#e.getFlameChartTimelineData();Ne.activeManager()?.getEntriesFilter().isEntryExpandable(e)&&_(n,t,{type:"HIDDEN_DESCENDANTS_ARROW"});const r=this.#t.Warnings.perEvent.get(e);r&&(_(n,t,{type:"WARNING_TRIANGLE"}),r.includes("LONG_TASK")&&_(n,t,{type:"CANDY",startAtTime:i.Handlers.ModelHandlers.Warnings.LONG_MAIN_THREAD_TASK_THRESHOLD}))}isIgnoreListedEntry(e){if(!i.Types.TraceEvents.isProfileCall(e))return!1;const t=e.callFrame.url;return t&&this.isIgnoreListedURL(t)}isIgnoreListedURL(e){return b.IgnoreListManager.IgnoreListManager.instance().isUserIgnoreListedURL(e)}colorForEvent(e){if(this.#t.Meta.traceIsGeneric)return e.name?`hsl(${c.StringUtilities.hashCode(e.name)%300+30}, 40%, 70%)`:"#ccc";if(i.Types.TraceEvents.isProfileCall(e))return"(idle)"===e.callFrame.functionName?me().idle.getComputedColorValue():"0"===e.callFrame.scriptId?me().scripting.getComputedColorValue():this.#M.colorForID(e.callFrame.url);const t=de(e.name)?.category.getComputedColorValue();return t||me().other.getComputedColorValue()}titleForEvent(e){if(this.isIgnoreListedEntry(e))return Ci(bi.onIgnoreList);if(i.Types.TraceEvents.isProfileCall(e)){if(this.#t.Samples){const t=i.Handlers.ModelHandlers.Samples.getProfileCallFunctionName(this.#t.Samples,e);if(t)return t}return e.callFrame.functionName||Ci(bi.anonymous)}if(i.Types.TraceEvents.isTraceEventDispatch(e))return Ci(bi.eventDispatchS,{PH1:e.args.data.type});const t=de(e.name)?.title;return t||e.name}highlightedEntryInfo(e){let t=this.titleForEvent(e);if(i.Types.TraceEvents.isTraceEventParseHTML(e)){const i=e.args.beginData.startLine,n=e.args.endData&&e.args.endData.endLine,r=e.args.beginData.url;t+=` - ${b.ResourceUtils.displayNameForURL(r)} [${-1!==n||n===i?`${i}...${n}`:i}]`}return{title:t,formattedTime:O(e.dur,e.selfTime)}}}var ki=Object.freeze({__proto__:null,ThreadAppender:Ei});const xi=new CSSStyleSheet;xi.replaceSync(".timeline-flamechart-popover{overflow:hidden}.timeline-flamechart-popover devtools-interaction-breakdown{margin-top:10px}.timeline-flamechart-popover span{margin-right:5px}.timeline-flamechart-popover span.timeline-info-network-time{color:var(--sys-color-primary)}.timeline-flamechart-popover span.timeline-info-time{color:var(--sys-color-green)}.timeline-flamechart-popover span.timeline-info-warning{color:var(--sys-color-error)}.timeline-flamechart-popover span.timeline-info-warning *{color:inherit}\n/*# sourceURL=timelineFlamechartPopover.css */\n");const Pi={frames:"Frames",idleFrame:"Idle Frame",droppedFrame:"Dropped Frame",partiallyPresentedFrame:"Partially Presented Frame",frame:"Frame"},Ii=e.i18n.registerUIStrings("panels/timeline/TimelineFlameChartDataProvider.ts",Pi),Mi=e.i18n.getLocalizedString.bind(void 0,Ii);class Fi extends r.ObjectWrapper.ObjectWrapper{isReactNative=!1;droppedFramePatternCanvas;partialFramePatternCanvas;timelineDataInternal;currentLevel;compatibilityTracksAppender;traceEngineData;isCpuProfile=!1;minimumBoundaryInternal;timeSpan;headerLevel1;headerLevel2;staticHeader;framesHeader;screenshotsHeader;entryData;entryTypeByLevel;screenshotImageCache;entryIndexToTitle;lastInitiatorEntry;lastInitiatorsData=[];lastSelection;#me;#pe=new WeakMap;constructor(){super(),this.isReactNative=a.Runtime.experiments.isEnabled("react-native-specific-ui"),this.reset(),this.#me=`${p.Font.DEFAULT_FONT_SIZE} ${p.Font.getFontFamilyForCanvas()}`,this.droppedFramePatternCanvas=document.createElement("canvas"),this.partialFramePatternCanvas=document.createElement("canvas"),this.preparePatternCanvas(),this.timelineDataInternal=null,this.currentLevel=0,this.compatibilityTracksAppender=null,this.traceEngineData=null,this.minimumBoundaryInternal=0,this.timeSpan=0,this.headerLevel1=this.buildGroupStyle({shareHeaderLine:!1}),this.headerLevel2=this.buildGroupStyle({padding:2,nestingLevel:1,collapsible:!1}),this.staticHeader=this.buildGroupStyle({collapsible:!1}),this.framesHeader=this.buildGroupStyle({useFirstLineForOverview:!0}),this.screenshotsHeader=this.buildGroupStyle({useFirstLineForOverview:!0,nestingLevel:1,collapsible:!1,itemsHeight:150}),t.ThemeSupport.instance().addEventListener(t.ThemeChangeEvent.eventName,(()=>{const e=[this.headerLevel1,this.headerLevel2,this.staticHeader,this.framesHeader,this.screenshotsHeader];for(const i of e)i.color=t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),i.backgroundColor=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container")}))}hasTrackConfigurationMode(){return!0}modifyTree(e,t){const i=this.entryData[e];Ne.activeManager()?.getEntriesFilter().applyFilterAction({type:t,entry:i})}findPossibleContextMenuActions(e){const t=this.entryData[e];return Ne.activeManager()?.getEntriesFilter().findPossibleActions(t)}buildGroupStyle(e){const i={padding:4,height:17,collapsible:!0,color:t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),backgroundColor:t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),nestingLevel:0,shareHeaderLine:!0};return Object.assign(i,e)}setModel(e,t=!1){if(this.reset(),this.traceEngineData=e,cr.instance().modelChanged(e),this.isCpuProfile=t,e){const{traceBounds:t}=e.Meta,n=i.Helpers.Timing.microSecondsToMilliseconds(t.min),r=i.Helpers.Timing.microSecondsToMilliseconds(t.max);this.minimumBoundaryInternal=n,this.timeSpan=n===r?1e3:r-this.minimumBoundaryInternal}}compatibilityTracksAppenderInstance(e=!1){if(!this.compatibilityTracksAppender||e){if(!this.traceEngineData)throw new Error("Attempted to instantiate a CompatibilityTracksAppender without having set the trace parse data first.");this.timelineDataInternal=this.#ue(),this.compatibilityTracksAppender=new Dr(this.timelineDataInternal,this.traceEngineData,this.entryData,this.entryTypeByLevel)}return this.compatibilityTracksAppender}#ue(){return this.timelineDataInternal||(this.timelineDataInternal=p.FlameChart.FlameChartTimelineData.createEmpty()),this.timelineDataInternal}buildFromTrackAppenders(e){if(!this.compatibilityTracksAppender)return;const t=this.compatibilityTracksAppender.allVisibleTrackAppenders();for(const i of t){if(i instanceof Ei&&!i.trackName().includes(e?.filterThreadsByName||""))continue;const t=Boolean(e?.expandedTracks?.has(i.appenderName));this.currentLevel=i.appendTrackAtLevel(this.currentLevel,t)}}groupTreeEvents(e){return this.compatibilityTracksAppender?.groupEventsForTreeView(e)??null}mainFrameNavigationStartEvents(){return this.traceEngineData?this.traceEngineData.Meta.mainFrameNavigations:[]}entryTitle(e){const t=this.#ge(e);if("Screenshot"===t)return"";if("TrackAppender"===t){const t=this.timelineDataInternal.entryLevels[e],i=this.entryData[e];return this.compatibilityTracksAppender?.titleForEvent(i,t)||null}let i=this.entryIndexToTitle[e];return i||(i=`Unexpected entryIndex ${e}`,console.error(i)),i}textColor(e){const t=this.entryData[e];return Fi.timelineEntryIsTraceEvent(t)&&this.isIgnoreListedEvent(t)?"#888":Ki.textColor}entryFont(e){return this.#me}reset(e=!0){this.currentLevel=0,this.entryData=[],this.entryTypeByLevel=[],this.entryIndexToTitle=[],this.screenshotImageCache=new Map,this.#pe=new Map,e?(this.compatibilityTracksAppender=null,this.timelineDataInternal=null):!e&&this.timelineDataInternal&&(this.compatibilityTracksAppender?.setFlameChartDataAndEntryData(this.timelineDataInternal,this.entryData,this.entryTypeByLevel),this.compatibilityTracksAppender?.threadAppenders().forEach((e=>e.setHeaderAppended(!1))))}maxStackDepth(){return this.currentLevel}timelineData(e=!1){return this.timelineDataInternal&&0!==this.timelineDataInternal.entryLevels.length&&!e||(this.timelineDataInternal=p.FlameChart.FlameChartTimelineData.createEmpty(),e&&this.reset(!1),this.currentLevel=0,this.traceEngineData&&(this.compatibilityTracksAppender=this.compatibilityTracksAppenderInstance(),this.traceEngineData.Meta.traceIsGeneric?this.#ve():this.#Te())),this.timelineDataInternal}#ve(){if(!this.compatibilityTracksAppender)return;const e=this.compatibilityTracksAppender.allThreadAppendersByProcess();for(const[t,i]of e){const e=this.buildGroupStyle({shareHeaderLine:!1}),n=this.traceEngineData?.Meta.processNames.get(t)?.args.name||"Process";this.appendHeader(`${n} (${t})`,e,!0,!1);for(const e of i)e.setHeaderNestingLevel(1),this.currentLevel=e.appendTrackAtLevel(this.currentLevel)}}#Te(){this.isCpuProfile||this.isReactNative||this.#fe();const e=e=>{switch(e.appenderName){case"Animations":return 0;case"Timings":return 1;case"Interactions":return 2;case"LayoutShifts":return 3;case"Extension":return 4;case"Thread":return 5;case"GPU":return 6;case"Thread_AuctionWorklet":return 7;default:return 8}},t=this.compatibilityTracksAppender?this.compatibilityTracksAppender.allVisibleTrackAppenders():[];t.sort(((t,i)=>e(t)-e(i)));for(const e of t)if(this.traceEngineData&&(this.currentLevel=e.appendTrackAtLevel(this.currentLevel),this.timelineDataInternal&&!this.timelineDataInternal.selectedGroup&&e instanceof Ei&&("MAIN_THREAD"===e.threadType||"CPU_PROFILE"===e.threadType))){const t=this.compatibilityTracksAppender?.groupForAppender(e);t&&(this.timelineDataInternal.selectedGroup=t)}this.timelineDataInternal&&this.timelineDataInternal.selectedGroup&&(this.timelineDataInternal.selectedGroup.expanded=!0)}minimumBoundary(){return this.minimumBoundaryInternal}totalTime(){return this.timeSpan}static timelineEntryIsTraceEvent(e){return e instanceof i.Handlers.ModelHandlers.Frames.TimelineFrame==!1}search(e,t,n){const r=[];this.timelineData();for(let a=0;a<this.entryData.length;++a){const s=this.entryData[a];if(!s)continue;if(!Fi.timelineEntryIsTraceEvent(s))continue;if(i.Types.TraceEvents.isTraceEventScreenshot(s))continue;const o=i.Helpers.Timing.eventTimingsMilliSeconds(s).startTime,l=i.Helpers.Timing.eventTimingsMilliSeconds(s).endTime;o>t||((l||o)<e||n.accept(s,this.traceEngineData||void 0)&&r.push(a))}return r.sort(((e,t)=>{const n=this.entryData.at(e);if(!n)return 0;const r=this.entryData.at(t);return r&&Fi.timelineEntryIsTraceEvent(n)&&Fi.timelineEntryIsTraceEvent(r)?i.Helpers.Trace.eventTimeComparator(n,r):0})),r}isIgnoreListedEvent(e){return!!i.Types.TraceEvents.isProfileCall(e)&&this.isIgnoreListedURL(e.callFrame.url)}isIgnoreListedURL(e){return b.IgnoreListManager.IgnoreListManager.instance().isUserIgnoreListedURL(e)}getEntryTypeForLevel(e){return this.entryTypeByLevel[e]}#fe(){if(!this.traceEngineData)return;const e=i.Extras.FilmStrip.fromTraceData(this.traceEngineData),t=e.frames.length>0;this.framesHeader.collapsible=t;const n="frames"===a.Runtime.Runtime.queryParam("flamechart-force-expand");this.appendHeader(Mi(Pi.frames),this.framesHeader,!1,n),this.entryTypeByLevel[this.currentLevel]="Frame";for(const e of this.traceEngineData.Frames.frames)this.#ye(e);++this.currentLevel,t&&this.#we(e)}#we(e){if(!this.timelineDataInternal||!this.traceEngineData)return;let t;this.appendHeader("",this.screenshotsHeader,!1),this.entryTypeByLevel[this.currentLevel]="Screenshot";for(const n of e.frames){const e=i.Helpers.Timing.microSecondsToMilliseconds(n.screenshotEvent.ts);this.entryData.push(n.screenshotEvent),this.timelineDataInternal.entryLevels.push(this.currentLevel),this.timelineDataInternal.entryStartTimes.push(e),t&&this.timelineDataInternal.entryTotalTimes.push(e-t),t=e}if(e.frames.length&&void 0!==t){const e=i.Helpers.Timing.traceWindowMilliSeconds(this.traceEngineData.Meta.traceBounds).max;this.timelineDataInternal.entryTotalTimes.push(e-t)}++this.currentLevel}#ge(e){const t=this.timelineData().entryLevels[e];return this.entryTypeByLevel[t]}prepareHighlightedEntryInfo(t){let n,r="",a=[],o="timeline-info-time";const l=[],c=this.#ge(t);if("TrackAppender"===c){if(!this.compatibilityTracksAppender)return null;const e=this.entryData[t],o=this.timelineDataInternal.entryLevels[t],c=this.compatibilityTracksAppender.highlightedEntryInfo(e,o);if(n=c.title,r=c.formattedTime,a=c.warningElements||a,i.Types.TraceEvents.isSyntheticInteractionEvent(e)){const t=new s.InteractionBreakdown.InteractionBreakdown;t.entry=e,l.push(t)}}else{if("Frame"!==c)return null;{const a=this.entryData[t];r=e.TimeUtilities.preciseMillisToString(i.Helpers.Timing.microSecondsToMilliseconds(a.duration),1),a.idle?n=Mi(Pi.idleFrame):a.dropped?(n=a.isPartial?Mi(Pi.partiallyPresentedFrame):Mi(Pi.droppedFrame),o="timeline-info-warning"):n=Mi(Pi.frame)}}const d=document.createElement("div"),h=u.UIUtils.createShadowRootWithCoreStyles(d,{cssFile:[xi],delegatesFocus:void 0}).createChild("div","timeline-flamechart-popover");if(h.createChild("span",o).textContent=r,h.createChild("span","timeline-info-title").textContent=n,a)for(const e of a)e.classList.add("timeline-info-warning"),h.appendChild(e);for(const e of l)h.appendChild(e);return d}prepareHighlightedHiddenEntriesArrowInfo(e){const t=document.createElement("div"),i=u.UIUtils.createShadowRootWithCoreStyles(t,{cssFile:[xi],delegatesFocus:void 0}),n=this.entryData[e],r=Ne.activeManager()?.getEntriesFilter().findHiddenDescendantsAmount(n);if(!r)return null;return i.createChild("div","timeline-flamechart-popover").createChild("span","timeline-info-title").textContent=r+" hidden",t}entryColor(e){const t=this.#ge(e);if("Frame"===t)return"white";if("TrackAppender"===t){const t=this.timelineDataInternal.entryLevels[e],i=this.entryData[e];return this.compatibilityTracksAppender?.colorForEvent(i,t)||""}return""}preparePatternCanvas(){const e=17;this.droppedFramePatternCanvas.width=e,this.droppedFramePatternCanvas.height=e,this.partialFramePatternCanvas.width=e,this.partialFramePatternCanvas.height=e;const t=this.droppedFramePatternCanvas.getContext("2d");if(t){t.translate(8.5,8.5),t.rotate(.25*Math.PI),t.translate(-8.5,-8.5),t.fillStyle="rgb(255, 255, 255)";for(let e=-17;e<34;e+=3)t.fillRect(e,-17,1,51)}const i=this.partialFramePatternCanvas.getContext("2d");i&&(i.strokeStyle="rgb(255, 255, 255)",i.lineWidth=2,i.beginPath(),i.moveTo(17,0),i.lineTo(10,7),i.moveTo(8,9),i.lineTo(2,15),i.stroke())}drawFrame(t,n,r,a,s,o,l){const c=this.entryData[t];if(a+=1,o-=2,c.idle)n.fillStyle="white";else if(c.dropped)if(c.isPartial){n.fillStyle="#f0e442",n.fillRect(a,s,o,l);const e=n.createPattern(this.partialFramePatternCanvas,"repeat");n.fillStyle=e||n.fillStyle}else{n.fillStyle="#f08080",n.fillRect(a,s,o,l);const e=n.createPattern(this.droppedFramePatternCanvas,"repeat");n.fillStyle=e||n.fillStyle}else n.fillStyle="#d7f0d1";n.fillRect(a,s,o,l);const d=e.TimeUtilities.preciseMillisToString(i.Helpers.Timing.microSecondsToMilliseconds(c.duration),1),h=n.measureText(d).width;h<=o&&(n.fillStyle=this.textColor(t),n.fillText(d,a+(o-h)/2,s+l-4))}async drawScreenshot(e,t,i,n,r,a){const s=this.entryData[e];if(!this.screenshotImageCache.has(s)){this.screenshotImageCache.set(s,null);const e=s.args.dataUri,t=await u.UIUtils.loadImage(e);return this.screenshotImageCache.set(s,t),void this.dispatchEventToListeners("DataChanged")}const o=this.screenshotImageCache.get(s);if(!o)return;const l=i+1,c=n+1,d=a-2,h=d/o.naturalHeight,m=Math.floor(o.naturalWidth*h);t.save(),t.beginPath(),t.rect(i,n,r,a),t.clip(),t.drawImage(o,l,c,m,d),t.strokeStyle="#ccc",t.strokeRect(l-.5,c-.5,Math.min(r-1,m+1),d),t.restore()}decorateEntry(e,t,n,r,a,s,o,l,c){const d=this.#ge(e);if("Frame"===d)return this.drawFrame(e,t,n,r,a,s,o),!0;if("Screenshot"===d)return this.drawScreenshot(e,t,r,a,s,o),!0;if("TrackAppender"===d){const d=this.entryData[e];if(i.Types.TraceEvents.isSyntheticInteractionEvent(d))return this.#be(t,e,n,d,r,a,l,s,o,c),!0}return!1}#be(e,n,r,a,s,o,l,c,d,h){const m=i.Helpers.Timing.microSecondsToMilliseconds(a.ts),p=s+c;function g(e){const t=i.Helpers.Timing.microSecondsToMilliseconds(e);return Math.floor(l+(t-m)*h)}e.save(),e.fillStyle=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container");let v=g(a.processingStart);const T=g(a.processingEnd);function f(t,i,n){e.moveTo(t,n-3),e.lineTo(t,n+3),e.moveTo(t,n),e.lineTo(i,n)}a.processingEnd-a.processingStart==0&&(v-=1),e.fillRect(s,o-.5,v-s,d),e.fillRect(T,o-.5,p-T,d);const y=g(a.ts),w=g(i.Types.Timing.MicroSeconds(a.ts+a.dur));e.beginPath(),e.lineWidth=1,e.strokeStyle="#ccc";const b=Math.floor(o+d/2)+.5,S=w-.5;if(f(y+.5,v,b),f(S,T,b),e.stroke(),r){const t=v>0?v:s;e.font=this.#me;const i=5,a=5;u.UIUtils.measureTextWidth(e,r)<=T-t+i&&(e.fillStyle=this.textColor(n),e.fillText(r,t+i,o+d-a))}e.restore()}forceDecoration(e){const t=this.#ge(e);if("Frame"===t)return!0;if("Screenshot"===t)return!0;const n=this.entryData[e];return!!i.Types.TraceEvents.isSyntheticInteractionEvent(n)||Boolean(this.traceEngineData?.Warnings.perEvent.get(n))}appendHeader(e,t,i,n){const r={startLevel:this.currentLevel,name:e,style:t,selectable:i,expanded:n};return this.timelineDataInternal.groups.push(r),r}#ye(t){const n=this.entryData.length;this.entryData.push(t);const r=i.Helpers.Timing.microSecondsToMilliseconds(t.duration);this.entryIndexToTitle[n]=e.TimeUtilities.millisToString(r,!0),this.timelineDataInternal&&(this.timelineDataInternal.entryLevels[n]=this.currentLevel,this.timelineDataInternal.entryTotalTimes[n]=r,this.timelineDataInternal.entryStartTimes[n]=i.Helpers.Timing.microSecondsToMilliseconds(t.startTime))}createSelection(e){const t=this.#ge(e);let i=null;const n=this.entryData[e];return n&&Fi.timelineEntryIsTraceEvent(n)?i=vt.fromTraceEvent(n):"Frame"===t&&(i=vt.fromFrame(this.entryData[e])),i&&(this.lastSelection=new Ji(i,e)),i}formatValue(t,i){return e.TimeUtilities.preciseMillisToString(t,i)}canJumpToEntry(e){return!1}entryIndexForSelection(e){if(!e||vt.isRangeSelection(e.object)||vt.isSyntheticNetworkRequestDetailsEventSelection(e.object))return-1;if(this.lastSelection&&this.lastSelection.timelineSelection.object===e.object)return this.lastSelection.entryIndex;-1===this.entryData.indexOf(e.object)&&vt.isTraceEventSelection(e.object)&&this.timelineDataInternal?.selectedGroup&&(Ne.activeManager()?.getEntriesFilter().revealEntry(e.object),this.timelineData(!0));const t=this.entryData.indexOf(e.object);return-1!==t&&(this.lastSelection=new Ji(e,t)),t}indexForEvent(e){const t=this.#pe.get(e);if(t)return t;const i=this.entryData.indexOf(e),n=i>-1?i:null;return this.#pe.set(e,n),n}buildFlowForInitiator(e){if(!this.traceEngineData)return!1;if(!this.timelineDataInternal)return!1;if(this.lastInitiatorEntry===e)return this.lastInitiatorsData&&(this.timelineDataInternal.initiatorsData=this.lastInitiatorsData),!1;if(!this.compatibilityTracksAppender)return!1;const t=this.timelineDataInternal.initiatorsData.length;if(-1===e)return this.lastInitiatorEntry=e,0!==t&&(this.timelineDataInternal.resetFlowData(),!0);if("TrackAppender"!==this.#ge(e))return!1;const i=this.entryData[e];this.timelineDataInternal.resetFlowData(),this.lastInitiatorEntry=e;const n=Ne.activeManager()?.getEntriesFilter().invisibleEntries()??[],r=Ne.activeManager()?.getEntriesFilter().expandableEntries()??[],a=vi(this.traceEngineData,i,n,r);if(0===t&&0===a.length)return!1;for(const e of a){const t=this.indexForEvent(e.event),i=this.indexForEvent(e.initiator);null!==t&&null!==i&&this.timelineDataInternal.initiatorsData.push({initiatorIndex:i,eventIndex:t,isInitiatorHidden:e.isInitiatorHidden,isEntryHidden:e.isEntryHidden})}return this.lastInitiatorsData=this.timelineDataInternal.initiatorsData,!0}eventByIndex(e){if(e<0)return null;const t=this.#ge(e);return"TrackAppender"===t||"Frame"===t?this.entryData[e]:null}}const Li=i.Types.Timing.MilliSeconds(.001);var Ri,Di=Object.freeze({__proto__:null,TimelineFlameChartDataProvider:Fi,InstantEventVisibleDurationMs:Li});function Ai(e){let i="--app-color-system";switch(e){case Ri.Doc:i="--app-color-doc";break;case Ri.JS:i="--app-color-scripting";break;case Ri.CSS:i="--app-color-css";break;case Ri.Img:i="--app-color-image";break;case Ri.Media:i="--app-color-media";break;case Ri.Font:i="--app-color-font";break;case Ri.Wasm:i="--app-color-wasm";break;case Ri.Other:default:i="--app-color-system"}return t.ThemeSupport.instance().getComputedValue(i)}function Ni(e){const t=function(e){switch(e.args.data.mimeType){case"text/html":return Ri.Doc;case"application/javascript":case"application/x-javascript":case"text/javascript":return Ri.JS;case"text/css":return Ri.CSS;case"image/gif":case"image/jpeg":case"image/png":case"image/svg+xml":case"image/webp":case"image/x-icon":return Ri.Img;case"audio/aac":case"audio/midi":case"audio/x-midi":case"audio/mpeg":case"audio/ogg":case"audio/wav":case"audio/webm":return Ri.Media;case"font/opentype":case"font/woff2":case"font/ttf":case"application/font-woff":return Ri.Font;case"application/wasm":return Ri.Wasm;default:return Ri.Other}}(e);return Ai(t)}!function(e){e.Doc="Doc",e.CSS="CSS",e.JS="JS",e.Font="Font",e.Img="Img",e.Media="Media",e.Wasm="Wasm",e.Other="Other"}(Ri||(Ri={}));const Bi={network:"Network",wsConnectionOpened:"WebSocket opened",wsConnectionOpenedWithUrl:"WebSocket opened: {PH1}",wsConnectionClosed:"WebSocket closed"},Hi=e.i18n.registerUIStrings("panels/timeline/NetworkTrackAppender.ts",Bi),Ui=e.i18n.getLocalizedString.bind(void 0,Hi);class Wi{appenderName="Network";#Se;webSocketIdToLevel=new Map;#k=[];#me;#Ce;constructor(e,i){this.#Se=e,this.#k=i,this.#me=`${p.Font.DEFAULT_FONT_SIZE} ${p.Font.getFontFamilyForCanvas()}`,t.ThemeSupport.instance().addEventListener(t.ThemeChangeEvent.eventName,(()=>{this.#Ce&&(this.#Ce.style.color=t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),this.#Ce.style.backgroundColor=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"))}))}group(){return this.#Ce}font(){return this.#me}appendTrackAtLevel(e,t){return 0===this.#k.length?e:(this.#i(e,t),this.#Ee(this.#k,e))}#i(e,t){const i=U({shareHeaderLine:!1,useFirstLineForOverview:!1,useDecoratorsForOverview:!0}),n=[];for(const e in Ri)n.push({color:o.colorForNetworkCategory(e),category:e});this.#Ce=W("network",0,Ui(Bi.network),i,!0,t,!1,n),this.#Se.groups.push(this.#Ce)}#Ee(e,t){for(let n=0;n<e.length;++n){const r=e[n];this.#ke(r,t),i.Types.TraceEvents.isSyntheticNetworkRequestEvent(r)&&i.Helpers.Network.isSyntheticNetworkRequestEventRenderBlocking(r)&&_(this.#Se,n,{type:"WARNING_TRIANGLE",customEndTime:r.args.data.syntheticData.finishTime})}return this.relayoutEntriesWithinBounds(e,i.Types.Timing.MilliSeconds(-1/0),i.Types.Timing.MilliSeconds(1/0))}#ke(e,t){const n=this.#Se.entryLevels.length;this.#Se.entryLevels[n]=t,this.#Se.entryStartTimes[n]=i.Helpers.Timing.microSecondsToMilliseconds(e.ts);const r=e.dur||i.Helpers.Timing.millisecondsToMicroseconds(Li);return this.#Se.entryTotalTimes[n]=i.Helpers.Timing.microSecondsToMilliseconds(r),t}relayoutEntriesWithinBounds(e,t,n){if(!this.#Se||0===e.length)return 0;const r=[];this.webSocketIdToLevel=new Map;let a=0;for(let s=0;s<e.length;++s){const o=e[s],l=i.Helpers.Timing.microSecondsToMilliseconds(o.ts),c=o.dur?i.Helpers.Timing.microSecondsToMilliseconds(o.dur):Li;if(!(l<n&&l+c>t)){this.#Se.entryLevels[s]=-1;continue}let d;d="identifier"in o.args.data&&i.Types.TraceEvents.isWebSocketEvent(o)?this.getWebSocketLevel(o,r):V(o,r),this.#Se.entryLevels[s]=d,a=Math.max(a,r.length,d)}for(let t=0;t<e.length;++t)-1===this.#Se.entryLevels[t]&&(this.#Se.entryLevels[t]=a);return a}getWebSocketLevel(e,t){const i=e.args.data.identifier;let n;return this.webSocketIdToLevel.has(i)?n=this.webSocketIdToLevel.get(i)||0:(n=V(e,t),this.webSocketIdToLevel.set(i,n)),n}colorForEvent(e){if(i.Types.TraceEvents.isSyntheticWebSocketConnectionEvent(e))return"";if(i.Types.TraceEvents.isWebSocketTraceEvent(e))return Ai(Ri.JS);if(!i.Types.TraceEvents.isSyntheticNetworkRequestEvent(e))throw new Error(`Unexpected Network Request: The event's type is '${e.name}'`);return Ni(e)}titleForEvent(e){return e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:O(e.dur)}}titleForWebSocketEvent(e){return i.Types.TraceEvents.isTraceEventWebSocketCreate(e)?e.args.data.url?Ui(Bi.wsConnectionOpenedWithUrl,{PH1:e.args.data.url}):Ui(Bi.wsConnectionOpened):i.Types.TraceEvents.isTraceEventWebSocketDestroy(e)?Ui(Bi.wsConnectionClosed):e.name}}var Oi=Object.freeze({__proto__:null,NetworkTrackAppender:Wi});class Vi{#xe;#Pe;#k;#Ie;#Me;#Fe;#Le;#A;#pe=new Map;#Re=-1;#De=[];constructor(){this.#xe=0,this.#Pe=0,this.#k=[],this.#Ie=0,this.#Me=null,this.#A=null}setModel(e){this.#Fe=null,this.#k=[],this.#A=e,this.#pe.clear(),this.#A&&(this.setEvents(this.#A),this.#Ae(this.#A))}setEvents(e){e.NetworkRequests.webSocket&&e.NetworkRequests.webSocket.forEach((e=>{e.syntheticConnectionEvent&&this.#k.push(e.syntheticConnectionEvent),this.#k.push(...e.events)})),e.NetworkRequests.byTime&&this.#k.push(...e.NetworkRequests.byTime)}isEmpty(){return this.timelineData(),!this.#k.length}maxStackDepth(){return this.#Ie}hasTrackConfigurationMode(){return!1}timelineData(){return this.#Fe&&0!==this.#Fe.entryLevels.length?this.#Fe:(this.#Fe=p.FlameChart.FlameChartTimelineData.createEmpty(),this.#A?(this.#k.length||this.setEvents(this.#A),this.#Me=new Wi(this.#Fe,this.#k),this.#Ie=this.#Me.appendTrackAtLevel(0),this.#Fe):this.#Fe)}minimumBoundary(){return this.#xe}totalTime(){return this.#Pe}setWindowTimes(e,t){this.#Ne(e,t)}createSelection(e){if(-1===e)return null;const t=this.#k[e];return this.#Le=new Ji(vt.fromTraceEvent(t),e),this.#Le.timelineSelection}customizedContextMenu(e,t){const n=this.eventByIndex(t);if(!n||!i.Types.TraceEvents.isSyntheticNetworkRequestEvent(n))return;const r=new R.NetworkRequest.TimelineNetworkRequest(n),a=new u.ContextMenu.ContextMenu(e,{useSoftMenu:!0});return a.appendApplicableItems(r),a}indexForEvent(e){if(e instanceof i.Handlers.ModelHandlers.Frames.TimelineFrame)return null;if(!i.Types.TraceEvents.isNetworkTrackEntry(e))return null;const t=this.#pe.get(e);if(void 0!==t)return t;const n=this.#k.indexOf(e),r=n>-1?n:null;return this.#pe.set(e,r),r}eventByIndex(e){return this.#k.at(e)??null}entryIndexForSelection(e){if(!e)return-1;if(this.#Le&&this.#Le.timelineSelection.object===e.object)return this.#Le.entryIndex;if(!vt.isNetworkEventSelection(e.object))return-1;const t=this.#k.indexOf(e.object);return-1!==t&&(this.#Le=new Ji(vt.fromTraceEvent(e.object),t)),t}entryColor(e){if(!this.#Me)throw new Error("networkTrackAppender should not be empty");return this.#Me.colorForEvent(this.#k[e])}textColor(e){return Ki.textColor}entryTitle(e){const t=this.#k[e];if(i.Types.TraceEvents.isWebSocketTraceEvent(t)||i.Types.TraceEvents.isSyntheticWebSocketConnectionEvent(t))return this.#Me?.titleForWebSocketEvent(t)||"";const n=new r.ParsedURL.ParsedURL(t.args.data.url);return n.isValid?`${n.displayName} (${n.host})`:t.args.data.url||null}entryFont(e){return this.#Me?.font()||null}getDecorationPixels(e,t,n){const r=i.Helpers.Timing.microSecondsToMilliseconds(e.ts),a=e=>t+(e-r)*n,s=i.Helpers.Timing.microSecondsToMilliseconds(e.ts),o=i.Helpers.Timing.microSecondsToMilliseconds(e.ts+e.dur),l=i.Helpers.Timing.microSecondsToMilliseconds(e.args.data.syntheticData.sendStartTime),c=i.Helpers.Timing.microSecondsToMilliseconds(e.args.data.syntheticData.downloadStart),d=Math.max(a(l),t),h=Math.max(a(c),d),m=Math.max(a(i.Helpers.Timing.microSecondsToMilliseconds(e.args.data.syntheticData.finishTime)),h);return{sendStart:d,headersEnd:h,finish:m,start:a(s),end:Math.max(a(o),m)}}decorateEntry(e,t,n,r,a,s,o,l,c){const d=this.#k[e];return i.Types.TraceEvents.isSyntheticWebSocketConnectionEvent(d)?this.#Be(e,t,a,o,l,c):!!i.Types.TraceEvents.isSyntheticNetworkRequestEvent(d)&&this.#He(e,t,n,r,a,s,o,l,c)}#He(e,n,r,a,s,o,l,c,d){const h=this.#k[e];if(!i.Types.TraceEvents.isSyntheticNetworkRequestEvent(h))return!1;const{sendStart:m,headersEnd:p,finish:g,start:v,end:T}=this.getDecorationPixels(h,c,d);function f(e,t,i){n.moveTo(e,i-3),n.lineTo(e,i+3),n.moveTo(e,i),n.lineTo(t,i)}n.fillStyle="hsla(0, 100%, 100%, 0.8)",n.fillRect(m+.5,s+.5,p-m-.5,l-2),n.fillStyle=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),n.fillRect(a,s-.5,m-a,l),n.fillRect(g,s-.5,a+o-g,l),n.beginPath(),n.lineWidth=1,n.strokeStyle="#ccc";const y=Math.floor(s+l/2)+.5,w=T-.5;f(v+.5,m,y),f(w,g,y),n.stroke();const b=this.#Ue(h.args.data.priority);b&&(n.fillStyle=b,n.fillRect(m+.5,s+.5,3.5,3.5));const S=Math.max(m,0),C=g-S;if(C>=20){let t=this.entryTitle(e)||"";if(h.args.data.fromServiceWorker&&(t="⚙ "+t),t){const e=4,i=l-5,r=u.UIUtils.trimTextEnd(n,t,C-2*e);n.fillStyle="#333",n.fillText(r,S+e,s+i)}}return!0}#Be(e,n,r,a,s,o){n.save();const l=this.#k[e],c=i.Helpers.Timing.microSecondsToMilliseconds(l.ts),d=e=>Math.floor(s+(e-c)*o),h=i.Helpers.Timing.microSecondsToMilliseconds(l.ts+l.dur),m=d(c)+.5,p=d(h)-.5;n.strokeStyle=t.ThemeSupport.instance().getComputedValue("--app-color-rendering");const u=Math.floor(r+a/2)+.5;return n.setLineDash([3,2]),n.moveTo(m,u-1),n.lineTo(p,u-1),n.moveTo(m,u+1),n.lineTo(p,u+1),n.stroke(),n.restore(),!0}forceDecoration(e){return!0}forceDrawableLevel(e){return this.#Me?.webSocketIdToLevel.has(e)||!1}prepareHighlightedEntryInfo(e){const t=this.#k[e];if(i.Types.TraceEvents.isSyntheticNetworkRequestEvent(t)){const e=document.createElement("div"),i=u.UIUtils.createShadowRootWithCoreStyles(e,{cssFile:[xi],delegatesFocus:void 0}).createChild("div","timeline-flamechart-popover"),n=new s.NetworkRequestTooltip.NetworkRequestTooltip;return n.networkRequest=t,i.appendChild(n),e}return null}#Ue(e){const t=p.NetworkPriorities.networkPriorityWeight(e);return t?`hsla(214, 80%, 50%, ${t/5})`:null}#Ae(e){const{traceBounds:t}=e.Meta,n=i.Helpers.Timing.microSecondsToMilliseconds(t.min),r=i.Helpers.Timing.microSecondsToMilliseconds(t.max);this.#xe=n,this.#Pe=n===r?1e3:r-this.#xe}#Ne(e,t){this.#Me&&this.#Fe&&(this.#Ie=this.#Me.relayoutEntriesWithinBounds(this.#k,e,t),this.#Fe=p.FlameChart.FlameChartTimelineData.create({entryLevels:this.#Fe?.entryLevels,entryTotalTimes:this.#Fe?.entryTotalTimes,entryStartTimes:this.#Fe?.entryStartTimes,groups:this.#Fe?.groups,initiatorsData:this.#Fe.initiatorsData,entryDecorations:this.#Fe.entryDecorations}))}preferredHeight(){if(!this.#Me||0===this.#Ie)return 0;const e=this.#Me.group();return e?e.style.height*(this.isExpanded()?c.NumberUtilities.clamp(this.#Ie+1,7,8.5):1):0}isExpanded(){return Boolean(this.#Me?.group()?.expanded)}formatValue(t,i){return e.TimeUtilities.preciseMillisToString(t,i)}canJumpToEntry(e){return!1}mainFrameNavigationStartEvents(){return this.#A?this.#A.Meta.mainFrameNavigations:[]}buildFlowForInitiator(e){if(!this.#A)return!1;if(!this.#Fe)return!1;if(this.#Re===e)return this.#De&&(this.#Fe.initiatorsData=this.#De),!0;if(!this.#Me)return!1;const t=this.#Fe.initiatorsData.length;if(-1===e)return this.#Re=e,0!==t&&(this.#Fe.resetFlowData(),!0);const i=this.#k[e];this.#Fe.resetFlowData(),this.#Re=e;const n=Ti(this.#A,i);if(0===t&&0===n.length)return!1;for(const e of n){const t=this.indexForEvent(e.event),i=this.indexForEvent(e.initiator);null!==t&&null!==i&&this.#Fe.initiatorsData.push({initiatorIndex:i,eventIndex:t})}return this.#De=this.#Fe.initiatorsData,!0}}var _i=Object.freeze({__proto__:null,TimelineFlameChartNetworkDataProvider:Vi});const zi=new CSSStyleSheet;zi.replaceSync(".timeline-overlays-container{position:absolute;top:0;left:0;right:0;bottom:0;z-index:200;pointer-events:none}.overlay-item{position:absolute;top:0;left:0}.overlay-type-ENTRY_SELECTED{pointer-events:none;border:2px solid var(--sys-color-primary);background-color:var(--sys-color-state-ripple-primary);&.cut-off-top{border-top:none}&.cut-off-bottom{border-bottom:none}}.overlay-type-TIME_RANGE{background:linear-gradient(180deg,rgb(255 125 210/0%) 0%,rgb(255 125 210/15%) 85%);border-color:var(--ref-palette-pink80);border-width:1px;border-style:solid;pointer-events:none;top:0;bottom:0;border-bottom-width:5px;&.overlap-1{bottom:55px;border-color:var(--ref-palette-pink70)}&.overlap-2{bottom:105px;border-color:var(--ref-palette-pink60)}}.overlay-type-CURSOR_TIMESTAMP_MARKER{top:0;bottom:0;width:2px;background-color:var(--sys-color-primary);pointer-events:none}.timeline-entry-tooltip-element:not(:empty){z-index:2000;position:absolute;contain:content;background-color:var(--sys-color-cdt-base-container);pointer-events:none;padding:4px 8px;white-space:nowrap;max-width:80%;box-shadow:var(--drop-shadow)}\n/*# sourceURL=timelineFlameChartView.css */\n");const Gi={sAtS:"{PH1} at {PH2}"},ji=e.i18n.registerUIStrings("panels/timeline/TimelineFlameChartView.ts",Gi),qi=e.i18n.getLocalizedString.bind(void 0,ji);class $i extends u.Widget.VBox{delegate;searchResults;eventListeners;networkSplitWidget;mainDataProvider;mainFlameChart;networkFlameChartGroupExpansionSetting;networkDataProvider;networkFlameChart;networkPane;splitResizer;chartSplitWidget;brickGame;countersView;detailsSplitWidget;detailsView;onMainAnnotateEntry;onNetworkAnnotateEntry;onMainEntrySelected;onNetworkEntrySelected;#We;#D;groupBySetting;searchableView;needsResizeToPreferredHeights;selectedSearchResult;searchRegex;#U;#Oe=null;#Ve=null;#x=this.#P.bind(this);#_e=0;#ze=setTimeout((()=>({})),0);#Ge=document.createElement("div");#je;#qe=null;#$e=[];#Je=null;#Ke=document.createElement("div");#Ye=new Map;constructor(e){super(),this.element.classList.add("timeline-flamechart"),this.delegate=e,this.eventListeners=[],this.#U=null;const t=new u.Widget.VBox;t.element.classList.add("flame-charts-container"),this.networkSplitWidget=new u.SplitWidget.SplitWidget(!1,!1,"timeline-flamechart-main-view",150),this.networkSplitWidget.show(t.element),this.#Ge.classList.add("timeline-overlays-container"),t.element.appendChild(this.#Ge),this.#Ke.classList.add("timeline-entry-tooltip-element"),t.element.appendChild(this.#Ke),this.networkSplitWidget.sidebarElement().style.zIndex="120";const i=r.Settings.Settings.instance().createSetting("timeline-flamechart-main-view-group-expansion",{});this.mainDataProvider=new Fi,this.mainDataProvider.addEventListener("DataChanged",(()=>this.mainFlameChart.scheduleUpdate())),this.mainFlameChart=new p.FlameChart.FlameChart(this.mainDataProvider,this,{groupExpansionSetting:i,selectedElementOutline:!1,tooltipElement:this.#Ke,useOverlaysForCursorRuler:!0}),this.mainFlameChart.alwaysShowVerticalScroll(),this.mainFlameChart.enableRuler(!1),this.mainFlameChart.addEventListener("LatestDrawDimensions",(e=>{this.#je.updateChartDimensions("main",e.data.chart),this.#je.updateVisibleWindow(e.data.traceWindow),this.#je.update()})),this.networkFlameChartGroupExpansionSetting=r.Settings.Settings.instance().createSetting("timeline-flamechart-network-view-group-expansion",{}),this.networkDataProvider=new Vi,this.networkFlameChart=new p.FlameChart.FlameChart(this.networkDataProvider,this,{groupExpansionSetting:this.networkFlameChartGroupExpansionSetting,selectedElementOutline:!1,tooltipElement:this.#Ke,useOverlaysForCursorRuler:!0}),this.networkFlameChart.alwaysShowVerticalScroll(),this.networkFlameChart.addEventListener("LatestDrawDimensions",(e=>{this.#je.updateChartDimensions("network",e.data.chart),this.#je.updateVisibleWindow(e.data.traceWindow),this.#je.update(),this.mainFlameChart.setTooltipYPixelAdjustment(this.#je.networkChartOffsetHeight())})),this.mainFlameChart.addEventListener("MouseMove",(e=>{this.#Xe(e.data)})),this.networkFlameChart.addEventListener("MouseMove",(e=>{this.#Xe(e.data)})),this.#je=new S.Overlays.Overlays({container:this.#Ge,charts:{mainChart:this.mainFlameChart,mainProvider:this.mainDataProvider,networkChart:this.networkFlameChart,networkProvider:this.networkDataProvider}}),this.#je.addEventListener(S.Overlays.AnnotationOverlayActionEvent.eventName,(e=>{const{overlay:t,action:i}=e;"Remove"===i?Ne.activeManager()?.removeAnnotationOverlay(t):"Update"===i&&Ne.activeManager()?.updateAnnotationOverlay(t)})),this.networkPane=new u.Widget.VBox,this.networkPane.setMinimumSize(23,23),this.networkFlameChart.show(this.networkPane.element),this.splitResizer=this.networkPane.element.createChild("div","timeline-flamechart-resizer"),this.networkSplitWidget.hideDefaultResizer(!0),this.networkSplitWidget.installResizer(this.splitResizer),this.networkSplitWidget.setMainWidget(this.mainFlameChart),this.networkSplitWidget.setSidebarWidget(this.networkPane),this.chartSplitWidget=new u.SplitWidget.SplitWidget(!1,!0,"timeline-counters-split-view-state"),this.countersView=new Qe(this.delegate),this.chartSplitWidget.setMainWidget(t),this.chartSplitWidget.setSidebarWidget(this.countersView),this.chartSplitWidget.hideDefaultResizer(),this.chartSplitWidget.installResizer(this.countersView.resizerElement()),this.detailsSplitWidget=new u.SplitWidget.SplitWidget(!1,!0,"timeline-panel-details-split-view-state"),this.detailsSplitWidget.element.classList.add("timeline-details-split"),this.detailsView=new pi(e),this.detailsSplitWidget.installResizer(this.detailsView.headerElement()),this.detailsSplitWidget.setMainWidget(this.chartSplitWidget),this.detailsSplitWidget.setSidebarWidget(this.detailsView),this.detailsSplitWidget.show(this.element),this.onMainAnnotateEntry=this.onAnnotateEntry.bind(this,this.mainDataProvider),this.onNetworkAnnotateEntry=this.onAnnotateEntry.bind(this,this.networkDataProvider),a.Runtime.experiments.isEnabled("perf-panel-annotations")&&(this.mainFlameChart.addEventListener("AnnotateEntry",this.onMainAnnotateEntry,this),this.networkFlameChart.addEventListener("AnnotateEntry",this.onNetworkAnnotateEntry,this)),this.onMainEntrySelected=this.onEntrySelected.bind(this,this.mainDataProvider),this.onNetworkEntrySelected=this.onEntrySelected.bind(this,this.networkDataProvider),this.mainFlameChart.addEventListener("EntrySelected",this.onMainEntrySelected,this),this.mainFlameChart.addEventListener("EntryInvoked",this.onMainEntrySelected,this),this.networkFlameChart.addEventListener("EntrySelected",this.onNetworkEntrySelected,this),this.networkFlameChart.addEventListener("EntryInvoked",this.onNetworkEntrySelected,this),this.mainFlameChart.addEventListener("EntryHighlighted",this.onEntryHighlighted,this),this.element.addEventListener("keydown",this.#Ze.bind(this)),this.#We=this.#Qe.bind(this),this.#D=null,this.groupBySetting=r.Settings.Settings.instance().createSetting("timeline-tree-group-by",zt.GroupBy.None),this.groupBySetting.addChangeListener(this.refreshMainFlameChart,this),this.refreshMainFlameChart(),h.TraceBounds.onChange(this.#x)}setActiveInsight(e){this.#Je=e;for(const e of this.#$e)this.removeOverlay(e);if(this.#Je&&e){const t=e.createOverlayFn();this.#$e=t;for(const e of this.#$e)this.addOverlay(e)}}#Xe(e){const{mouseEvent:t,timeInMicroSeconds:i}=e;if(!t.shiftKey){this.#je.removeOverlaysOfType("CURSOR_TIMESTAMP_MARKER")>0&&this.#je.update()}!t.metaKey&&t.shiftKey&&this.addOverlay({type:"CURSOR_TIMESTAMP_MARKER",timestamp:i})}#Ze(e){const t="fixme";e.key===t[this.#_e]?(this.#_e++,clearTimeout(this.#ze),this.#ze=setTimeout((()=>{this.#_e=0}),2e3)):(this.#_e=0,clearTimeout(this.#ze)),5===this.#_e&&this.fixMe()}fixMe(){}#P(e){if("MINIMAP_BOUNDS"===e.updateType)return;const t=e.state.milli.timelineTraceWindow,i=Boolean(e.options.shouldAnimate);this.mainFlameChart.setWindowTimes(t.min,t.max,i),this.networkDataProvider.setWindowTimes(t.min,t.max),this.networkFlameChart.setWindowTimes(t.min,t.max,i),this.updateSearchResults(!1,!1)}isNetworkTrackShownForTests(){return"OnlyMain"!==this.networkSplitWidget.showMode()}getMainDataProvider(){return this.mainDataProvider}refreshMainFlameChart(){this.mainFlameChart.update()}extensionDataVisibilityChanged(){this.#et(),this.mainDataProvider.reset(!0),this.mainDataProvider.timelineData(!0),this.refreshMainFlameChart()}windowChanged(e,t,n){h.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(i.Helpers.Timing.traceWindowFromMilliSeconds(i.Types.Timing.MilliSeconds(e),i.Types.Timing.MilliSeconds(t)),{shouldAnimate:n})}updateRangeSelection(e,t){if(this.delegate.select(vt.fromRange(e,t)),a.Runtime.experiments.isEnabled("perf-panel-annotations")){const n=i.Helpers.Timing.traceWindowFromMilliSeconds(i.Types.Timing.MilliSeconds(e),i.Types.Timing.MilliSeconds(t));this.#qe?this.updateExistingOverlay(this.#qe,{bounds:n}):this.#qe=this.addOverlay({type:"TIME_RANGE",label:"",showDuration:!0,bounds:n})}}getMainFlameChart(){return this.mainFlameChart}getNetworkFlameChart(){return this.networkFlameChart}updateSelectedGroup(e,t){e===this.mainFlameChart&&this.#Ve!==t?.name&&(this.#Ve=t?.name||null,this.#D=t?this.mainDataProvider.groupTreeEvents(t):null,this.#tt())}setModel(e,t=!1){e!==this.#U&&(this.#Ve=null,this.#U=e,r.EventTarget.removeEventListeners(this.eventListeners),this.#D=null,this.mainDataProvider.setModel(e,t),this.networkDataProvider.setModel(e),this.#et(),this.updateSearchResults(!1,!1),this.refreshMainFlameChart(),this.#it())}setInsights(e){this.#Oe!==e&&(this.#Oe=e)}#et(){this.networkDataProvider.isEmpty()?(this.mainFlameChart.enableRuler(!0),this.networkSplitWidget.hideSidebar()):(this.mainFlameChart.enableRuler(!1),this.networkSplitWidget.showBoth(),this.resizeToPreferredHeights()),this.#je.reset(),this.mainFlameChart.reset(),this.networkFlameChart.reset(),this.updateSearchResults(!1,!1);const e=h.TraceBounds.BoundsManager.instance().state();if(!e)throw new Error("TimelineFlameChartView could not set the window bounds.");const t=e.milli.timelineTraceWindow;this.mainFlameChart.setWindowTimes(t.min,t.max),this.networkDataProvider.setWindowTimes(t.min,t.max),this.networkFlameChart.setWindowTimes(t.min,t.max)}#Qe(){this.mainDataProvider.timelineData(!0),this.mainFlameChart.scheduleUpdate()}#tt(){this.countersView.setModel(this.#U,this.#D),this.detailsView.setModel(this.#U,this.#D)}#it(){this.mainFlameChart.scheduleUpdate(),this.networkFlameChart.scheduleUpdate(),this.#nt()}#nt(){const e=[...this.mainFlameChart.timelineData()?.groups??[],...this.networkFlameChart.timelineData()?.groups??[]];for(const t of e){if(!t.jslogContext)continue;const e=this.#Ye.get(t.jslogContext)??Symbol(t.jslogContext);this.#Ye.has(t.jslogContext)||(this.#Ye.set(t.jslogContext,e),g.registerLoggable(e,`${g.section().context(`timeline.${t.jslogContext}`)}`,this.delegate.element))}}onEntryHighlighted(e){n.OverlayModel.OverlayModel.hideDOMNodeHighlight();const t=e.data,r=this.mainDataProvider.eventByIndex(t);if(!r||!this.#U)return;if(r instanceof i.Handlers.ModelHandlers.Frames.TimelineFrame)return;const a=rt(this.#U,r);if(!a)return;const s=i.Extras.FetchNodes.nodeIdsForEvent(this.#U,r);for(const e of s)new n.DOMModel.DeferredDOMNode(a,e).highlight()}highlightEvent(e){const t=e?this.mainDataProvider.entryIndexForSelection(vt.fromTraceEvent(e)):-1;t>=0?this.mainFlameChart.highlightEntry(t):this.mainFlameChart.hideHighlight()}willHide(){this.networkFlameChartGroupExpansionSetting.removeChangeListener(this.resizeToPreferredHeights,this),b.IgnoreListManager.IgnoreListManager.instance().removeChangeListener(this.#We)}wasShown(){this.registerCSSFiles([zi]),this.networkFlameChartGroupExpansionSetting.addChangeListener(this.resizeToPreferredHeights,this),b.IgnoreListManager.IgnoreListManager.instance().addChangeListener(this.#We),this.needsResizeToPreferredHeights&&this.resizeToPreferredHeights(),this.#it()}updateCountersGraphToggle(e){e?this.chartSplitWidget.showBoth():this.chartSplitWidget.hideSidebar()}setSelection(e){const t=this.mainDataProvider.entryIndexForSelection(e),i=this.networkDataProvider.entryIndexForSelection(e);this.mainFlameChart.setSelectedEntry(t),this.networkFlameChart.setSelectedEntry(i),this.#je.removeOverlaysOfType("ENTRY_SELECTED"),null!==e&&vt.isRangeSelection(e.object)||!this.#qe||(this.#je.remove(this.#qe),this.#qe=null);let n=this.mainDataProvider.entryIndexForSelection(e);this.mainFlameChart.setSelectedEntry(n),n=this.networkDataProvider.entryIndexForSelection(e),this.networkFlameChart.setSelectedEntry(n),this.detailsView&&this.detailsView.setSelection(e),e&&(vt.isTraceEventSelection(e.object)||vt.isSyntheticNetworkRequestDetailsEventSelection(e.object)||vt.isFrameObject(e.object))&&this.addOverlay({type:"ENTRY_SELECTED",entry:e.object})}addOverlay(e){const t=this.#je.add(e);return this.#je.update(),t}removeOverlay(e){this.#je.remove(e),this.#je.update()}updateExistingOverlay(e,t){this.#je.updateExisting(e,t),this.#je.update()}onAnnotateEntry(e,t){const i=e.createSelection(t.data);i&&(vt.isTraceEventSelection(i.object)||vt.isSyntheticNetworkRequestDetailsEventSelection(i.object))&&(this.setSelection(i),Ne.activeManager()?.createAnnotation({type:"ENTRY_LABEL",entry:i.object,label:""}))}onEntrySelected(e,t){const i=e.timelineData();if(!i)return;const n=t.data,r=i.entryLevels[n],a=Xi(i.groups,r);if(a&&a.jslogContext){const e=this.#Ye.get(a.jslogContext)??null;e&&g.logClick(e,new MouseEvent("click"))}e.buildFlowForInitiator(n),this.delegate.select(e.createSelection(n))}resizeToPreferredHeights(){this.isShowing()?(this.needsResizeToPreferredHeights=!1,this.networkPane.element.classList.toggle("timeline-network-resizer-disabled",!this.networkDataProvider.isExpanded()),this.networkSplitWidget.setSidebarSize(this.networkDataProvider.preferredHeight()+this.splitResizer.clientHeight+p.FlameChart.RulerHeight+2)):this.needsResizeToPreferredHeights=!0}setSearchableView(e){this.searchableView=e}jumpToNextSearchResult(){if(!this.searchResults||!this.searchResults.length)return;const e=void 0!==this.selectedSearchResult?this.searchResults.indexOf(this.selectedSearchResult):-1;this.selectSearchResult(c.NumberUtilities.mod(e+1,this.searchResults.length))}jumpToPreviousSearchResult(){if(!this.searchResults||!this.searchResults.length)return;const e=void 0!==this.selectedSearchResult?this.searchResults.indexOf(this.selectedSearchResult):0;this.selectSearchResult(c.NumberUtilities.mod(e-1,this.searchResults.length))}supportsCaseSensitiveSearch(){return!0}supportsRegexSearch(){return!0}selectSearchResult(e){this.searchableView.updateCurrentMatchIndex(e),this.searchResults&&(this.selectedSearchResult=this.searchResults[e],this.delegate.select(this.mainDataProvider.createSelection(this.selectedSearchResult)),this.mainFlameChart.showPopoverForSearchResult(this.selectedSearchResult))}updateSearchResults(e,t){const i=h.TraceBounds.BoundsManager.instance().state();if(!i)return;const n=this.selectedSearchResult;if(delete this.selectedSearchResult,this.searchResults=[],this.mainFlameChart.removeSearchResultHighlights(),!this.searchRegex)return;const r=new At(this.searchRegex),a=i.milli.timelineTraceWindow;if(this.searchResults=this.mainDataProvider.search(a.min,a.max,r),this.searchableView.updateSearchMatchesCount(this.searchResults.length),this.searchResults.length<=200&&this.mainFlameChart.highlightAllEntries(this.searchResults),!e||!this.searchResults.length)return;let s=this.searchResults.indexOf(n);-1===s&&(s=t?this.searchResults.length-1:0),this.selectSearchResult(s)}getSearchResults(){return this.searchResults}onSearchCanceled(){void 0!==this.selectedSearchResult&&this.delegate.select(null),delete this.searchResults,delete this.selectedSearchResult,delete this.searchRegex,this.mainFlameChart.showPopoverForSearchResult(-1),this.mainFlameChart.removeSearchResultHighlights()}performSearch(e,t,i){this.searchRegex=e.toSearchRegex().regex,this.updateSearchResults(t,i)}}class Ji{timelineSelection;entryIndex;constructor(e,t){this.timelineSelection=e,this.entryIndex=t}}const Ki={textColor:"#333"};class Yi{startTimeInternal;startOffset;style;constructor(e,t,i){this.startTimeInternal=e,this.startOffset=t,this.style=i}startTime(){return this.startTimeInternal}color(){return this.style.color}title(){if(this.style.lowPriority)return null;const t=e.TimeUtilities.millisToString(this.startOffset);return qi(Gi.sAtS,{PH1:this.style.title,PH2:t})}draw(e,t,i,n){this.style.lowPriority&&n<4||(e.save(),this.style.tall&&(e.strokeStyle=this.style.color,e.lineWidth=this.style.lineWidth,e.translate(this.style.lineWidth<1||1&this.style.lineWidth?.5:0,.5),e.beginPath(),e.moveTo(t,0),e.setLineDash(this.style.dashStyle),e.lineTo(t,e.canvas.height),e.stroke()),e.restore())}}function Xi(e,t){return e.find(((i,n)=>{const r=e.at(n+1),a=r?r.startLevel-1:1/0;return i.startLevel<=t&&a>=t}))??null}var Zi=Object.freeze({__proto__:null,TimelineFlameChartView:$i,Selection:Ji,FlameChartStyle:Ki,TimelineFlameChartMarker:Yi,groupForLevel:Xi});const Qi={net:"NET",cpu:"CPU",heap:"HEAP",sSDash:"{PH1} – {PH2}"},en=e.i18n.registerUIStrings("panels/timeline/TimelineEventOverview.ts",Qi),tn=e.i18n.getLocalizedString.bind(void 0,en);class nn extends p.TimelineOverviewPane.TimelineOverviewBase{constructor(e,t){super(),this.element.id="timeline-overview-"+e,this.element.classList.add("overview-strip"),t&&(this.element.createChild("div","timeline-overview-strip-title").textContent=t)}renderBar(e,t,i,n,r){const a=e,s=t-e,o=this.context();o.fillStyle=r,o.fillRect(a,i,s,n)}}const rn=new Set(["VeryHigh","High","Medium"]);class an extends nn{#t;constructor(e){super("network",tn(Qi.net)),this.#t=e}update(e,t){this.resetCanvas(),this.#rt(e,t)}#rt(e,t){if(!this.#t)return;const n=e&&t?{min:e,max:t,range:t-e}:i.Helpers.Timing.traceWindowMilliSeconds(this.#t.Meta.traceBounds),r=this.height()/2,a=this.width(),s=a/n.range,o=new Path2D,l=new Path2D;for(const e of this.#t.NetworkRequests.byTime){const t=rn.has(e.args.data.priority)?o:l,{startTime:c,endTime:d}=i.Helpers.Timing.eventTimingsMilliSeconds(e),h=Math.max(Math.floor((c-n.min)*s),0),m=Math.min(Math.ceil((d-n.min)*s+1),a);t.rect(h,0,m-h,r-1)}const c=this.context();c.save(),c.fillStyle="hsl(214, 60%, 60%)",c.fill(o),c.translate(0,r),c.fillStyle="hsl(214, 80%, 80%)",c.fill(l),c.restore()}}const sn=new WeakMap;class on extends nn{backgroundCanvas;#t;#at=!1;#st;#ot;constructor(e){super("cpu-activity",tn(Qi.cpu)),this.#t=e,this.backgroundCanvas=this.element.createChild("canvas","fill background"),this.#st=i.Helpers.Timing.traceWindowMilliSeconds(e.Meta.traceBounds).min,this.#ot=i.Helpers.Timing.traceWindowMilliSeconds(e.Meta.traceBounds).max}#lt(e){if(i.Types.TraceEvents.isProfileCall(e)&&"(idle)"===e.callFrame.functionName)return ie.IDLE;return(de(e.name)?.category||me().other).name}resetCanvas(){super.resetCanvas(),this.#at=!1,this.backgroundCanvas.width=this.element.clientWidth*window.devicePixelRatio,this.backgroundCanvas.height=this.element.clientHeight*window.devicePixelRatio}#ct(e){const t=4*window.devicePixelRatio,n=this.width(),r=this.height(),a=r,s=this.#ot-this.#st,o=t/(n/s),l=me(),c=Te(),d=c.indexOf(ie.OTHER);console.assert(0===c.indexOf(ie.IDLE));for(let e=0;e<c.length;++e)sn.set(l[c[e]],e);const h=(e,h)=>{const m=new hn(this.#st,o,(function(e){let i=a;for(let n=1;n<c.length;++n){i-=(e[n]||0)/o*r,g[n].bezierCurveTo(p,v[n],p,i,p+t/2,i),v[n]=i}p+=t}));let p=0;const u=[],g=[],v=[];for(let e=0;e<c.length;++e)g[e]=new Path2D,g[e].moveTo(0,r),v[e]=r;const T=i.Helpers.Timing.millisecondsToMicroseconds(this.#st),f=i.Helpers.Timing.millisecondsToMicroseconds(this.#ot),y={min:T,max:f,range:i.Types.Timing.MicroSeconds(f-T)},w=i.Types.Timing.MicroSeconds(y.range>2e5?16e3:0);i.Helpers.TreeHelpers.walkEntireTree(h.entryToNode,h.tree,(e=>{const t=this.#lt(e);if(!t||"idle"===t)return;const n=i.Helpers.Timing.microSecondsToMilliseconds(e.ts),r=u.length?u[u.length-1]:0;m.appendInterval(n,r);const a=c.indexOf(t);u.push(a||d)}),(function(e){const t=i.Helpers.Timing.microSecondsToMilliseconds(e.ts)+i.Helpers.Timing.microSecondsToMilliseconds(i.Types.Timing.MicroSeconds(e.dur||0)),n=u.pop();void 0!==t&&n&&m.appendInterval(t,n)}),y,w),m.appendInterval(this.#st+s+o,0);for(let t=c.length-1;t>0;--t){g[t].lineTo(n,r);const i=l[c[t]].getComputedColorValue();e.fillStyle=i,e.fill(g[t]),e.strokeStyle="white",e.lineWidth=1,e.stroke(g[t])}},m=this.backgroundCanvas.getContext("2d");if(!m)throw new Error("Could not find 2d canvas");const p=i.Handlers.Threads.threadsInTrace(e),u=this.context();for(const e of p){h("MAIN_THREAD"===e.type||"CPU_PROFILE"===e.type?u:m,e)}!function(e){const t=4*window.devicePixelRatio;e.save(),e.lineWidth=t/Math.sqrt(8);for(let i=.5;i<n+r;i+=t)e.moveTo(i,0),e.lineTo(i-r,r);e.globalCompositeOperation="destination-out",e.stroke(),e.restore()}(m)}update(){const e=h.TraceBounds.BoundsManager.instance().state(),t=e?.milli.minimapTraceBounds;t&&(t.min===this.#st&&t.max===this.#ot&&this.#at||(this.#st=t.min,this.#ot=t.max,this.resetCanvas(),this.#at=!0,this.#ct(this.#t)))}}class ln extends nn{#t;constructor(e){super("responsiveness",null),this.#t=e}#dt(){const{topLevelRendererIds:e}=this.#t.Meta,t=new Set(["LONG_TASK","FORCED_REFLOW","IDLE_CALLBACK_OVER_TIME"]),i=new Set;for(const n of t){const t=this.#t.Warnings.perWarning.get(n);if(t)for(const n of t)e.has(n.pid)&&i.add(n)}return i}update(e,t){this.resetCanvas();const n=this.height(),r=e&&t?{min:i.Helpers.Timing.millisecondsToMicroseconds(e),max:i.Helpers.Timing.millisecondsToMicroseconds(t),range:i.Helpers.Timing.millisecondsToMicroseconds(i.Types.Timing.MilliSeconds(t-e))}:this.#t.Meta.traceBounds,a=r.range,s=this.width()/a,o=this.context(),l=new Path2D,c=new Path2D,d=this.#dt();for(const e of d)h(e);function h(e){const{startTime:t,duration:a}=i.Helpers.Timing.eventTimingsMicroSeconds(e),o=Math.round(s*(t-r.min)),d=Math.round(s*a);l.rect(o,0,d,n),c.moveTo(o+d,0),c.lineTo(o+d,n)}o.fillStyle="hsl(0, 80%, 90%)",o.strokeStyle="red",o.lineWidth=2*window.devicePixelRatio,o.fill(l),o.stroke(c)}}class cn extends nn{frameToImagePromise;lastFrame=null;lastElement;drawGeneration;emptyImage;#G=null;constructor(e){super("filmstrip",null),this.frameToImagePromise=new Map,this.#G=e,this.lastFrame=null,this.lastElement=null,this.reset()}update(e,t){this.resetCanvas();const i=this.#G?this.#G.frames:[];if(!i.length)return;if(0===this.height())return void console.warn("TimelineFilmStrip could not be drawn as its canvas height is 0");const n=Symbol("drawGeneration");this.drawGeneration=n,this.imageByFrame(i[0]).then((i=>{if(this.drawGeneration!==n)return;if(!i||!i.naturalWidth||!i.naturalHeight)return;const r=this.height()-2*cn.Padding,a=Math.ceil(r*i.naturalWidth/i.naturalHeight),s=Math.min(200/i.naturalWidth,1);this.emptyImage=new Image(i.naturalWidth*s,i.naturalHeight*s),this.drawFrames(a,r,e,t)}))}async imageByFrame(e){let t=this.frameToImagePromise.get(e);return t||(t=u.UIUtils.loadImage(e.screenshotEvent.args.dataUri),this.frameToImagePromise.set(e,t)),t}drawFrames(e,t,n,r){if(!e)return;if(!this.#G||this.#G.frames.length<1)return;const a=cn.Padding,s=this.width(),o=n??i.Helpers.Timing.microSecondsToMilliseconds(this.#G.zeroTime),l=(r?r-o:i.Helpers.Timing.microSecondsToMilliseconds(this.#G.spanTime))/s,c=this.context(),d=this.drawGeneration;c.beginPath();for(let n=a;n<s;n+=e+2*a){const r=i.Types.Timing.MilliSeconds(o+(n+e/2)*l),a=i.Helpers.Timing.millisecondsToMicroseconds(r),s=i.Extras.FilmStrip.frameClosestToTimestamp(this.#G,a);s&&(c.rect(n-.5,.5,e+1,t+1),this.imageByFrame(s).then(h.bind(this,n)))}function h(i,n){this.drawGeneration===d&&n&&c.drawImage(n,i,1,e,t)}c.strokeStyle="#ddd",c.stroke()}async overviewInfoPromise(e){if(!this.#G||0===this.#G.frames.length)return null;const t=this.calculator();if(!t)return null;const n=i.Types.Timing.MilliSeconds(t.positionToTime(e)),r=i.Helpers.Timing.millisecondsToMicroseconds(n),a=i.Extras.FilmStrip.frameClosestToTimestamp(this.#G,r);if(a===this.lastFrame)return this.lastElement;const s=a?this.imageByFrame(a):Promise.resolve(this.emptyImage),o=await s,l=document.createElement("div");return l.classList.add("frame"),o&&l.createChild("div","thumbnail").appendChild(o),this.lastFrame=a,this.lastElement=l,l}reset(){this.lastFrame=null,this.lastElement=null,this.frameToImagePromise=new Map}static Padding=2}class dn extends nn{heapSizeLabel;#t;constructor(e){super("memory",tn(Qi.heap)),this.heapSizeLabel=this.element.createChild("div","memory-graph-label"),this.#t=e}resetHeapSizeLabels(){this.heapSizeLabel.textContent=""}update(e,t){this.resetCanvas();const n=window.devicePixelRatio;if(0===this.#t.Memory.updateCountersByProcess.size)return void this.resetHeapSizeLabels();const r=Array.from(this.#t.Meta.topLevelRendererIds).map((e=>this.#t.Memory.updateCountersByProcess.get(e)||[])).filter((e=>e.length>0)),a=3*n;let s=0,o=1e11;const l=e&&t?{min:e,max:t,range:t-e}:i.Helpers.Timing.traceWindowMilliSeconds(this.#t.Meta.traceBounds),d=l.min,h=l.max;function m(e){const t=e.args.data;t&&t.jsHeapSizeUsed&&(s=Math.max(s,t.jsHeapSizeUsed),o=Math.min(o,t.jsHeapSizeUsed))}for(let e=0;e<r.length;e++)r[e].forEach(m);o=Math.min(o,s);const p=this.width(),u=this.height()-a,g=p/(h-d),v=(u-1)/Math.max(s-o,1),T=new Array(p);function f(e){const t=e.args.data;if(!t||!t.jsHeapSizeUsed)return;const{startTime:n}=i.Helpers.Timing.eventTimingsMilliSeconds(e),r=Math.round((n-d)*g),a=Math.round((t.jsHeapSizeUsed-o)*v);T[r]=Math.max(T[r]||0,a)}for(let e=0;e<r.length;e++)r[e].forEach(f);const y=this.context(),w=u+a+1;y.translate(.5,.5),y.beginPath(),y.moveTo(-1,w);let b=0,S=!0,C=0;for(let e=0;e<T.length;e++){if(void 0===T[e])continue;S&&(S=!1,b=T[e],y.lineTo(-1,u-b));const t=T[e];Math.abs(t-b)>2&&Math.abs(e-C)>1&&y.lineTo(e,u-b),b=t,y.lineTo(e,u-b),C=e}y.lineTo(p+1,u-b),y.lineTo(p+1,w),y.closePath(),y.fillStyle="hsla(220, 90%, 70%, 0.2)",y.fill(),y.lineWidth=1,y.strokeStyle="hsl(220, 90%, 70%)",y.stroke(),this.heapSizeLabel.textContent=tn(Qi.sSDash,{PH1:c.NumberUtilities.bytesToString(o),PH2:c.NumberUtilities.bytesToString(s)})}}class hn{lastTime;quantDuration;callback;counters;remainder;constructor(e,t,i){this.lastTime=e,this.quantDuration=t,this.callback=i,this.counters=[],this.remainder=t}appendInterval(e,t){let i=e-this.lastTime;if(i<=this.remainder)return this.counters[t]=(this.counters[t]||0)+i,this.remainder-=i,void(this.lastTime=e);for(this.counters[t]=(this.counters[t]||0)+this.remainder,this.callback(this.counters),i-=this.remainder;i>=this.quantDuration;){const e=[];e[t]=this.quantDuration,this.callback(e),i-=this.quantDuration}this.counters=[],this.counters[t]=i,this.lastTime=e,this.remainder=this.quantDuration-i}}var mn=Object.freeze({__proto__:null,TimelineEventOverview:nn,TimelineEventOverviewNetwork:an,TimelineEventOverviewCPUActivity:on,TimelineEventOverviewResponsiveness:ln,TimelineFilmStripOverview:cn,TimelineEventOverviewMemory:dn,Quantizer:hn});const pn=new CSSStyleSheet;pn.replaceSync(".drop-down{padding:1px;box-shadow:var(--drop-shadow);background:var(--sys-color-cdt-base-container)}.preview-item{border-color:transparent;border-style:solid;border-width:1px 5px;padding:2px 0;margin:2px 1px}.preview-item.selected{border-color:var(--sys-color-primary)}.preview-item canvas{width:100%;height:100%}.text-details{font-size:11px;padding:3px}.text-details span{flex:1 0;padding-left:8px;padding-right:8px}.text-details .name{font-weight:bold}.text-details span.time{color:var(--sys-color-token-subtle);text-align:right}.screenshot-thumb{display:flex;border:1px solid var(--sys-color-neutral-outline);margin:2px 4px}.screenshot-thumb img{margin:auto;max-width:100%;max-height:100%}\n/*# sourceURL=timelineHistoryManager.css */\n");const un={currentSessionSS:"Current Session: {PH1}. {PH2}",noRecordings:"(no recordings)",sAgo:"({PH1} ago)",moments:"moments",sM:"{PH1} m",sH:"{PH1} h",sD:"{PH1} #{PH2}",selectTimelineSession:"Select Timeline Session"},gn=e.i18n.registerUIStrings("panels/timeline/TimelineHistoryManager.ts",un),vn=e.i18n.getLocalizedString.bind(void 0,gn);class Tn{recordings;action;nextNumberByDomain;buttonInternal;allOverviews;totalHeight;enabled;lastActiveTraceIndex=null;#ht;constructor(e){this.recordings=[],this.#ht=e,this.action=u.ActionRegistry.ActionRegistry.instance().getAction("timeline.show-history"),this.nextNumberByDomain=new Map,this.buttonInternal=new Sn(this.action),u.ARIAUtils.markAsMenuButton(this.buttonInternal.element),this.clear(),this.allOverviews=[{constructor:e=>{const t=this.#ht?.getControls().find((e=>e instanceof ln));return t||new ln(e)},height:3},{constructor:e=>{const t=this.#ht?.getControls().find((e=>e instanceof on));return t||new on(e)},height:20},{constructor:e=>{const t=this.#ht?.getControls().find((e=>e instanceof an));return t||new an(e)},height:8}],this.totalHeight=this.allOverviews.reduce(((e,t)=>e+t.height),0),this.enabled=!0}addRecording(e){const{traceParseDataIndex:t}=e.data,i=e.filmStripForPreview;this.lastActiveTraceIndex=t,this.recordings.unshift({traceParseDataIndex:t}),this.#mt(t,e.traceParsedData,i,e.startTime);const n=this.title(t);this.buttonInternal.setText(n);const r=this.action.title();if(u.ARIAUtils.setLabel(this.buttonInternal.element,vn(un.currentSessionSS,{PH1:n,PH2:r})),this.updateState(),this.recordings.length<=fn)return;const a=this.recordings.reduce(((e,t)=>s(e.traceParseDataIndex)<s(t.traceParseDataIndex)?e:t));function s(e){const t=Tn.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");return t.lastUsed}this.recordings.splice(this.recordings.indexOf(a),1)}setEnabled(e){this.enabled=e,this.updateState()}button(){return this.buttonInternal}clear(){this.recordings=[],this.lastActiveTraceIndex=null,this.updateState(),this.buttonInternal.setText(vn(un.noRecordings)),this.nextNumberByDomain.clear()}async showHistoryDropDown(){if(this.recordings.length<2||!this.enabled)return null;const e=await bn.show(this.recordings.map((e=>e.traceParseDataIndex)),this.lastActiveTraceIndex,this.buttonInternal.element);if(null===e)return null;const t=this.recordings.findIndex((t=>t.traceParseDataIndex===e));return t<0?(console.assert(!1,"selected recording not found"),null):(this.setCurrentModel(e),this.recordings[t])}cancelIfShowing(){bn.cancelIfShowing()}navigate(e){if(!this.enabled||null===this.lastActiveTraceIndex)return null;const t=this.recordings.findIndex((e=>e.traceParseDataIndex===this.lastActiveTraceIndex));if(t<0)return null;const i=c.NumberUtilities.clamp(t+e,0,this.recordings.length-1),{traceParseDataIndex:n}=this.recordings[i];return this.setCurrentModel(n),this.recordings[i]}setCurrentModel(e){const t=Tn.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");t.lastUsed=Date.now(),this.lastActiveTraceIndex=e;const i=this.title(e),n=this.action.title();this.buttonInternal.setText(i),u.ARIAUtils.setLabel(this.buttonInternal.element,vn(un.currentSessionSS,{PH1:i,PH2:n}))}updateState(){this.action.setEnabled(this.recordings.length>1&&this.enabled)}static previewElement(e){const t=Tn.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");const i=t.startTime;return t.time.textContent=i?vn(un.sAgo,{PH1:Tn.coarseAge(i)}):"",t.preview}static coarseAge(e){const t=Math.round((Date.now()-e)/1e3);if(t<50)return vn(un.moments);const i=Math.round(t/60);if(i<50)return vn(un.sM,{PH1:i});const n=Math.round(i/60);return vn(un.sH,{PH1:n})}title(e){const t=Tn.dataForTraceIndex(e);if(!t)throw new Error("Unable to find data for model");return t.title}#mt(e,t,i,n){const a=r.ParsedURL.ParsedURL.fromString(t.Meta.mainFrameURL),s=a?a.host:"",o=this.nextNumberByDomain.get(s)||1,l=vn(un.sD,{PH1:s,PH2:o});this.nextNumberByDomain.set(s,o+1);const c=document.createElement("span"),d=document.createElement("div");d.classList.add("preview-item"),d.classList.add("vbox"),d.setAttribute("jslog",`${g.dropDown("timeline.history-item").track({click:!0})}`);const h={preview:d,title:l,time:c,lastUsed:Date.now(),startTime:n};wn.set(e,h),d.appendChild(this.#pt(t,s,c));const m=d.createChild("div","hbox");return m.appendChild(this.#ut(i)),m.appendChild(this.#gt(t)),h.preview}#pt(t,n,r){const a=document.createElement("div");a.classList.add("text-details"),a.classList.add("hbox");const s=a.createChild("span","name");s.textContent=n,u.ARIAUtils.setLabel(s,n);const o=i.Helpers.Timing.traceWindowMilliSeconds(t.Meta.traceBounds),l=e.TimeUtilities.millisToString(o.range,!1),c=a.createChild("span","time");return c.appendChild(document.createTextNode(l)),c.appendChild(r),a}#ut(e){const t=document.createElement("div");t.classList.add("screenshot-thumb");if(t.style.width=1.5*this.totalHeight+"px",t.style.height=this.totalHeight+"px",!e)return t;const i=e.frames.at(-1);return i?(u.UIUtils.loadImage(i.screenshotEvent.args.dataUri).then((e=>{e&&t.appendChild(e)})),t):t}#gt(e){const t=document.createElement("div"),i=window.devicePixelRatio;t.style.width=yn+"px",t.style.height=this.totalHeight+"px";const n=t.createChild("canvas");n.width=i*yn,n.height=i*this.totalHeight;const r=n.getContext("2d");let a=0;for(const t of this.allOverviews){const n=t.constructor(e);n.update(),r&&r.drawImage(n.context().canvas,0,a,i*yn,t.height*i),a+=t.height*i}return t}static dataForTraceIndex(e){return wn.get(e)||null}}const fn=5,yn=450,wn=new Map;class bn{glassPane;listControl;focusRestorer;selectionDone;constructor(e){this.glassPane=new u.GlassPane.GlassPane,this.glassPane.setSizeBehavior("MeasureContent"),this.glassPane.setOutsideClickCallback((()=>this.close(null))),this.glassPane.setPointerEventsBehavior("BlockedByGlassPane"),this.glassPane.setAnchorBehavior("PreferBottom"),this.glassPane.element.addEventListener("blur",(()=>this.close(null)));const t=u.UIUtils.createShadowRootWithCoreStyles(this.glassPane.contentElement,{cssFile:[pn],delegatesFocus:void 0}).createChild("div","drop-down"),i=new u.ListModel.ListModel;this.listControl=new u.ListControl.ListControl(i,this,u.ListControl.ListMode.NonViewport),this.listControl.element.addEventListener("mousemove",this.onMouseMove.bind(this),!1),i.replaceAll(e),u.ARIAUtils.markAsMenu(this.listControl.element),u.ARIAUtils.setLabel(this.listControl.element,vn(un.selectTimelineSession)),t.appendChild(this.listControl.element),t.addEventListener("keydown",this.onKeyDown.bind(this),!1),t.addEventListener("click",this.onClick.bind(this),!1),this.focusRestorer=new u.UIUtils.ElementFocusRestorer(this.listControl.element),this.selectionDone=null}static show(e,t,i){if(bn.instance)return Promise.resolve(null);return new bn(e).show(i,t)}static cancelIfShowing(){bn.instance&&bn.instance.close(null)}show(e,t){return bn.instance=this,this.glassPane.setContentAnchorBox(e.boxInWindow()),this.glassPane.show(this.glassPane.contentElement.ownerDocument),this.listControl.element.focus(),this.listControl.selectItem(t),new Promise((e=>{this.selectionDone=e}))}onMouseMove(e){const t=e.target.enclosingNodeOrSelfWithClass("preview-item"),i=t&&this.listControl.itemForNode(t);null!==i&&this.listControl.selectItem(i)}onClick(e){e.target.enclosingNodeOrSelfWithClass("preview-item")&&this.close(this.listControl.selectedItem())}onKeyDown(e){switch(e.key){case"Tab":case"Escape":this.close(null);break;case"Enter":this.close(this.listControl.selectedItem());break;default:return}e.consume(!0)}close(e){this.selectionDone&&this.selectionDone(e),this.focusRestorer.restore(),this.glassPane.hide(),bn.instance=null}createElementForItem(e){const t=Tn.previewElement(e);return u.ARIAUtils.markAsMenuItem(t),t.classList.remove("selected"),t}heightForItem(e){return console.assert(!1,"Should not be called"),0}isItemSelectable(e){return!0}selectedItemChanged(e,t,i,n){i&&i.classList.remove("selected"),n&&n.classList.add("selected")}updateSelectedItemARIA(e,t){return!1}static instance=null}class Sn extends u.Toolbar.ToolbarItem{contentElement;constructor(e){const t=document.createElement("button");t.classList.add("history-dropdown-button"),super(t),this.contentElement=this.element.createChild("span","content"),this.element.addEventListener("click",(()=>{e.execute()}),!1),this.setEnabled(e.enabled()),e.addEventListener("Enabled",(e=>this.setEnabled(e.data))),this.setTitle(e.title())}setText(e){this.contentElement.textContent=e}}var Cn=Object.freeze({__proto__:null,TimelineHistoryManager:Tn,maxRecordings:fn,previewWidth:yn,DropDown:bn,ToolbarButton:Sn});const En={learnmore:"Learn more",wasd:"WASD",clickTheRecordButtonSOrHitSTo:"Click the record button {PH1} or hit {PH2} to start a new recording.",clickTheReloadButtonSOrHitSTo:"Click the reload button {PH1} or hit {PH2} to record the page load.",afterRecordingSelectAnAreaOf:"After recording, select an area of interest in the overview by dragging. Then, zoom and pan the timeline with the mousewheel or {PH1} keys. {PH2}"},kn=e.i18n.registerUIStrings("panels/timeline/TimelineLandingPage.ts",En),xn=e.i18n.getLocalizedString.bind(void 0,kn);class Pn extends u.Widget.VBox{isReactNative=!1;toggleRecordAction;constructor(e,t){super(),this.isReactNative=a.Runtime.experiments.isEnabled("react-native-specific-ui"),this.toggleRecordAction=e,this.contentElement.classList.add("timeline-landing-page","fill"),a.Runtime.experiments.isEnabled("timeline-observations")?this.renderLandingPage():this.renderLegacyLandingPage(t)}renderLandingPage(){const e=new u.Widget.Widget;e.contentElement.append(new s.LiveMetricsView.LiveMetricsView),e.show(this.contentElement)}renderLegacyLandingPage(t){function i(e,t){const i=document.createElement(e);return i.textContent=t,i}const n=u.XLink.XLink.create("https://developer.chrome.com/docs/devtools/evaluate-performance/",xn(En.learnmore),void 0,void 0,"learn-more"),r=i("b",u.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("timeline.toggle-recording")[0].title()),a=this.isReactNative?null:i("b",u.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("timeline.record-reload")[0].title()),s=i("b",xn(En.wasd));this.contentElement.classList.add("legacy");const o=this.contentElement.createChild("div"),l=u.UIUtils.createInlineButton(u.Toolbar.Toolbar.createActionButton(this.toggleRecordAction)),c=this.isReactNative?null:u.UIUtils.createInlineButton(u.Toolbar.Toolbar.createActionButtonForId("timeline.record-reload"));if(o.createChild("p").appendChild(e.i18n.getFormatLocalizedString(kn,En.clickTheRecordButtonSOrHitSTo,{PH1:l,PH2:r})),this.isReactNative||null===c||null===a||o.createChild("p").appendChild(e.i18n.getFormatLocalizedString(kn,En.clickTheReloadButtonSOrHitSTo,{PH1:c,PH2:a})),o.createChild("p").appendChild(e.i18n.getFormatLocalizedString(kn,En.afterRecordingSelectAnAreaOf,{PH1:s,PH2:n})),t?.isNode){const e=new D.PanelFeedback.PanelFeedback;e.data={feedbackUrl:"https://crbug.com/1354548",quickStartUrl:"https://goo.gle/js-profiler-deprecation",quickStartLinkText:xn(En.learnmore)},o.appendChild(e);const t=new D.FeedbackButton.FeedbackButton;t.data={feedbackUrl:"https://crbug.com/1354548"},o.appendChild(t)}}}const In={malformedTimelineDataS:"Malformed timeline data: {PH1}"},Mn=e.i18n.registerUIStrings("panels/timeline/TimelineLoader.ts",In),Fn=e.i18n.getLocalizedString.bind(void 0,Mn);class Ln{client;canceledCallback;buffer;firstRawChunk;totalSize;filter;#vt;#C=[];#Tt;#ft;#yt;constructor(e){this.client=e,this.canceledCallback=null,this.buffer="",this.firstRawChunk=!0,this.filter=null,this.#vt=!1,this.#Tt=null,this.#yt=new Promise((e=>{this.#ft=e}))}static async loadFromFile(e,t){const i=new Ln(t),n=new b.FileUtils.ChunkedFileReader(e);return i.canceledCallback=n.cancel.bind(n),i.totalSize=e.size,setTimeout((async()=>{!await n.read(i)&&n.error()&&i.reportErrorAndCancelLoading(n.error().message)})),i}static loadFromEvents(e,t){const i=new Ln(t);return window.setTimeout((async()=>{i.addEvents(e)})),i}static loadFromCpuProfile(e,t){const n=new Ln(t);n.#vt=!0;try{const t=C.TimelineJSProfile.TimelineJSProfileProcessor.createFakeTraceFromCpuProfile(e,i.Types.TraceEvents.ThreadID(1));window.setTimeout((async()=>{n.addEvents(t)}))}catch(e){console.error(e.stack)}return n}static async loadFromURL(e,t){const i=new Ln(t),n=new r.StringOutputStream.StringOutputStream;await t.loadingStarted();const a=r.Settings.Settings.instance().moduleSetting("network.enable-remote-file-loading").get();return l.ResourceLoader.loadAsStream(e,null,n,(async function(e,t,r){if(!e)return i.reportErrorAndCancelLoading(r.message);try{const e=n.data(),t=JSON.parse(e);i.#wt(t),await i.close()}catch(e){await i.close();const t=e instanceof Error?e.message:"";return i.reportErrorAndCancelLoading(Fn(In.malformedTimelineDataS,{PH1:t}))}}),a),i}#wt(e){if("traceEvents"in e||Array.isArray(e)){const t=Array.isArray(e)?e:e.traceEvents;this.#bt(t)}else{if(!e.nodes)return void this.reportErrorAndCancelLoading(Fn(In.malformedTimelineDataS));this.#St(e),this.#vt=!0}"metadata"in e&&(this.#Tt=e.metadata)}async addEvents(e){await(this.client?.loadingStarted());const t=15e4;for(let i=0;i<e.length;i+=t){const n=e.slice(i,i+t);this.#bt(n),await(this.client?.loadingProgress((i+n.length)/e.length)),await new Promise((e=>window.setTimeout(e,0)))}this.close()}async cancel(){this.client&&(await this.client.loadingComplete([],null,!1,null,null),this.client=null),this.canceledCallback&&this.canceledCallback()}async write(e,t){if(!this.client)return Promise.resolve();if(this.buffer+=e,this.firstRawChunk)await this.client.loadingStarted(),await new Promise((e=>requestAnimationFrame((()=>requestAnimationFrame(e))))),this.firstRawChunk=!1;else{let e;e=this.buffer.length/this.totalSize,e=e>1?e-Math.floor(e):e,await this.client.loadingProgress(e)}if(t){let e;try{return e=JSON.parse(this.buffer),this.#wt(e),Promise.resolve()}catch(e){return void this.reportErrorAndCancelLoading(Fn(In.malformedTimelineDataS,{PH1:e.toString()}))}}}reportErrorAndCancelLoading(e){e&&r.Console.Console.instance().error(e),this.cancel()}async close(){this.client&&(await this.client.processingStarted(),await this.finalizeTrace())}isCpuProfile(){return this.#vt}async finalizeTrace(){await this.client.loadingComplete(this.#C,this.filter,this.isCpuProfile(),null,this.#Tt),this.#ft?.()}traceFinalizedForTest(){return this.#yt}#St(e){const t=C.TimelineJSProfile.TimelineJSProfileProcessor.createFakeTraceFromCpuProfile(e,i.Types.TraceEvents.ThreadID(1));this.#bt(t)}#bt(e){this.#C=this.#C.concat(e)}}var Rn=Object.freeze({__proto__:null,TimelineLoader:Ln});const Dn=new CSSStyleSheet;Dn.replaceSync('.timeline-minimap{position:relative}.timeline-sidebar-floating-icon{position:absolute;top:5px;left:10px;z-index:999;border:none;width:36px;height:36px;border-radius:50%;box-shadow:var(--drop-shadow-depth-1);background:var(--sys-color-cdt-base-container);&:hover{background:var(--sys-color-base-container-elevated)}}.timeline-minimap .overview-strip{margin-top:2px;justify-content:center}.timeline-minimap .overview-strip .timeline-overview-strip-title{color:var(--sys-color-token-subtle);font-size:10px;font-weight:bold;z-index:100;background-color:var(--sys-color-cdt-base-container);padding:0 4px;position:absolute;top:-2px;right:0}.timeline-minimap #timeline-overview-cpu-activity{flex-basis:20px}.timeline-minimap #timeline-overview-network{flex-basis:8px}.timeline-minimap #timeline-overview-filmstrip{flex-basis:30px}.timeline-minimap #timeline-overview-memory{flex-basis:20px}.timeline-minimap #timeline-overview-network::before,\n.timeline-minimap #timeline-overview-cpu-activity::before{content:"";position:absolute;left:0;right:0;bottom:0;border-bottom:1px solid var(--divider-line);z-index:-200}.timeline-minimap .overview-strip .background{z-index:-10}.timeline-minimap #timeline-overview-responsiveness{flex-basis:5px;margin-top:0!important}.timeline-minimap #timeline-overview-input{flex-basis:6px}.timeline-minimap #timeline-overview-pane{flex:auto;position:relative;overflow:hidden}.timeline-minimap #timeline-overview-container{display:flex;flex-direction:column;flex:none;position:relative;overflow:hidden}.timeline-minimap #timeline-overview-container canvas{width:100%;height:100%}.timeline-minimap .memory-graph-label{position:absolute;right:0;bottom:0;font-size:9px;color:var(--sys-color-token-subtle);white-space:nowrap;padding:0 4px;background-color:var(--sys-color-cdt-base-container)}\n/*# sourceURL=timelineMiniMap.css */\n');const An={openSidebarButton:"Open the sidebar"},Nn=e.i18n.registerUIStrings("panels/timeline/TimelineMiniMap.ts",An),Bn=e.i18n.getLocalizedString.bind(void 0,Nn);class Hn extends(r.ObjectWrapper.eventMixin(u.Widget.VBox)){breadcrumbsActivated=!1;#Ct=new p.TimelineOverviewPane.TimelineOverviewPane("timeline");#Et=[];breadcrumbs=null;#kt;#xt=null;#x=this.#P.bind(this);#Pt=document.createElement("button");constructor(){super(),this.element.classList.add("timeline-minimap"),this.#kt=new s.BreadcrumbsUI.BreadcrumbsUI;const e=new A.Icon.Icon;e.setAttribute("name","left-panel-open"),e.setAttribute("jslog",`${g.action("timeline.sidebar-open").track({click:!0})}`),e.addEventListener("click",(()=>{this.dispatchEventToListeners("OpenSidebarButtonClicked",{})})),this.#Pt.setAttribute("aria-label",Bn(An.openSidebarButton)),this.#Pt.appendChild(e),this.#Pt.classList.add("timeline-sidebar-floating-icon"),a.Runtime.experiments.isEnabled("timeline-rpp-sidebar")||this.hideSidebarFloatingIcon(),this.element.appendChild(this.#Pt),this.#Ct.show(this.element),this.#Ct.addEventListener("OverviewPaneWindowChanged",(e=>{this.#It(e)})),this.#Mt(),h.TraceBounds.onChange(this.#x)}showSidebarFloatingIcon(){this.#Pt.removeAttribute("hidden")}hideSidebarFloatingIcon(){this.#Pt.setAttribute("hidden","hidden")}#It(e){const t=this.#xt?.traceParsedData;if(!t)return;const n=h.TraceBounds.BoundsManager.instance().state();if(!n)return;const r=e.data.startTime>0?e.data.startTime:n.milli.entireTraceBounds.min,a=Number.isFinite(e.data.endTime)?e.data.endTime:n.milli.entireTraceBounds.max;h.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(i.Helpers.Timing.traceWindowFromMilliSeconds(i.Types.Timing.MilliSeconds(r),i.Types.Timing.MilliSeconds(a)),{shouldAnimate:!0})}#P(e){"RESET"!==e.updateType&&"VISIBLE_WINDOW"!==e.updateType||this.#Ct.setWindowTimes(e.state.milli.timelineTraceWindow.min,e.state.milli.timelineTraceWindow.max),"RESET"!==e.updateType&&"MINIMAP_BOUNDS"!==e.updateType||this.#Ct.setBounds(e.state.milli.minimapTraceBounds.min,e.state.milli.minimapTraceBounds.max)}#Mt(){this.breadcrumbsActivated=!0,this.element.prepend(this.#kt),this.#Ct.addEventListener("OverviewPaneBreadcrumbAdded",(e=>{this.addBreadcrumb(e.data)})),this.#kt.addEventListener(s.BreadcrumbsUI.BreadcrumbRemovedEvent.eventName,(e=>{const t=e.breadcrumb;this.#Ft(t)})),this.#Ct.enableCreateBreadcrumbsButton()}addBreadcrumb({startTime:e,endTime:t}){const n=h.TraceBounds.BoundsManager.instance().state();if(!n)return;const r=n.milli.minimapTraceBounds,a={startTime:i.Types.Timing.MilliSeconds(Math.max(e,r.min)),endTime:i.Types.Timing.MilliSeconds(Math.min(t,r.max))},s=i.Helpers.Timing.traceWindowFromMilliSeconds(a.startTime,a.endTime);null===this.breadcrumbs?this.breadcrumbs=Ne.activeManager()?.getTimelineBreadcrumbs()??null:this.breadcrumbs.add(s),this.breadcrumbs?this.#kt.data={breadcrumb:this.breadcrumbs.initialBreadcrumb}:console.warn("ModificationsManager has not been created, therefore Breadcrumbs can not be added")}#Ft(e){this.breadcrumbs&&(this.breadcrumbs.setLastBreadcrumb(e),this.#kt.data={breadcrumb:this.breadcrumbs.initialBreadcrumb})}wasShown(){super.wasShown(),this.registerCSSFiles([Dn])}reset(){this.#xt=null,this.#Ct.reset()}#Lt(e){const t=new Map,{Meta:n,PageLoadMetrics:r}=e,a=n.mainFrameNavigations,s=i.Helpers.Timing.microSecondsToMilliseconds(n.traceBounds.min);for(const e of a){const{startTime:n}=i.Helpers.Timing.eventTimingsMilliSeconds(e);t.set(n,Ct.createEventDivider(e,s))}for(const e of r.allMarkerEvents){const{startTime:n}=i.Helpers.Timing.eventTimingsMilliSeconds(e);t.set(n,Ct.createEventDivider(e,s))}this.#Ct.setMarkers(t)}#Rt(e){this.#Ct.setNavStartTimes(e.Meta.mainFrameNavigations)}getControls(){return this.#Et}setData(e){if(this.#xt?.traceParsedData!==e.traceParsedData){if(this.#xt=e,this.#Et=[],this.#Lt(e.traceParsedData),this.#Rt(e.traceParsedData),this.#Et.push(new ln(e.traceParsedData)),this.#Et.push(new on(e.traceParsedData)),this.#Et.push(new an(e.traceParsedData)),e.settings.showScreenshots){const t=i.Extras.FilmStrip.fromTraceData(e.traceParsedData);t.frames.length&&this.#Et.push(new cn(t))}e.settings.showMemory&&this.#Et.push(new dn(e.traceParsedData)),this.#Ct.setOverviewControls(this.#Et),this.#Ct.showingScreenshots=e.settings.showScreenshots}}addInitialBreadcrumb(){this.breadcrumbs=null;const e=h.TraceBounds.BoundsManager.instance().state();e&&this.addBreadcrumb({startTime:e.milli.entireTraceBounds.min,endTime:e.milli.entireTraceBounds.max})}}var Un=Object.freeze({__proto__:null,TimelineMiniMap:Hn});const Wn=new CSSStyleSheet;Wn.replaceSync('.timeline-toolbar-container{display:flex;flex:none}.timeline-toolbar-container > .toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.timeline-main-toolbar{flex:1 1 auto}.timeline-settings-pane{flex:none;background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}#timeline-overview-panel{flex:none;position:relative;border-bottom:1px solid var(--sys-color-divider)}#timeline-overview-grid{background-color:var(--sys-color-cdt-base-container)}#timeline-overview-grid .timeline-grid-header{height:12px}#timeline-overview-grid .resources-dividers-label-bar{pointer-events:auto;height:12px}#timeline-overview-grid .resources-divider-label{top:1px}.timeline-details-split{flex:auto}.timeline.panel .status-pane-container{z-index:1000;display:flex;align-items:center;pointer-events:none}.timeline.panel .status-pane-container.tinted{background-color:var(--sys-color-cdt-base-container);pointer-events:auto}.popover ul{margin:0;padding:0;list-style-type:none}#memory-graphs-canvas-container{overflow:hidden;flex:auto;position:relative}#memory-counters-graph{flex:auto}#memory-graphs-canvas-container .memory-counter-marker{position:absolute;border-radius:3px;width:5px;height:5px;margin-left:-3px;margin-top:-2px}#memory-graphs-container .timeline-memory-header{flex:0 0 26px;background-color:var(--sys-color-surface2);border-bottom:1px solid var(--sys-color-divider);justify-content:space-between}#memory-graphs-container .timeline-memory-header::after{content:"";background-image:var(--image-file-toolbarResizerVertical);background-repeat:no-repeat;background-position:right center,center;flex:20px 0 0;margin:0 4px}.timeline-memory-toolbar{flex-shrink:1}.memory-counter-value{margin:8px}#counter-values-bar{flex:0 0 20px;border-top:solid 1px var(--sys-color-divider);width:100%;overflow:hidden;line-height:18px}.timeline-details{vertical-align:top}.timeline-details-view{color:var(--sys-color-on-surface);overflow:hidden}.timeline-details-view-body{flex:auto;overflow:auto;position:relative;background-color:var(--sys-color-cdt-base-container);user-select:text}.timeline-details-view-block{flex:none;display:flex;background-color:var(--sys-color-cdt-base-container);flex-direction:column;padding-bottom:5px;border-bottom:1px solid var(--sys-color-divider)}.timeline-details-view-row{padding-left:10px;min-height:20px}.timeline-details-view-block .timeline-details-stack-values{flex-direction:column!important}.timeline-details-chip-title{font-size:12px;padding:8px;display:flex;align-items:center}.timeline-details-view-block:first-child > .timeline-details-chip-title{font-size:13px}.timeline-details-view-row-title:not(:empty){color:var(--sys-color-token-subtle);overflow:hidden;padding-right:10px;display:inline-block;vertical-align:top}.timeline-details-warning{--override-details-warning-background-color:rgb(250 209 209/48%);background-color:var(--override-details-warning-background-color)}.theme-with-dark-background .timeline-details-warning,\n:host-context(.theme-with-dark-background) .timeline-details-warning{--override-details-warning-background-color:rgb(87 10 10/48%)}.timeline-details-warning .timeline-details-view-row-title{color:var(--sys-color-error)}.timeline-details-view-row-value{display:inline-block;user-select:text;text-overflow:ellipsis;overflow:hidden;padding:0 3px}.timeline-details-warning .timeline-details-view-row-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timeline-details-view-pie-chart-wrapper{margin:4px 0}.timeline-details-view-pie-chart{margin-top:5px}.timeline-flamechart{overflow:hidden}.brick-game{background-color:var(--sys-color-neutral-container);position:fixed;top:0;left:0;width:100%;height:100%;z-index:9999}.game-close-button{display:flex;align-items:center;justify-content:center;width:25px;height:25px;position:absolute;right:15px;top:15px;border-radius:50%;cursor:pointer}.scorePanel{display:flex;align-items:center;justify-content:center;flex-direction:column;white-space:pre-line;padding:15px;position:absolute;left:15px;bottom:15px;border:double 7px transparent;border-radius:20px;background-origin:border-box;background-clip:content-box,border-box;font-weight:200}.confetti-100{display:block;top:0;left:0;width:100%;height:100%}.confetti-100 > .confetti-100-particle{opacity:0%;position:fixed;animation:confetti-100-animation 1s none ease-out;font-size:30px}@keyframes confetti-100-animation{0%{opacity:100%;transform:translateY(0%) translateY(0%) rotate(0deg)}100%{opacity:0%;transform:translateY(var(--to-Y)) translateX(var(--to-X)) rotate(var(--rotation))}}@media (prefers-reduced-motion){.confetti-100 > .confetti-100-particle{animation-name:dissolve}}.timeline-flamechart-resizer{flex:8px 0 0;background-color:var(--sys-color-surface2);border:1px var(--sys-color-divider);border-style:solid none;display:flex;flex-direction:row;align-items:flex-end;justify-content:center}.timeline-network-resizer-disabled > .timeline-flamechart-resizer{display:none}.timeline-flamechart-resizer::after{content:"...";font-size:14px;margin-bottom:-1px}.timeline-layers-view-properties table{width:100%;border-collapse:collapse}.timeline-layers-view-properties td{border:1px solid var(--sys-color-divider);line-height:22px}.timeline-filmstrip-preview > img{margin-top:5px;max-width:500px;max-height:300px;cursor:pointer;border:1px solid var(--sys-color-divider)}.timeline-tree-view{display:flex;overflow:hidden}.timeline-tree-view .toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.timeline-tree-view .data-grid{border:none;flex:auto}.timeline-tree-view .data-grid .data-container{overflow-y:scroll}.timeline-tree-view .data-grid.data-grid-fits-viewport .corner{display:table-cell}.timeline-tree-view .data-grid table.data{background:var(--sys-color-cdt-base-container)}.timeline-tree-view .data-grid .odd{background-color:var(--sys-color-surface1)}.timeline-tree-view .data-grid tr:hover td:not(.bottom-filler-td){background-color:var(--sys-color-state-hover-on-subtle)}.timeline-tree-view .data-grid td.numeric-column{text-align:right;position:relative}.timeline-tree-view .data-grid div.background-percent-bar{float:right;position:relative;z-index:1}.timeline-tree-view .data-grid span.percent-column{color:var(--sys-color-token-subtle);width:45px;display:inline-block}.timeline-tree-view .data-grid tr.selected span{color:inherit}.timeline-tree-view .data-grid tr.selected{background-color:var(--sys-color-tonal-container)}.timeline-tree-view .data-grid .name-container{display:flex;align-items:center;padding-left:2px}.timeline-tree-view .data-grid .name-container .activity-icon{width:12px;height:12px;border:1px solid var(--divider-line);margin:3px 0}.timeline-tree-view .data-grid .name-container .activity-icon-container{margin-right:3px;display:flex;flex-wrap:wrap;align-items:center;justify-content:center;width:18px;height:18px;overflow:hidden}.timeline-tree-view .data-grid .name-container .activity-warning::after{content:"[deopt]";margin:0 4px;line-height:12px;font-size:10px;color:var(--sys-color-state-disabled)}.timeline-tree-view .data-grid tr.selected .name-container .activity-warning::after{color:var(--sys-color-on-tonal-container)}.timeline-tree-view .data-grid .name-container .activity-link{flex:auto;text-align:right;overflow:hidden;text-overflow:ellipsis;margin-left:5px}.timeline-tree-view .data-grid .background-bar-container{position:absolute;left:3px;right:0}.timeline-tree-view .data-grid .background-bar{float:right;height:18px;background-color:var(--sys-color-surface-yellow);border-bottom:1px solid var(--sys-color-yellow-outline)}.timeline-tree-view .data-grid .selected .background-bar{background-color:var(--app-color-selected-progress-bar);border-bottom:1px solid var(--app-border-selected-progress-bar)}.timeline-tree-view .timeline-details-view-body .full-widget-dimmed-banner{background-color:inherit}.timeline-details .filter-input-field{width:120px}.timeline-tree-view .data-grid thead{height:21px;z-index:2}.timeline-stack-view-header{height:27px;background-color:var(--sys-color-cdt-base-container);padding:6px 10px;color:var(--sys-color-on-surface);white-space:nowrap;border-bottom:1px solid var(--sys-color-divider)}.timeline-landing-page{position:absolute;background-color:var(--sys-color-cdt-base-container)}.timeline-landing-page.legacy{justify-content:center;align-items:center;overflow:auto;font-size:13px;color:var(--sys-color-on-surface-subtle)}@media (forced-colors: active){.timeline-tree-view .data-grid .name-container .activity-icon{forced-color-adjust:none}.timeline-tree-view .data-grid tr.selected span.percent-column,\n  .timeline-tree-view .data-grid tr.selected div.background-percent-bar span,\n  .timeline-tree-view .data-grid tr.selected .name-container .activity-link .devtools-link{color:HighlightText}.timeline-tree-view .data-grid .background-bar,\n  .timeline-tree-view .data-grid tr:hover td:not(.bottom-filler-td){background-color:transparent}.timeline-tree-view .data-grid tr.selected .background-bar{background-color:transparent;border-bottom-color:HighlightText}}.timeline-details-view-body > div{overflow-y:hidden;overflow-x:hidden}.timeline-details-chip-title > div{width:12px;height:12px;border:1px solid var(--sys-color-divider);display:inline-block;margin-right:4px;content:" "}.timeline-landing-page.legacy > div{max-width:450px;margin:10px}.timeline-paint-profiler-log-split > div:last-child{background-color:var(--color-background-elevation-1);z-index:0}.timeline-layers-view > div:last-child,\n.timeline-layers-view-properties > div:last-child{background-color:var(--color-background-elevation-1)}.timeline.panel .status-pane-container > div{pointer-events:auto}.timeline-landing-page.legacy > div > p{flex:none;white-space:pre-line;line-height:18px}.timeline-tree-view .data-grid .name-container div{flex:none}.status-pane-container > .small-dialog{width:100%;height:100%}.timeline-concurrency-input{width:50px}.timeline-concurrency-hidden{visibility:hidden}devtools-feedback-button{float:right}\n/*# sourceURL=timelinePanel.css */\n');const On=new CSSStyleSheet;On.replaceSync(".timeline-status-dialog{display:flex;flex-direction:column;padding:16px 16px 12px;align-self:center;background-color:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);border-radius:10px}.status-dialog-line{margin:2px;height:14px;min-height:auto;display:flex;align-items:baseline}.status-dialog-line .label{display:inline-block;width:80px;text-align:right;color:var(--sys-color-on-surface);margin-right:10px}.timeline-status-dialog .progress .indicator-container{display:inline-block;width:200px;height:8px;background-color:var(--sys-color-surface5)}.timeline-status-dialog .progress .indicator{background-color:var(--sys-color-primary);height:100%;width:0;margin:0}.timeline-status-dialog .stop-button{margin-top:8px;height:100%;align-self:flex-end}.timeline-status-dialog .stop-button button{border-radius:12px}.timeline-status-dialog.small-dialog{width:inherit;justify-content:center}.small-dialog > .stop-button{align-self:center;margin-top:20px;height:initial}@media (forced-colors: active){.timeline-status-dialog{border:1px solid canvastext}.timeline-status-dialog .progress .indicator-container{border:1px solid ButtonText;background-color:ButtonFace}.timeline-status-dialog .progress .indicator{forced-color-adjust:none;background-color:ButtonText}}\n/*# sourceURL=timelineStatusDialog.css */\n");const Vn={frameStart:"Frame Start",drawFrame:"Draw Frame",layout:"Layout",rasterizing:"Rasterizing",drawing:"Drawing",painting:"Painting",system:"System",idle:"Idle",loading:"Loading",experience:"Experience",scripting:"Scripting",rendering:"Rendering",gpu:"GPU",async:"Async",messaging:"Messaging"},_n=e.i18n.registerUIStrings("panels/timeline/UIDevtoolsUtils.ts",Vn),zn=e.i18n.getLocalizedString.bind(void 0,_n);let Gn=null,jn=null;class qn{static isUiDevTools(){return"true"===a.Runtime.Runtime.queryParam("uiDevTools")}static categorizeEvents(){if(Gn)return Gn;const e=$n,t=qn.categories(),i=t.drawing,n=t.rasterizing,r=t.layout,a=t.painting,s=t.other,o={};return o[e.ViewPaint]=new se("View::Paint",a),o[e.ViewOnPaint]=new se("View::OnPaint",a),o[e.ViewPaintChildren]=new se("View::PaintChildren",a),o[e.ViewOnPaintBackground]=new se("View::OnPaintBackground",a),o[e.ViewOnPaintBorder]=new se("View::OnPaintBorder",a),o[e.LayerPaintContentsToDisplayList]=new se("Layer::PaintContentsToDisplayList",a),o[e.ViewLayout]=new se("View::Layout",r),o[e.ViewLayoutBoundsChanged]=new se("View::Layout(bounds_changed)",r),o[e.RasterTask]=new se("RasterTask",n),o[e.RasterizerTaskImplRunOnWorkerThread]=new se("RasterizerTaskImpl::RunOnWorkerThread",n),o[e.DirectRendererDrawFrame]=new se("DirectRenderer::DrawFrame",i),o[e.BeginFrame]=new se(zn(Vn.frameStart),i,!0),o[e.DrawFrame]=new se(zn(Vn.drawFrame),i,!0),o[e.NeedsBeginFrameChanged]=new se("NeedsBeginFrameChanged",i,!0),o[e.ThreadControllerImplRunTask]=new se("ThreadControllerImpl::RunTask",s),Gn=o,o}static categories(){return jn||(jn={layout:new oe(ie.LAYOUT,zn(Vn.layout),!0,"--app-color-loading-children","--app-color-loading"),rasterizing:new oe(ie.RASTERIZING,zn(Vn.rasterizing),!0,"--app-color-children","--app-color-scripting"),drawing:new oe(ie.DRAWING,zn(Vn.drawing),!0,"--app-color-rendering-children","--app-color-rendering"),painting:new oe(ie.PAINTING,zn(Vn.painting),!0,"--app-color-painting-children","--app-color-painting"),other:new oe(ie.OTHER,zn(Vn.system),!1,"--app-color-system-children","--app-color-system"),idle:new oe(ie.IDLE,zn(Vn.idle),!1,"--app-color-idle-children","--app-color-idle"),loading:new oe(ie.LOADING,zn(Vn.loading),!1,"--app-color-loading-children","--app-color-loading"),experience:new oe(ie.EXPERIENCE,zn(Vn.experience),!1,"--app-color-rendering-children","--pp-color-rendering"),messaging:new oe(ie.MESSAGING,zn(Vn.messaging),!1,"--app-color-messaging-children","--pp-color-messaging"),scripting:new oe(ie.SCRIPTING,zn(Vn.scripting),!1,"--app-color-scripting-children","--pp-color-scripting"),rendering:new oe(ie.RENDERING,zn(Vn.rendering),!1,"--app-color-rendering-children","--pp-color-rendering"),gpu:new oe(ie.GPU,zn(Vn.gpu),!1,"--app-color-painting-children","--app-color-painting"),async:new oe(ie.ASYNC,zn(Vn.async),!1,"--app-color-async-children","--app-color-async")},jn)}static getMainCategoriesList(){return["idle","drawing","painting","rasterizing","layout","other"]}}var $n;!function(e){e.ViewPaint="View::Paint",e.ViewOnPaint="View::OnPaint",e.ViewPaintChildren="View::PaintChildren",e.ViewOnPaintBackground="View::OnPaintBackground",e.ViewOnPaintBorder="View::OnPaintBorder",e.ViewLayout="View::Layout",e.ViewLayoutBoundsChanged="View::Layout(bounds_changed)",e.LayerPaintContentsToDisplayList="Layer::PaintContentsToDisplayList",e.DirectRendererDrawFrame="DirectRenderer::DrawFrame",e.RasterTask="RasterTask",e.RasterizerTaskImplRunOnWorkerThread="RasterizerTaskImpl::RunOnWorkerThread",e.BeginFrame="BeginFrame",e.DrawFrame="DrawFrame",e.NeedsBeginFrameChanged="NeedsBeginFrameChanged",e.ThreadControllerImplRunTask="ThreadControllerImpl::RunTask"}($n||($n={}));var Jn=Object.freeze({__proto__:null,UIDevtoolsUtils:qn,get RecordType(){return $n}});class Kn extends Je{constructor(e,t,i){super(e,t,i),ue(qn.categorizeEvents()),ge(qn.categories()),fe(qn.getMainCategoriesList().filter(he))}}var Yn=Object.freeze({__proto__:null,UIDevtoolsController:Kn});const Xn={dropTimelineFileOrUrlHere:"Drop timeline file or URL here",disableJavascriptSamples:"Disable JavaScript samples",enableAdvancedPaint:"Enable advanced paint instrumentation (slow)",enableSelectorStats:"Enable CSS selector stats (slow)",screenshots:"Screenshots",memory:"Memory",clear:"Clear",fixMe:"Fix me",loadProfile:"Load profile…",saveProfile:"Save profile…",captureScreenshots:"Capture screenshots",showMemoryTimeline:"Show memory timeline",captureSettings:"Capture settings",disablesJavascriptSampling:"Disables JavaScript sampling, reduces overhead when running against mobile devices",capturesAdvancedPaint:"Captures advanced paint instrumentation, introduces significant performance overhead",capturesSelectorStats:"Captures CSS selector statistics",network:"Network:",cpu:"CPU:",networkConditions:"Network conditions",failedToSaveTimelineSS:"Failed to save timeline: {PH1} ({PH2})",CpuThrottlingIsEnabled:"- CPU throttling is enabled",NetworkThrottlingIsEnabled:"- Network throttling is enabled",HardwareConcurrencyIsEnabled:"- Hardware concurrency override is enabled",SignificantOverheadDueToPaint:"- Significant overhead due to paint instrumentation",SelectorStatsEnabled:"- Selector stats is enabled",JavascriptSamplingIsDisabled:"- JavaScript sampling is disabled",stoppingTimeline:"Stopping timeline…",received:"Received",close:"Close",downloadAfterError:"Download raw trace events",recordingFailed:"Recording failed",profiling:"Profiling…",bufferUsage:"Buffer usage",loadingProfile:"Loading profile…",processingProfile:"Processing profile…",initializingProfiler:"Initializing profiler…",status:"Status",time:"Time",description:"Description",stop:"Stop",ssec:"{PH1} sec",exportNormalTraces:"Basic Performance Traces",exportEnhancedTraces:"Enhanced Performance Traces",showDataAddedByExtensions:"Show data added by extensions of the Performance panel",performanceExtension:"Extension data"},Zn=e.i18n.registerUIStrings("panels/timeline/TimelinePanel.ts",Xn),Qn=e.i18n.getLocalizedString.bind(void 0,Zn);let er,tr,ir;class nr extends u.Panel.Panel{dropTarget;recordingOptionUIControls;state;recordingPageReload;millisecondsToRecordAfterLoadEvent;toggleRecordAction;recordReloadAction;#Dt;disableCaptureJSProfileSetting;captureLayersAndPicturesSetting;captureSelectorStatsSetting;#At;showScreenshotsSetting;showMemorySetting;panelToolbar;panelRightToolbar;timelinePane;#ht=new Hn;#Nt=new s.Sidebar.SidebarWidget;statusPaneContainer;flameChart;searchableViewInternal;showSettingsPaneButton;showSettingsPaneSetting;settingsPane;controller;cpuProfiler;clearButton;fixMeButton;fixMeButtonAdded=!1;loadButton;saveButton;statusPane;landingPage;loader;showScreenshotsToolbarCheckbox;showMemoryToolbarCheckbox;networkThrottlingSelect;cpuThrottlingSelect;fileSelectorElement;selection;traceLoadStart;primaryPageTargetPromiseCallback=e=>{};primaryPageTargetPromise=new Promise((e=>{this.primaryPageTargetPromiseCallback=e}));#Bt;#Ht=-1;#Ut=null;#Wt=this.#Ot.bind(this);#Vt;constructor(){super("timeline");const e=document.createElement("span");e.innerHTML='<div style="\n      font-size: 12px;\n      transform: scale(1.25);\n      color: transparent;\n      background: linear-gradient(90deg, rgb(255 0 0 / 100%) 0%, rgb(255 154 0 / 100%) 10%, rgb(208 222 33 / 100%) 20%, rgb(79 220 74 / 100%) 30%, rgb(63 218 216 / 100%) 40%, rgb(47 201 226 / 100%) 50%, rgb(28 127 238 / 100%) 60%, rgb(95 21 242 / 100%) 70%, rgb(186 12 248 / 100%) 80%, rgb(251 7 217 / 100%) 90%, rgb(255 0 0 / 100%) 100%);\n      -webkit-background-clip: text;\n      ">💫</div>';const t=new m.Adorner.Adorner;t.classList.add("fix-perf-icon"),t.data={name:Qn(Xn.fixMe),content:e},this.fixMeButton=new u.Toolbar.ToolbarButton(Qn(Xn.fixMe),t),this.fixMeButton.addEventListener("Click",(()=>this.onFixMe()));const o=i.Types.Configuration.defaults();o.showAllEvents=a.Runtime.experiments.isEnabled("timeline-show-all-events"),o.includeRuntimeCallStats=a.Runtime.experiments.isEnabled("timeline-v8-runtime-call-stats"),o.debugMode=a.Runtime.experiments.isEnabled("timeline-debug-mode"),this.#Bt=i.TraceModel.Model.createWithAllHandlers(o),this.element.addEventListener("contextmenu",this.contextMenu.bind(this),!1),this.dropTarget=new u.DropTarget.DropTarget(this.element,[u.DropTarget.Type.File,u.DropTarget.Type.URI],Qn(Xn.dropTimelineFileOrUrlHere),this.handleDrop.bind(this)),this.recordingOptionUIControls=[],this.state="Idle",this.recordingPageReload=!1,this.millisecondsToRecordAfterLoadEvent=5e3,this.toggleRecordAction=u.ActionRegistry.ActionRegistry.instance().getAction("timeline.toggle-recording"),this.recordReloadAction=ir?null:u.ActionRegistry.ActionRegistry.instance().getAction("timeline.record-reload"),this.#Dt=new Tn(this.#ht),this.traceLoadStart=null,this.disableCaptureJSProfileSetting=r.Settings.Settings.instance().createSetting("timeline-disable-js-sampling",!1),this.disableCaptureJSProfileSetting.setTitle(Qn(Xn.disableJavascriptSamples)),this.captureLayersAndPicturesSetting=r.Settings.Settings.instance().createSetting("timeline-capture-layers-and-pictures",!1,"Session"),this.captureLayersAndPicturesSetting.setTitle(Qn(Xn.enableAdvancedPaint)),this.captureSelectorStatsSetting=r.Settings.Settings.instance().createSetting("timeline-capture-selector-stats",!1,"Session"),this.captureSelectorStatsSetting.setTitle(Qn(Xn.enableSelectorStats)),this.showScreenshotsSetting=r.Settings.Settings.instance().createSetting("timeline-show-screenshots",!tr&&!ir),this.showScreenshotsSetting.setTitle(Qn(Xn.screenshots)),this.showScreenshotsSetting.addChangeListener(this.updateMiniMap,this),ir?this.showMemorySetting=null:(this.showMemorySetting=r.Settings.Settings.instance().createSetting("timeline-show-memory",!1),this.showMemorySetting.setTitle(Qn(Xn.memory)),this.showMemorySetting.addChangeListener(this.onModeChanged,this)),this.#At=nr.extensionDataVisibilitySetting(),this.#At.addChangeListener(this.#_t,this),this.#At.setTitle(Qn(Xn.performanceExtension));const l=this.element.createChild("div","timeline-toolbar-container");l.setAttribute("jslog",`${g.toolbar()}`),this.panelToolbar=new u.Toolbar.Toolbar("timeline-main-toolbar",l),this.panelToolbar.makeWrappable(!0),this.panelRightToolbar=new u.Toolbar.Toolbar("",l),tr||ir||(this.createSettingsPane(),this.updateShowSettingsToolbarButton()),this.timelinePane=new u.Widget.VBox;const c=this.timelinePane.element.createChild("div","hbox");c.id="timeline-overview-panel",this.#ht.show(c),this.#ht.addEventListener("OpenSidebarButtonClicked",this.#zt.bind(this)),this.statusPaneContainer=this.timelinePane.element.createChild("div","status-pane-container fill"),this.createFileSelector(),n.TargetManager.TargetManager.instance().addModelListener(n.ResourceTreeModel.ResourceTreeModel,n.ResourceTreeModel.Events.Load,this.loadEventFired,this),this.flameChart=new $i(this),this.#Vt=this.#Gt.bind(this),this.flameChart.getMainFlameChart().addEventListener("ChartPlayableStateChange",this.#Vt,this),this.searchableViewInternal=new u.SearchableView.SearchableView(this.flameChart,null),this.searchableViewInternal.setMinimumSize(0,100),this.searchableViewInternal.element.classList.add("searchable-view"),this.searchableViewInternal.show(this.timelinePane.element),this.flameChart.show(this.searchableViewInternal.element),this.flameChart.setSearchableView(this.searchableViewInternal),this.searchableViewInternal.hideWidget(),this.#Nt.setMainWidget(this.timelinePane),this.#Nt.show(this.element),this.#Nt.hideSidebar(),this.#Nt.addEventListener("SidebarCollapseClick",this.#jt.bind(this)),this.#Nt.contentElement.addEventListener(T.SidebarInsight.InsightDeactivated.eventName,(()=>{this.#qt(null)})),this.#Nt.contentElement.addEventListener(T.SidebarInsight.InsightActivated.eventName,(e=>{const{name:t,navigationId:i,createOverlayFn:n}=e;this.#qt({name:t,navigationId:i,createOverlayFn:n})})),this.#Nt.contentElement.addEventListener(s.Sidebar.RemoveAnnotation.eventName,(e=>{const{removedAnnotation:t}=e;Ne.activeManager()?.removeAnnotation(t)})),this.onModeChanged(),this.populateToolbar(),this.showLandingPage(),this.updateTimelineControls(),n.TargetManager.TargetManager.instance().addEventListener("SuspendStateChanged",this.onSuspendStateChanged,this);const d=n.TargetManager.TargetManager.instance().models(n.CPUProfilerModel.CPUProfilerModel);for(const e of d)for(const t of e.registeredConsoleProfileMessages)this.consoleProfileFinished(t);n.TargetManager.TargetManager.instance().observeModels(n.CPUProfilerModel.CPUProfilerModel,{modelAdded:e=>{e.addEventListener("ConsoleProfileFinished",(e=>this.consoleProfileFinished(e.data)))},modelRemoved:e=>{}}),n.TargetManager.TargetManager.instance().observeTargets({targetAdded:e=>{e===n.TargetManager.TargetManager.instance().primaryPageTarget()&&this.primaryPageTargetPromiseCallback(e)},targetRemoved:e=>{}})}#zt(){a.Runtime.experiments.isEnabled("timeline-rpp-sidebar")&&(this.#Nt.showBoth(),this.#Nt.updateContentsOnExpand(),this.#Nt.setResizable(!1),this.#ht.hideSidebarFloatingIcon())}#jt(){a.Runtime.experiments.isEnabled("timeline-rpp-sidebar")&&(this.#ht.showSidebarFloatingIcon(),this.#Nt.hideSidebar())}#qt(e){this.#Nt.setActiveInsight(e),this.flameChart.setActiveInsight(e)}static instance(e={forceNew:null,isNode:!1}){const{forceNew:t,isNode:i}=e;return tr=i,ir=a.Runtime.experiments.isEnabled("react-native-specific-ui"),er&&!t||(er=new nr),er}static extensionDataVisibilitySetting(){return r.Settings.Settings.instance().createSetting("timeline-show-extension-data",!0)}searchableView(){return this.searchableViewInternal}wasShown(){super.wasShown(),u.Context.Context.instance().setFlavor(nr,this),this.registerCSSFiles([Wn]),l.userMetrics.panelLoaded("timeline","DevTools.Launch.Timeline")}willHide(){u.Context.Context.instance().setFlavor(nr,null),this.#Dt.cancelIfShowing()}loadFromEvents(e){"Idle"===this.state&&(this.prepareToLoadTimeline(),this.loader=Ln.loadFromEvents(e,this))}getFlameChart(){return this.flameChart}getMinimap(){return this.#ht}getTraceEngineDataForLayoutTests(){const e=this.#Bt.traceParsedData(this.#Ht);if(null===e)throw new Error("No trace engine data found.");return e}getTraceEngineRawTraceEventsForLayoutTests(){const e=this.#Bt.rawTraceEvents(this.#Ht);if(null===e)throw new Error("No trace engine data found.");return e}#Gt(e){if(e.data){const e=new Date,t=e.getUTCMonth()+1,i=e.getUTCDate();4===t&&(1===i||2===i)&&this.fixMeButtonAdded,0}else this.fixMeButtonAdded=!1,this.panelToolbar.removeToolbarItem(this.fixMeButton)}loadFromCpuProfile(e){"Idle"===this.state&&null!==e&&(this.prepareToLoadTimeline(),this.loader=Ln.loadFromCpuProfile(e,this))}setState(e){this.state=e,this.updateTimelineControls()}createSettingCheckbox(e,t){const i=new u.Toolbar.ToolbarSettingCheckbox(e,t);return this.recordingOptionUIControls.push(i),i}populateToolbar(){this.panelToolbar.appendToolbarItem(u.Toolbar.Toolbar.createActionButton(this.toggleRecordAction)),ir||null===this.recordReloadAction||this.panelToolbar.appendToolbarItem(u.Toolbar.Toolbar.createActionButton(this.recordReloadAction)),this.clearButton=new u.Toolbar.ToolbarButton(Qn(Xn.clear),"clear"),this.clearButton.addEventListener("Click",(()=>this.onClearButton())),this.panelToolbar.appendToolbarItem(this.clearButton),this.loadButton=new u.Toolbar.ToolbarButton(Qn(Xn.loadProfile),"import",void 0,"timeline.load-from-file"),this.loadButton.addEventListener("Click",(()=>{l.userMetrics.actionTaken(l.UserMetrics.Action.PerfPanelTraceImported),this.selectFileToLoad()})),this.saveButton=new u.Toolbar.ToolbarButton(Qn(Xn.saveProfile),"download",void 0,"timeline.save-to-file"),this.saveButton.addEventListener("Click",(e=>{l.userMetrics.actionTaken(l.UserMetrics.Action.PerfPanelTraceExported),this.saveToFile()})),a.Runtime.experiments.isEnabled("timeline-enhanced-traces")&&this.saveButton.element.addEventListener("contextmenu",(e=>{if(e.preventDefault(),e.stopPropagation(),e.ctrlKey||2===e.button){const t=new u.ContextMenu.ContextMenu(e);t.saveSection().appendItem(Qn(Xn.exportNormalTraces),(()=>{this.saveToFile()})),t.saveSection().appendItem(Qn(Xn.exportEnhancedTraces),(()=>{this.saveToFile(!0)})),t.show()}else this.saveToFile()})),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(this.loadButton),this.panelToolbar.appendToolbarItem(this.saveButton),this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(this.#Dt.button()),this.panelToolbar.registerCSSFiles([ke]),this.panelToolbar.appendSeparator(),this.panelToolbar.appendSeparator(),tr||ir||(this.showScreenshotsToolbarCheckbox=this.createSettingCheckbox(this.showScreenshotsSetting,Qn(Xn.captureScreenshots)),this.panelToolbar.appendToolbarItem(this.showScreenshotsToolbarCheckbox)),ir||null===this.showMemorySetting||(this.showMemoryToolbarCheckbox=this.createSettingCheckbox(this.showMemorySetting,Qn(Xn.showMemoryTimeline)),this.panelToolbar.appendToolbarItem(this.showMemoryToolbarCheckbox)),this.panelToolbar.appendToolbarItem(u.Toolbar.Toolbar.createActionButtonForId("components.collect-garbage"));const e=new Me;tr&&(this.panelToolbar.appendSeparator(),this.panelToolbar.appendToolbarItem(e)),tr||ir||(this.panelRightToolbar.appendSeparator(),this.panelRightToolbar.appendToolbarItem(this.showSettingsPaneButton))}createSettingsPane(){this.showSettingsPaneSetting=r.Settings.Settings.instance().createSetting("timeline-show-settings-toolbar",!1),this.showSettingsPaneButton=new u.Toolbar.ToolbarSettingToggle(this.showSettingsPaneSetting,"gear",Qn(Xn.captureSettings),"gear-filled","timeline-settings-toggle"),n.NetworkManager.MultitargetNetworkManager.instance().addEventListener("ConditionsChanged",this.updateShowSettingsToolbarButton,this),n.CPUThrottlingManager.CPUThrottlingManager.instance().addEventListener("RateChanged",this.updateShowSettingsToolbarButton,this),n.CPUThrottlingManager.CPUThrottlingManager.instance().addEventListener("HardwareConcurrencyChanged",this.updateShowSettingsToolbarButton,this),this.disableCaptureJSProfileSetting.addChangeListener(this.updateShowSettingsToolbarButton,this),this.captureLayersAndPicturesSetting.addChangeListener(this.updateShowSettingsToolbarButton,this),this.captureSelectorStatsSetting.addChangeListener(this.updateShowSettingsToolbarButton,this),this.settingsPane=new u.Widget.HBox,this.settingsPane.element.classList.add("timeline-settings-pane"),this.settingsPane.element.setAttribute("jslog",`${g.pane("timeline-settings-pane").track({resize:!0})}`),this.settingsPane.show(this.element);const e=new u.Toolbar.Toolbar("",this.settingsPane.element);e.element.classList.add("flex-auto"),e.makeVertical(),e.appendToolbarItem(this.createSettingCheckbox(this.disableCaptureJSProfileSetting,Qn(Xn.disablesJavascriptSampling))),e.appendToolbarItem(this.createSettingCheckbox(this.captureLayersAndPicturesSetting,Qn(Xn.capturesAdvancedPaint))),e.appendToolbarItem(this.createSettingCheckbox(this.captureSelectorStatsSetting,Qn(Xn.capturesSelectorStats)));const t=new u.Widget.VBox;t.element.classList.add("flex-auto"),t.show(this.settingsPane.element);const i=new u.Toolbar.Toolbar("",t.element);i.appendText(Qn(Xn.cpu)),this.cpuThrottlingSelect=v.ThrottlingManager.throttlingManager().createCPUThrottlingSelector(),i.appendToolbarItem(this.cpuThrottlingSelect);const a=new u.Toolbar.Toolbar("",t.element);a.appendText(Qn(Xn.network)),this.networkThrottlingSelect=this.createNetworkConditionsSelect(),a.appendToolbarItem(this.networkThrottlingSelect);const s=new u.Widget.VBox;s.element.classList.add("flex-auto"),s.show(this.settingsPane.element);const o=new u.Toolbar.Toolbar("",this.settingsPane.element);o.element.classList.add("flex-auto"),o.makeVertical(),o.appendToolbarItem(this.createSettingCheckbox(this.#At,Qn(Xn.showDataAddedByExtensions)));const{toggle:l,input:c,reset:d,warning:h}=v.ThrottlingManager.throttlingManager().createHardwareConcurrencySelector(),m=new u.Toolbar.Toolbar("",s.element);m.registerCSSFiles([Wn]),c.element.classList.add("timeline-concurrency-input"),m.appendToolbarItem(l),m.appendToolbarItem(c),m.appendToolbarItem(d),m.appendToolbarItem(h),this.showSettingsPaneSetting.addChangeListener(this.updateSettingsPaneVisibility.bind(this)),this.updateSettingsPaneVisibility()}createNetworkConditionsSelect(){const e=new u.Toolbar.ToolbarComboBox(null,Qn(Xn.networkConditions));return e.setMaxWidth(140),v.ThrottlingManager.throttlingManager().decorateSelectWithNetworkThrottling(e.selectElement()),e}prepareToLoadTimeline(){console.assert("Idle"===this.state),this.setState("Loading")}createFileSelector(){this.fileSelectorElement&&this.fileSelectorElement.remove(),this.fileSelectorElement=u.UIUtils.createFileSelectorElement(this.loadFromFile.bind(this),".json,.gz,.gzip,.cpuprofile"),this.timelinePane.element.appendChild(this.fileSelectorElement)}contextMenu(e){const t=e;if(-1!==this.flameChart.getMainFlameChart().coordinatesToEntryIndex(t.offsetX,t.offsetY))return;const i=new u.ContextMenu.ContextMenu(e,{useSoftMenu:!0});i.appendItemsAtLocation("timelineMenu"),i.show()}async saveToFile(e=!1){if("Idle"!==this.state)return;const t=this.#Bt.rawTraceEvents(this.#Ht),n=this.#Bt.metadata(this.#Ht);if(a.Runtime.experiments.isEnabled("perf-panel-annotations")&&n&&(n.modifications=Ne.activeManager()?.toJSON()),n&&e&&(n.enhancedTraceVersion=i.Handlers.ModelHandlers.EnhancedTraces.EnhancedTracesVersion),!t)return;const s=c.DateUtilities.toISO8601Compact(new Date);let o;o="CPUProfile"===n?.dataOrigin?`CPU-${s}.cpuprofile`:n&&n.enhancedTraceVersion?`EnhancedTraces-${s}.json`:`Trace-${s}.json`;try{let e;if("CPUProfile"===n?.dataOrigin){const i=t.find((e=>"CpuProfile"===e.name));if(!i||!i.args?.data)return;const n=i.args?.data;if(n.hasOwnProperty("cpuProfile")){e=We(n.cpuProfile)}}else{const i=Ue(t,n);e=Array.from(i).join("")}if(!e)throw new Error("Trace content empty");await d.FileManager.FileManager.instance().save(o,e,!0,!1),d.FileManager.FileManager.instance().close(o)}catch(e){if(console.error(e.stack),"AbortError"===e.name)return;r.Console.Console.instance().error(Qn(Xn.failedToSaveTimelineSS,{PH1:e.message,PH2:e.name}))}}async showHistory(){const e=await this.#Dt.showHistoryDropDown();this.#$t(),e&&e.traceParseDataIndex!==this.#Ht&&this.setModel(e.traceParseDataIndex)}navigateHistory(e){this.#$t();const t=this.#Dt.navigate(e);return t&&t.traceParseDataIndex!==this.#Ht&&this.setModel(t.traceParseDataIndex),!0}#$t(){const e=Ne.activeManager()?.toJSON();e&&this.#Bt.overrideModifications(this.#Ht,e)}selectFileToLoad(){this.fileSelectorElement&&this.fileSelectorElement.click()}async loadFromFile(e){"Idle"===this.state&&(this.prepareToLoadTimeline(),this.loader=await Ln.loadFromFile(e,this),this.createFileSelector())}async loadFromURL(e){"Idle"===this.state&&(this.prepareToLoadTimeline(),this.loader=await Ln.loadFromURL(e,this))}updateMiniMap(){const e=this.#Bt.traceParsedData(this.#Ht),t="CPUProfile"===this.#Bt.metadata(this.#Ht)?.dataOrigin;e&&this.#ht.setData({traceParsedData:e,isCpuProfile:t,settings:{showScreenshots:this.showScreenshotsSetting.get(),showMemory:!ir&&null!==this.showMemorySetting&&this.showMemorySetting.get()}})}onModeChanged(){this.flameChart.updateCountersGraphToggle(!ir&&null!==this.showMemorySetting&&this.showMemorySetting.get()),this.updateMiniMap(),this.doResize(),this.select(null)}#_t(){this.flameChart.extensionDataVisibilityChanged()}updateSettingsPaneVisibility(){tr||ir||(this.showSettingsPaneSetting.get()?(this.showSettingsPaneButton.setToggled(!0),this.settingsPane.showWidget()):(this.showSettingsPaneButton.setToggled(!1),this.settingsPane.hideWidget()))}updateShowSettingsToolbarButton(){const e=[];if(1!==n.CPUThrottlingManager.CPUThrottlingManager.instance().cpuThrottlingRate()&&e.push(Qn(Xn.CpuThrottlingIsEnabled)),v.ThrottlingManager.throttlingManager().hardwareConcurrencyOverrideEnabled&&e.push(Qn(Xn.HardwareConcurrencyIsEnabled)),n.NetworkManager.MultitargetNetworkManager.instance().isThrottling()&&e.push(Qn(Xn.NetworkThrottlingIsEnabled)),this.captureLayersAndPicturesSetting.get()&&e.push(Qn(Xn.SignificantOverheadDueToPaint)),this.captureSelectorStatsSetting.get()&&e.push(Qn(Xn.SelectorStatsEnabled)),this.disableCaptureJSProfileSetting.get()&&e.push(Qn(Xn.JavascriptSamplingIsDisabled)),this.showSettingsPaneButton.setDefaultWithRedColor(e.length>0),this.showSettingsPaneButton.setToggleWithRedColor(e.length>0),e.length){const t=document.createElement("div");e.forEach((e=>{t.createChild("div").textContent=e})),this.showSettingsPaneButton.setTitle(t.textContent||"")}else this.showSettingsPaneButton.setTitle(Qn(Xn.captureSettings))}setUIControlsEnabled(e){this.recordingOptionUIControls.forEach((t=>t.setEnabled(e)))}async#Jt(){if(!this.controller)return c.DevToolsPath.EmptyUrlString;const e=this.controller.primaryPageTarget.inspectedURL(),t=this.controller.primaryPageTarget.model(n.ResourceTreeModel.ResourceTreeModel),i=t&&await t.navigationHistory();if(!t||!i)return e;const{currentIndex:r,entries:a}=i;return a[r].url}async#Kt(){const e=new Promise((async(e,t)=>{if(!this.controller)return void t("Could not find TimelineController");const i=this.controller.primaryPageTarget.model(n.ResourceTreeModel.ResourceTreeModel);i?(i.addEventListener(n.ResourceTreeModel.Events.FrameNavigated,(function r(a){"about:blank"===a.data.url?e():t(`Unexpected navigation to ${a.data.url}`),i?.removeEventListener(n.ResourceTreeModel.Events.FrameNavigated,r)})),await i.navigate("about:blank")):t("Could not load resourceModel")}));await e}async#Yt(){try{if(this.cpuProfiler=u.Context.Context.instance().flavor(n.CPUProfilerModel.CPUProfilerModel),!this.cpuProfiler){const e=n.TargetManager.TargetManager.instance().targets().find((e=>e.type()===n.Target.Type.Node));if(!e)throw new Error("Could not load any Node target.");e&&(this.cpuProfiler=e.model(n.CPUProfilerModel.CPUProfilerModel))}if(this.setUIControlsEnabled(!1),this.hideLandingPage(),!this.cpuProfiler)throw new Error("No Node target is found.");await n.TargetManager.TargetManager.instance().suspendAllTargets("performance-timeline"),await this.cpuProfiler.startRecording(),this.recordingStarted()}catch(e){await this.recordingFailed(e.message)}}async#Xt(){try{const e=n.TargetManager.TargetManager.instance().rootTarget(),t=n.TargetManager.TargetManager.instance().primaryPageTarget();if(!t)throw new Error("Could not load primary page target.");if(!e)throw new Error("Could not load root target.");if(qn.isUiDevTools()?this.controller=new Kn(e,t,this):this.controller=new Je(e,t,this),this.setUIControlsEnabled(!1),this.hideLandingPage(),!this.controller)throw new Error("Could not create Timeline controller");const i=await this.#Jt();this.recordingPageReload&&await this.#Kt();const r={enableJSSampling:!this.disableCaptureJSProfileSetting.get(),capturePictures:this.captureLayersAndPicturesSetting.get(),captureFilmStrip:this.showScreenshotsSetting.get(),captureSelectorStats:this.captureSelectorStatsSetting.get()},a=await this.controller.startRecording(r);if(a.getError())throw new Error(a.getError());const s=this.recordingPageReload?{navigateToUrl:i}:void 0;this.recordingStarted(s)}catch(e){await this.recordingFailed(e.message)}}async startRecording(){this.#$t(),console.assert(!this.statusPane,"Status pane is already opened."),this.setState("StartPending"),this.showRecordingStarted(),tr?await this.#Yt():await this.#Xt()}async stopRecording(){if(this.statusPane&&(this.statusPane.finish(),this.statusPane.updateStatus(Qn(Xn.stoppingTimeline)),this.statusPane.updateProgressBar(Qn(Xn.received),0)),this.setState("StopPending"),this.controller)return await this.controller.stopRecording(),this.setUIControlsEnabled(!0),await this.controller.dispose(),void(this.controller=null);if(this.cpuProfiler){const e=await this.cpuProfiler.stopRecording();this.setState("Idle"),this.loadFromCpuProfile(e),this.setUIControlsEnabled(!0),this.cpuProfiler=null,await n.TargetManager.TargetManager.instance().resumeAllTargets()}}async recordingFailed(e,t){this.statusPane&&this.statusPane.remove(),this.statusPane=new rr({description:e,buttonText:Qn(Xn.close),buttonDisabled:!1,showProgress:void 0,showTimer:void 0},(async()=>{this.statusPane?.remove(),await this.loadingComplete([],null,!1,null,null)})),this.statusPane.showPane(this.statusPaneContainer),this.statusPane.updateStatus(Qn(Xn.recordingFailed)),t&&this.statusPane.enableDownloadOfEvents(t),this.setState("RecordingFailed"),this.traceLoadStart=null,this.setUIControlsEnabled(!0),this.controller&&(await this.controller.dispose(),this.controller=null),n.TargetManager.TargetManager.instance().resumeAllTargets()}onSuspendStateChanged(){this.updateTimelineControls()}consoleProfileFinished(e){this.loadFromCpuProfile(e.cpuProfile),u.InspectorView.InspectorView.instance().showPanel("timeline")}updateTimelineControls(){this.toggleRecordAction.setToggled("Recording"===this.state),this.toggleRecordAction.setEnabled("Recording"===this.state||"Idle"===this.state),ir||null===this.recordReloadAction||this.recordReloadAction.setEnabled(!tr&&"Idle"===this.state),this.#Dt.setEnabled("Idle"===this.state),this.clearButton.setEnabled("Idle"===this.state),this.panelToolbar.setEnabled("Loading"!==this.state),this.panelRightToolbar.setEnabled("Loading"!==this.state),this.dropTarget.setEnabled("Idle"===this.state),this.loadButton.setEnabled("Idle"===this.state),this.saveButton.setEnabled("Idle"===this.state&&this.#Zt())}async toggleRecording(){"Idle"===this.state?(this.recordingPageReload=!1,await this.startRecording(),l.userMetrics.actionTaken(l.UserMetrics.Action.TimelineStarted)):"Recording"===this.state&&await this.stopRecording()}recordReload(){"Idle"===this.state&&(this.recordingPageReload=!0,this.startRecording(),l.userMetrics.actionTaken(l.UserMetrics.Action.TimelinePageReloadStarted))}onClearButton(){this.#Dt.clear(),this.clear()}#Zt(){return this.#Ht>-1}onFixMe(){this.#Zt()&&this.flameChart.fixMe()}clear(){this.statusPane&&this.statusPane.remove(),this.showLandingPage(),this.reset()}reset(){p.LineLevelProfile.Performance.instance().reset(),this.#Ut&&(this.#Ut.removeEventListener(Ve.eventName,this.#Wt),this.#Ut.uninstall(),this.#Ut=null),this.setModel(-1)}#Qt(e,t=null){if(e||a.Runtime.experiments.isEnabled("timeline-show-all-events"))return;const i=t?[t]:[Ct.visibleEventsFilter()];be.instance().setFilters(i)}setModel(e,t=null){this.#$t(),this.#Ht=e;const r=this.#Bt.traceParsedData(this.#Ht),a="CPUProfile"===this.#Bt.metadata(this.#Ht)?.dataOrigin;if(this.#ht.reset(),r&&h.TraceBounds.BoundsManager.instance().resetWithNewBounds(r.Meta.traceBounds),this.flameChart.setModel(r,a),this.flameChart.resizeToPreferredHeights(),this.flameChart.setSelection(null),this.#Nt.setTraceParsedData(r),r?(this.searchableViewInternal.showWidget(),this.#Qt(r.Meta.traceIsGeneric,t)):this.searchableViewInternal.hideWidget(),r){const e=Ne.initAndActivateModificationsManager(this.#Bt,this.#Ht);e||console.error("ModificationsManager could not be created or activated."),e?.addEventListener(Ae.eventName,(t=>{const{overlay:i,action:n}=t;"Add"===n?this.flameChart.addOverlay(i):"Remove"===n&&this.flameChart.removeOverlay(i),this.#Nt.setAnnotationsTabContent(e.getAnnotations())})),this.#ht.breadcrumbsActivated&&this.#ht.addInitialBreadcrumb();const t=this.flameChart.getMainDataProvider().compatibilityTracksAppenderInstance().threadAppenders().at(0);if(t){const e=i.Extras.MainThreadActivity.calculateWindow(r.Meta.traceBounds,t.getEntries());h.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(e)}}r||(this.fixMeButtonAdded=!1,this.panelToolbar.removeToolbarItem(this.fixMeButton));const s=Ne.activeManager();if(s&&(s.getOverlays().forEach((e=>{this.flameChart.addOverlay(e)})),this.#Nt.setAnnotationsTabContent(s.getAnnotations())),p.LineLevelProfile.Performance.instance().reset(),r&&r.Samples.profilesInProcess.size){const e=n.TargetManager.TargetManager.instance().primaryPageTarget(),t=Array.from(r.Samples.profilesInProcess).flatMap((([e,t])=>Array.from(t.values()).map((e=>e.parsedProfile))));for(const i of t)p.LineLevelProfile.Performance.instance().appendCPUProfile(i,e)}this.updateMiniMap(),this.updateTimelineControls();const o=this.#Bt.traceInsights(this.#Ht);o&&(this.flameChart.setInsights(o),this.#Nt.setInsights(o),this.#qt(null))}recordingStarted(e){if(e&&this.recordingPageReload&&this.controller){const t=this.controller?.primaryPageTarget.model(n.ResourceTreeModel.ResourceTreeModel);if(!t)return void this.recordingFailed("Could not navigate to original URL");t.navigate(e.navigateToUrl)}this.reset(),this.setState("Recording"),this.showRecordingStarted(),this.statusPane&&(this.statusPane.enableAndFocusButton(),this.statusPane.updateStatus(Qn(Xn.profiling)),this.statusPane.updateProgressBar(Qn(Xn.bufferUsage),0),this.statusPane.startTimer()),this.hideLandingPage()}recordingProgress(e){this.statusPane&&this.statusPane.updateProgressBar(Qn(Xn.bufferUsage),100*e)}showLandingPage(){this.updateSettingsPaneVisibility(),this.#jt(),this.landingPage||(this.landingPage=new Pn(this.toggleRecordAction,{isNode:tr})),this.landingPage.show(this.statusPaneContainer)}hideLandingPage(){this.landingPage.detach(),this.showSettingsPaneButton?.setToggled(!1),this.settingsPane?.hideWidget()}async loadingStarted(){this.hideLandingPage(),this.statusPane&&this.statusPane.remove(),this.statusPane=new rr({showProgress:!0,showTimer:void 0,buttonDisabled:void 0,buttonText:void 0,description:void 0},(()=>this.cancelLoading())),this.statusPane.showPane(this.statusPaneContainer),this.statusPane.updateStatus(Qn(Xn.loadingProfile)),this.loader||this.statusPane.finish(),this.traceLoadStart=i.Types.Timing.MilliSeconds(performance.now()),await this.loadingProgress(0)}async loadingProgress(e){"number"==typeof e&&this.statusPane&&this.statusPane.updateProgressBar(Qn(Xn.received),100*e)}async processingStarted(){this.statusPane&&this.statusPane.updateStatus(Qn(Xn.processingProfile))}#Ot(){this.flameChart.refreshMainFlameChart()}async loadingComplete(e,t=null,n,r,s){this.#Bt.resetProcessor(),ze.clearResolvedNodeNames(),delete this.loader;const o="StopPending"===this.state;if(this.setState("Idle"),0!==e.length){s=s||await i.Extras.Metadata.forNewRecording(n,r??void 0);try{await this.#ei(e,o,s),this.setModel(this.#Bt.lastTraceIndex(),t),this.statusPane&&this.statusPane.remove(),this.statusPane=null,a.Runtime.experiments.isEnabled("timeline-rpp-sidebar")&&1===this.#Bt.size()&&this.#ht.showSidebarFloatingIcon();const n=this.#Bt.traceParsedData(this.#Ht);if(!n)throw new Error(`Could not get trace data at index ${this.#Ht}`);o&&Ce.instance().registerFreshRecording(n),this.#Ut=new ze(n),this.#Ut.addEventListener(Ve.eventName,this.#Wt),await this.#Ut.install(),this.#Dt.addRecording({data:{traceParseDataIndex:this.#Ht},filmStripForPreview:i.Extras.FilmStrip.fromTraceData(n),traceParsedData:n,startTime:r??null})}catch(t){this.recordingFailed(t.message,e),console.error(t)}finally{this.recordTraceLoadMetric()}}else this.#Bt.size()?(this.statusPane&&this.statusPane.remove(),this.setModel(this.#Bt.lastTraceIndex())):this.clear()}recordTraceLoadMetric(){if(!this.traceLoadStart)return;const e=this.traceLoadStart;requestAnimationFrame((()=>{setTimeout((()=>{const t=i.Types.Timing.MilliSeconds(performance.now()),n=performance.measure("TraceLoad",{start:e,end:t}),r=i.Types.Timing.MilliSeconds(n.duration);this.element.dispatchEvent(new K(r)),l.userMetrics.performanceTraceLoad(n)}),0)}))}async#ei(e,t,i){return this.#Bt.parse(e,{metadata:i,isFreshRecording:t})}loadingCompleteForTest(){}showRecordingStarted(){this.statusPane&&this.statusPane.remove(),this.statusPane=new rr({showTimer:!0,showProgress:!0,buttonDisabled:!0,description:void 0,buttonText:void 0},(()=>this.stopRecording())),this.statusPane.showPane(this.statusPaneContainer),this.statusPane.updateStatus(Qn(Xn.initializingProfiler))}cancelLoading(){this.loader&&this.loader.cancel()}async loadEventFired(e){if("Recording"!==this.state||!this.recordingPageReload||!this.controller||this.controller.primaryPageTarget!==e.data.resourceTreeModel.target())return;const t=this.controller;await new Promise((e=>window.setTimeout(e,this.millisecondsToRecordAfterLoadEvent))),t===this.controller&&"Recording"===this.state&&this.stopRecording()}frameForSelection(e){if(vt.isFrameObject(e.object))return e.object;if(vt.isRangeSelection(e.object)||vt.isSyntheticNetworkRequestDetailsEventSelection(e.object))return null;if(vt.isTraceEventSelection(e.object)){const t=this.#Bt.traceParsedData(this.#Ht);if(!t)return null;const n=i.Helpers.Timing.millisecondsToMicroseconds(e.endTime);return i.Handlers.ModelHandlers.Frames.framesWithinWindow(t.Frames.frames,n,n).at(0)||null}return console.assert(!1,"Should never be reached"),null}jumpToFrame(e){const t=this.selection&&this.frameForSelection(this.selection);if(!t)return;const n=this.#Bt.traceParsedData(this.#Ht);if(!n)return;let r=n.Frames.frames.indexOf(t);console.assert(r>=0,"Can't find current frame in the frame list"),r=c.NumberUtilities.clamp(r+e,0,n.Frames.frames.length-1);const a=n.Frames.frames[r];return this.#ti(i.Helpers.Timing.microSecondsToMilliseconds(a.startTime),i.Helpers.Timing.microSecondsToMilliseconds(a.endTime)),this.select(vt.fromFrame(a)),!0}select(e){this.selection=e,this.flameChart.setSelection(e)}selectEntryAtTime(e,t){if(e)if(0!==e.length){for(let n=c.ArrayUtilities.upperBound(e,t,((e,t)=>e-t.ts))-1;n>=0;--n){const r=e[n],{endTime:a}=i.Helpers.Timing.eventTimingsMilliSeconds(r);if(i.Helpers.Trace.isTopLevelEvent(r)&&a<t)break;if(be.instance().isVisible(r)&&a>=t)return void this.select(vt.fromTraceEvent(r))}this.select(null)}else this.select(null)}highlightEvent(e){this.flameChart.highlightEvent(e)}#ti(e,t){const n=h.TraceBounds.BoundsManager.instance().state();if(!n)return;const r=n.milli.timelineTraceWindow;let a=0;r.max<t?a=t-r.max:r.min>e&&(a=e-r.min),h.TraceBounds.BoundsManager.instance().setTimelineVisibleWindow(i.Helpers.Timing.traceWindowFromMilliSeconds(i.Types.Timing.MilliSeconds(r.min+a),i.Types.Timing.MilliSeconds(r.max+a)),{shouldAnimate:!0})}handleDrop(e){const t=e.items;if(!t.length)return;const i=t[0];if(l.userMetrics.actionTaken(l.UserMetrics.Action.PerfPanelTraceImported),"string"===i.kind){const t=e.getData("text/uri-list");new r.ParsedURL.ParsedURL(t).isValid&&this.loadFromURL(t)}else if("file"===i.kind){const e=t[0].getAsFile();if(!e)return;this.loadFromFile(e)}}}class rr extends u.Widget.VBox{status;time;progressLabel;progressBar;description;button;downloadTraceButton;startTime;timeUpdateTimer;#ii;constructor(e,t){super(!0),this.contentElement.classList.add("timeline-status-dialog");const i=this.contentElement.createChild("div","status-dialog-line status");if(i.createChild("div","label").textContent=Qn(Xn.status),this.status=i.createChild("div","content"),u.ARIAUtils.markAsStatus(this.status),e.showTimer){const e=this.contentElement.createChild("div","status-dialog-line time");e.createChild("div","label").textContent=Qn(Xn.time),this.time=e.createChild("div","content")}if(e.showProgress){const e=this.contentElement.createChild("div","status-dialog-line progress");this.progressLabel=e.createChild("div","label"),this.progressBar=e.createChild("div","indicator-container").createChild("div","indicator"),u.ARIAUtils.markAsProgressBar(this.progressBar)}if("string"==typeof e.description){const t=this.contentElement.createChild("div","status-dialog-line description");t.createChild("div","label").textContent=Qn(Xn.description),this.description=t.createChild("div","content"),this.description.innerText=e.description}const n=this.contentElement.createChild("div","stop-button");this.downloadTraceButton=u.UIUtils.createTextButton(Qn(Xn.downloadAfterError),(()=>{this.#ni()}),{jslogContext:"timeline.download-after-error"}),this.downloadTraceButton.disabled=!0,this.downloadTraceButton.style.visibility="hidden";const r=e.buttonText||Qn(Xn.stop);this.button=u.UIUtils.createTextButton(r,t,{jslogContext:"timeline.stop-recording"}),this.button.disabled=!1==!e.buttonDisabled,n.append(this.downloadTraceButton),n.append(this.button)}finish(){this.stopTimer(),this.button.disabled=!0}async#ni(){if(!this.#ii||0===this.#ii.length)return;const e=`Trace-Load-Error-${c.DateUtilities.toISO8601Compact(new Date)}.json`,t=Ue(this.#ii,{}),i=Array.from(t).join("");await d.FileManager.FileManager.instance().save(e,i,!0,!1),d.FileManager.FileManager.instance().close(e)}enableDownloadOfEvents(e){this.#ii=e,this.downloadTraceButton.disabled=!1,this.downloadTraceButton.style.visibility="visible"}remove(){this.element.parentNode&&(this.element.parentNode.classList.remove("tinted"),this.arrangeDialog(this.element.parentNode)),this.stopTimer(),this.element.remove()}showPane(e){this.arrangeDialog(e),this.show(e),e.classList.add("tinted")}enableAndFocusButton(){this.button.disabled=!1,this.button.focus()}updateStatus(e){this.status.textContent=e}updateProgressBar(e,t){this.progressLabel.textContent=e,this.progressBar.style.width=t.toFixed(1)+"%",u.ARIAUtils.setValueNow(this.progressBar,t),this.updateTimer()}startTimer(){this.startTime=Date.now(),this.timeUpdateTimer=window.setInterval(this.updateTimer.bind(this,!1),1e3),this.updateTimer()}stopTimer(){this.timeUpdateTimer&&(clearInterval(this.timeUpdateTimer),this.updateTimer(!0),delete this.timeUpdateTimer)}updateTimer(e){if(this.arrangeDialog(this.element.parentNode),!this.timeUpdateTimer||!this.time)return;const t=(Date.now()-this.startTime)/1e3;this.time.textContent=Qn(Xn.ssec,{PH1:t.toFixed(e?1:0)})}arrangeDialog(e){const t=e.clientWidth<325;this.element.classList.toggle("small-dialog",t),this.contentElement.classList.toggle("small-dialog",t)}wasShown(){super.wasShown(),this.registerCSSFiles([On])}}let ar;class sr{static instance(e={forceNew:null}){const{forceNew:t}=e;return ar&&!t||(ar=new sr),ar}handleQueryParam(e){u.ViewManager.ViewManager.instance().showView("timeline").then((async()=>{await nr.instance().loadFromURL(window.decodeURIComponent(e))}))}}var or=Object.freeze({__proto__:null,TimelinePanel:nr,rowHeight:18,headerHeight:20,StatusPane:rr,LoadTimelineHandler:sr,ActionDelegate:class{handleAction(e,t){const i=e.flavor(nr);if(null===i)return!1;switch(t){case"timeline.toggle-recording":return i.toggleRecording(),!0;case"timeline.record-reload":return i.recordReload(),!0;case"timeline.save-to-file":return i.saveToFile(),!0;case"timeline.load-from-file":return i.selectFileToLoad(),!0;case"timeline.jump-to-previous-frame":return i.jumpToFrame(-1),!0;case"timeline.jump-to-next-frame":return i.jumpToFrame(1),!0;case"timeline.show-history":return i.showHistory(),!0;case"timeline.previous-recording":return i.navigateHistory(1),!0;case"timeline.next-recording":return i.navigateHistory(-1),!0}return!1}}});let lr;class cr{#t=null;#ri=new Map;static instance(){return lr||(lr=new cr,lr)}static removeInstance(){lr=void 0}getExtensionData(){if(!nr.extensionDataVisibilitySetting().get()||!this.#t||!this.#t.ExtensionTraceData)return{extensionMarkers:[],extensionTrackData:[]};const e=this.#ri.get(this.#t);return e||this.#t.ExtensionTraceData}saveCurrentModelData(){this.#t&&!this.#ri.has(this.#t)&&this.#ri.set(this.#t,this.getExtensionData())}modelChanged(e){e!==this.#t&&(null!==this.#t&&this.saveCurrentModelData(),this.#t=e)}}var dr=Object.freeze({__proto__:null,ExtensionDataGatherer:cr});const hr={customTrackDescription:"This is a custom track added by a third party.",customTrackName:"{PH1} — Custom Track"},mr=e.i18n.registerUIStrings("panels/timeline/ExtensionTrackAppender.ts",hr),pr=e.i18n.getLocalizedString.bind(void 0,mr);class ur{appenderName="Extension";#ai;#e;constructor(e,t){this.#ai=t,this.#e=e}appendTrackAtLevel(e,t){return 0===Object.values(this.#ai.entriesByTrack).reduce(((e,t)=>t.length+e),0)?e:(this.#si(e,t),this.#oi(e))}#si(e,t){const i=U({shareHeaderLine:!1,collapsible:!0}),n=W("extension",e,pr(hr.customTrackName,{PH1:this.#ai.name}),i,!0,t);n.description=pr(hr.customTrackDescription),this.#e.registerTrackForGroup(n,this)}#li(e,t){const i=W("extension",e,t,U({shareHeaderLine:!1,padding:2,nestingLevel:1,collapsible:!0}),!0);this.#e.registerTrackForGroup(i,this)}#oi(e){let t=e;for(const[e,i]of Object.entries(this.#ai.entriesByTrack))this.#ai.isTrackGroup&&this.#li(t,e),t=this.#e.appendEventsAtLevel(i,t,this);return t}colorForEvent(e){const n=t.ThemeSupport.instance().getComputedValue("--app-color-rendering");return i.Types.Extensions.isSyntheticExtensionEntry(e)?P.ExtensionUI.extensionEntryColor(e):n}titleForEvent(e){return i.Types.Extensions.isSyntheticExtensionEntry(e)?e.name:t.ThemeSupport.instance().getComputedValue("--app-color-rendering")}highlightedEntryInfo(e){return{title:i.Types.Extensions.isSyntheticExtensionEntry(e)&&e.args.tooltipText?e.args.tooltipText:this.titleForEvent(e),formattedTime:O(e.dur)}}}var gr=Object.freeze({__proto__:null,ExtensionTrackAppender:ur});const vr={gpu:"GPU"},Tr=e.i18n.registerUIStrings("panels/timeline/GPUTrackAppender.ts",vr),fr=e.i18n.getLocalizedString.bind(void 0,Tr);class yr{appenderName="GPU";#e;#t;constructor(e,t){this.#e=e,this.#t=t}appendTrackAtLevel(e,t){const i=this.#t.GPU.mainGPUThreadTasks;return 0===i.length?e:(this.#i(e,t),this.#e.appendEventsAtLevel(i,e,this))}#i(e,t){const i=U({collapsible:!1}),n=W("gpu",e,fr(vr.gpu),i,!0,t);this.#e.registerTrackForGroup(n,this)}colorForEvent(e){if(!i.Types.TraceEvents.isTraceEventGPUTask(e))throw new Error(`Unexpected GPU Task: The event's type is '${e.name}'`);return t.ThemeSupport.instance().getComputedValue("--app-color-painting")}titleForEvent(e){return i.Types.TraceEvents.isTraceEventGPUTask(e)?"GPU":e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:O(e.dur)}}}var wr=Object.freeze({__proto__:null,GPUTrackAppender:yr});const br={layoutShifts:"Layout Shifts"},Sr=e.i18n.registerUIStrings("panels/timeline/LayoutShiftsTrackAppender.ts",br),Cr=e.i18n.getLocalizedString.bind(void 0,Sr),Er=i.Types.Timing.MicroSeconds(5e3);class kr{appenderName="LayoutShifts";#e;#t;constructor(e,t){this.#e=e,this.#t=t}appendTrackAtLevel(e,t){return 0===this.#t.LayoutShifts.clusters.length?e:(this.#i(e,t),this.#ci(e))}#i(e,t){const i=U({collapsible:!1}),n=W("layout-shifts",e,Cr(br.layoutShifts),i,!0,t);this.#e.registerTrackForGroup(n,this)}#ci(e){const t=this.#t.LayoutShifts.clusters.flatMap((e=>e.events));return this.#e.appendEventsAtLevel(t,e,this,((e,t)=>{this.#e.getFlameChartTimelineData().entryTotalTimes[t]=i.Helpers.Timing.microSecondsToMilliseconds(Er)}))}colorForEvent(e){return t.ThemeSupport.instance().getComputedValue("--app-color-rendering")}titleForEvent(e){return i.Types.TraceEvents.isTraceEventLayoutShift(e)?"Layout shift":e.name}highlightedEntryInfo(e){return{title:this.titleForEvent(e),formattedTime:O(e.dur)}}}var xr=Object.freeze({__proto__:null,LAYOUT_SHIFT_SYNTHETIC_DURATION:Er,LayoutShiftsTrackAppender:kr});const Pr={timings:"Timings"},Ir=e.i18n.registerUIStrings("panels/timeline/TimingsTrackAppender.ts",Pr),Mr=e.i18n.getLocalizedString.bind(void 0,Ir);class Fr{appenderName="Timings";#M;#e;#t;constructor(e,t,i){this.#e=e,this.#M=i,this.#t=t}appendTrackAtLevel(e,t){const n=cr.instance().getExtensionData().extensionMarkers,r=this.#t.PageLoadMetrics.allMarkerEvents,a=0===n.length,s=this.#t.UserTimings.performanceMarks.filter((e=>!i.Handlers.ModelHandlers.ExtensionTraceData.extensionDataInTiming(e))),o=this.#t.UserTimings.performanceMeasures.filter((e=>!i.Handlers.ModelHandlers.ExtensionTraceData.extensionDataInTiming(e))),l=this.#t.UserTimings.timestampEvents,c=this.#t.UserTimings.consoleTimings;if(a&&0===r.length&&0===s.length&&0===o.length&&0===l.length&&0===c.length)return e;this.#i(e,t);let d=this.#di(e);return d=this.#e.appendEventsAtLevel(s,d,this),d=this.#e.appendEventsAtLevel(o,d,this),d=this.#e.appendEventsAtLevel(l,d,this),this.#e.appendEventsAtLevel(c,d,this)}#i(e,t){const i=U({useFirstLineForOverview:!0,collapsible:this.#t.UserTimings.performanceMeasures.length>0}),n=W("timings",e,Mr(Pr.timings),i,!0,t);this.#e.registerTrackForGroup(n,this)}#di(e){let t=this.#t.PageLoadMetrics.allMarkerEvents;if(t=t.concat(cr.instance().getExtensionData().extensionMarkers).sort(((e,t)=>e.ts-t.ts)),0===t.length)return e;t.forEach((t=>{const i=this.#e.appendEventAtLevel(t,e,this);this.#e.getFlameChartTimelineData().entryTotalTimes[i]=Number.NaN}));const n=i.Helpers.Timing.microSecondsToMilliseconds(this.#t.Meta.traceBounds.min),r=t.map((e=>{const t=i.Helpers.Timing.microSecondsToMilliseconds(e.ts),r=i.Types.Extensions.isSyntheticExtensionEntry(e)?this.markerStyleForExtensionMarker(e):this.markerStyleForPageLoadEvent(e);return new Yi(t,t-n,r)}));return this.#e.getFlameChartTimelineData().markers.push(...r),++e}markerStyleForPageLoadEvent(e){let t="",n="grey";return i.Types.TraceEvents.isTraceEventMarkDOMContent(e)&&(n="#0867CB",t="DCL"),i.Types.TraceEvents.isTraceEventMarkLoad(e)&&(n="#B31412",t="L"),i.Types.TraceEvents.isTraceEventFirstPaint(e)&&(n="#228847",t="FP"),i.Types.TraceEvents.isTraceEventFirstContentfulPaint(e)&&(n="#1A6937",t="FCP"),i.Types.TraceEvents.isTraceEventLargestContentfulPaintCandidate(e)&&(n="#1A3422",t="LCP"),i.Types.TraceEvents.isTraceEventNavigationStart(e)&&(n="#FF9800",t=""),{title:t,dashStyle:[6,4],lineWidth:.5,color:n,tall:!0,lowPriority:!1}}markerStyleForExtensionMarker(e){return{title:e.name,dashStyle:[6,4],lineWidth:.5,color:P.ExtensionUI.extensionEntryColor(e),tall:!0,lowPriority:!1}}colorForEvent(e){return i.Types.TraceEvents.eventIsPageLoadEvent(e)?this.markerStyleForPageLoadEvent(e).color:i.Types.Extensions.isSyntheticExtensionEntry(e)?P.ExtensionUI.extensionEntryColor(e):this.#M.colorForID(e.name)}titleForEvent(e){i.Handlers.ModelHandlers.PageLoadMetrics;if(i.Types.TraceEvents.eventIsPageLoadEvent(e))switch(e.name){case"MarkDOMContent":return"DCL";case"MarkLoad":return"L";case"firstContentfulPaint":return"FCP";case"firstPaint":return"FP";case"largestContentfulPaint::Candidate":return"LCP";case"navigationStart":return"";default:return e.name}return i.Types.TraceEvents.isTraceEventTimeStamp(e)?`${e.name}: ${e.args.data.message}`:i.Types.TraceEvents.isTraceEventPerformanceMark(e)?`[mark]: ${e.name}`:e.name}highlightedEntryInfo(e){const t=i.Types.Extensions.isSyntheticExtensionEntry(e)&&e.args.tooltipText?e.args.tooltipText:this.titleForEvent(e);if(i.Types.TraceEvents.isTraceEventMarkerEvent(e)||i.Types.TraceEvents.isTraceEventPerformanceMark(e)||i.Types.TraceEvents.isTraceEventTimeStamp(e)){return{title:t,formattedTime:O(i.Helpers.Timing.timeStampForEventAdjustedByClosestNavigation(e,this.#t.Meta.traceBounds,this.#t.Meta.navigationsByNavigationId,this.#t.Meta.navigationsByFrameId))}}return{title:t,formattedTime:O(e.dur)}}}var Lr=Object.freeze({__proto__:null,TimingsTrackAppender:Fr});const Rr=["Animations","Timings","Interactions","GPU","LayoutShifts","Thread","Thread_AuctionWorklet","Extension"];class Dr{#hi=new Map;#mi=new Map;#pi=new Map;#ui=new Map;#Se;#t;#gi;#M;#vi=[];#Ti=new Set([...Rr]);#fi;#yi;#wi;#bi;#Si;#Ci;#Ei=[];constructor(e,i,n,a){this.#Se=e,this.#t=i,this.#gi=n,this.#M=new r.Color.Generator({min:30,max:55,count:void 0},{min:70,max:100,count:6},50,.7),this.#fi=a,this.#yi=new Fr(this,this.#t,this.#M),this.#vi.push(this.#yi),this.#bi=new mt(this,this.#t,this.#M),this.#vi.push(this.#bi),this.#wi=new $(this,this.#t),this.#vi.push(this.#wi),this.#Si=new yr(this,this.#t),this.#vi.push(this.#Si),this.#Ci=new kr(this,this.#t),this.#vi.push(this.#Ci),this.#ki(),this.#xi(),t.ThemeSupport.instance().addEventListener(t.ThemeChangeEvent.eventName,(()=>{for(const e of this.#Se.groups)e.style.color=t.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),e.style.backgroundColor=t.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container")}))}setFlameChartDataAndEntryData(e,t,i){this.#mi.clear(),this.#Se=e,this.#gi=t,this.#fi=i}getFlameChartTimelineData(){return this.#Se}#xi(){const e=cr.instance().getExtensionData().extensionTrackData;for(const t of e)this.#vi.push(new ur(this,t))}#ki(){const e=e=>{switch(e.threadType){case"MAIN_THREAD":if(e.isOnMainFrame){const t=e.getUrl();return t.startsWith("about:")||t.startsWith("chrome:")?2:0}return 1;case"WORKER":return 3;case"RASTERIZER":return 4;case"THREAD_POOL":return 5;case"AUCTION_WORKLET":return 6;case"OTHER":return 7;default:return 8}},t=i.Handlers.Threads.threadsInTrace(this.#t),n=new Set,r=a.Runtime.experiments.isEnabled("timeline-show-all-events");for(const{pid:e,tid:i,name:a,type:s}of t){if(this.#t.Meta.traceIsGeneric){this.#Ei.push(new Ei(this,this.#t,e,i,a,"OTHER"));continue}if(("Chrome_ChildIOThread"===a||"Compositor"===a||"GpuMemoryThread"===a)&&!r)continue;const t=this.#t.AuctionWorklets.worklets.get(e);n.has(e)||(t?(n.add(e),this.#Ei.push(new Ei(this,this.#t,e,t.args.data.utilityThread.tid,"auction-worket-utility","AUCTION_WORKLET")),this.#Ei.push(new Ei(this,this.#t,e,t.args.data.v8HelperThread.tid,"auction-worklet-v8helper","AUCTION_WORKLET"))):this.#Ei.push(new Ei(this,this.#t,e,i,a,s)))}this.#Ei.sort(((t,i)=>e(t)-e(i)||i.getEntries().length-t.getEntries().length)),this.#vi.push(...this.#Ei)}timingsTrackAppender(){return this.#yi}animationsTrackAppender(){return this.#wi}interactionsTrackAppender(){return this.#bi}gpuTrackAppender(){return this.#Si}layoutShiftsTrackAppender(){return this.#Ci}threadAppenders(){return this.#Ei}eventsInTrack(e){const t=this.#pi.get(e);if(t)return t;let i=null,n=null;for(const[t,r]of this.#hi)r===e&&(null===i&&(i=t),n=t);if(null===i||null===n)throw new Error(`Could not find events for track: ${e}`);const r=this.#Se.entryLevels,a=[];for(let e=0;e<r.length;e++)i<=r[e]&&r[e]<=n&&a.push(this.#gi[e]);return a.sort(((e,t)=>e.ts-t.ts)),this.#pi.set(e,a),a}eventsForTreeView(e){const t=this.#ui.get(e);if(t)return t;let n=this.eventsInTrack(e);return i.Helpers.TreeHelpers.canBuildTreesFromEvents(n)||(n=n.filter((e=>!i.Types.TraceEvents.isAsyncPhase(e.ph)))),this.#ui.set(e,n),n}registerTrackForGroup(e,t){this.#Se.groups.push(e),this.#mi.set(e,t)}getCurrentTrackCountForThreadType(e){return this.#Ei.filter((t=>t.threadType===e&&t.headerAppended())).length}groupForAppender(e){let t=null;for(const[i,n]of this.#mi)if(n===e){t=i;break}return t}groupEventsForTreeView(e){const t=this.#mi.get(e);return t?this.eventsForTreeView(t):null}registerTrackForLevel(e,t){this.#hi.set(e,t)}appendEventAtLevel(e,t,n){this.#hi.set(t,n);const r=this.#gi.length;this.#gi.push(e),this.#fi[t]="TrackAppender",this.#Se.entryLevels[r]=t,this.#Se.entryStartTimes[r]=i.Helpers.Timing.microSecondsToMilliseconds(e.ts);const a=e.dur||i.Helpers.Timing.millisecondsToMicroseconds(Li);return this.#Se.entryTotalTimes[r]=i.Helpers.Timing.microSecondsToMilliseconds(a),r}appendEventsAtLevel(e,t,i,n){const r=[];for(let a=0;a<e.length;++a){const s=e[a];if(!this.entryIsVisibleInTimeline(s))continue;const o=V(s,r),l=this.appendEventAtLevel(s,t+o,i);n?.(s,l)}return this.#fi.length=t+r.length,this.#fi.fill("TrackAppender",t),t+r.length}entryIsVisibleInTimeline(e){if(this.#t.Meta.traceIsGeneric)return!0;if(i.Types.TraceEvents.isTraceEventUpdateCounters(e))return!0;if(i.Types.TraceEvents.isTraceEventSchedulePostMessage(e)||i.Types.TraceEvents.isTraceEventHandlePostMessage(e))return a.Runtime.experiments.isEnabled("timeline-show-postmessage-events");if(i.Types.Extensions.isSyntheticExtensionEntry(e))return!0;const t=de(e.name),n=i.Types.TraceEvents.isTraceEventConsoleTime(e)||i.Types.TraceEvents.isTraceEventPerformanceMeasure(e)||i.Types.TraceEvents.isTraceEventPerformanceMark(e);return t&&!t.hidden||n}allVisibleTrackAppenders(){return this.#vi.filter((e=>this.#Ti.has(e.appenderName)))}allThreadAppendersByProcess(){const e=this.allVisibleTrackAppenders(),t=new Map;for(const i of e){if(!(i instanceof Ei))continue;const e=t.get(i.processId())??[];e.push(i),t.set(i.processId(),e)}return t}setVisibleTracks(e){this.#Ti=e||new Set([...Rr])}colorForEvent(e,t){const i=this.#hi.get(t);if(!i)throw new Error("Track not found for level");return i.colorForEvent(e)}titleForEvent(e,t){const i=this.#hi.get(t);if(!i)throw new Error("Track not found for level");return i.titleForEvent(e)}highlightedEntryInfo(e,t){const i=this.#hi.get(t);if(!i)throw new Error("Track not found for level");const n=s.DetailsView.buildWarningElementsForEvent(e,this.#t),{title:r,formattedTime:a,warningElements:o}=i.highlightedEntryInfo(e);return{title:r,formattedTime:a,warningElements:n.concat(o||[])}}}var Ar=Object.freeze({__proto__:null,TrackNames:Rr,CompatibilityTracksAppender:Dr});export{J as AnimationsTrackAppender,z as AppenderUtils,Y as BenchmarkEvents,ee as CLSLinkifier,Ar as CompatibilityTracksAppender,nt as CountersGraph,ye as EventUICategory,Le as EventsSerializer,Qt as EventsTimelineTreeView,dr as ExtensionDataGatherer,gr as ExtensionTrackAppender,Ee as FreshRecording,wr as GPUTrackAppender,wi as Initiators,ut as InteractionsTrackAppender,xr as LayoutShiftsTrackAppender,Be as ModificationsManager,Oi as NetworkTrackAppender,Oe as SaveFileFormatter,Ge as SourceMapsResolver,at as TargetForEvent,ki as ThreadAppender,Ke as TimelineController,gi as TimelineDetailsView,mn as TimelineEventOverview,Nt as TimelineFilters,Di as TimelineFlameChartDataProvider,_i as TimelineFlameChartNetworkDataProvider,Zi as TimelineFlameChartView,Cn as TimelineHistoryManager,ti as TimelineLayersView,Rn as TimelineLoader,Un as TimelineMiniMap,ai as TimelinePaintProfilerView,or as TimelinePanel,Tt as TimelineSelection,$t as TimelineTreeView,Lt as TimelineUIUtils,Lr as TimingsTrackAppender,Yn as UIDevtoolsController,Jn as UIDevtoolsUtils};
