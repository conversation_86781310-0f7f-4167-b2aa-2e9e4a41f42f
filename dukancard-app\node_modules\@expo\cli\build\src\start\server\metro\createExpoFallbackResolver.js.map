{"version": 3, "sources": ["../../../../../src/start/server/metro/createExpoFallbackResolver.ts"], "sourcesContent": ["// This file creates the fallback resolver\n// The fallback resolver applies only to module imports and should be the last resolver\n// in the chain. It applies to failed Node module resolution of modules and will attempt\n// to resolve them to `expo` and `expo-router` dependencies that couldn't be resolved.\n// This resolves isolated dependency issues, where we expect dependencies of `expo`\n// and `expo-router` to be resolvable, due to hoisting, but they aren't hoisted in\n// a user's project.\n// See: https://github.com/expo/expo/pull/34286\n\nimport type { ResolutionContext } from 'metro-resolver';\nimport path from 'path';\n\nimport type { StrictResolver, StrictResolverFactory } from './withMetroMultiPlatform';\nimport type { ExpoCustomMetroResolver } from './withMetroResolvers';\n\n/** A record of dependencies that we know are only used for scripts and config-plugins\n * @privateRemarks\n * This includes dependencies we never resolve indirectly. Generally, we only want\n * to add fallback resolutions for dependencies of `expo` and `expo-router` that\n * are either transpiled into output code or resolved from other Expo packages\n * without them having direct dependencies on these dependencies.\n * Meaning: If you update this list, exclude what a user might use when they\n * forget to specify their own dependencies, rather than what we use ourselves\n * only in `expo` and `expo-router`.\n */\nconst EXCLUDE_ORIGIN_MODULES: Record<string, true | undefined> = {\n  '@expo/config': true,\n  '@expo/config-plugins': true,\n  'schema-utils': true, // Used by `expo-router/plugin`\n  semver: true, // Used by `expo-router/doctor`\n};\n\ninterface PackageMetaPeerDependenciesMetaEntry {\n  [propName: string]: unknown;\n  optional?: boolean;\n}\n\ninterface PackageMeta {\n  readonly [propName: string]: unknown;\n  readonly name?: string;\n  readonly main?: string;\n  readonly exports?: any; // unused\n  readonly dependencies?: Record<string, unknown>;\n  readonly peerDependencies?: Record<string, unknown>;\n  readonly peerDependenciesMeta?: Record<\n    string,\n    PackageMetaPeerDependenciesMetaEntry | undefined | null\n  >;\n}\n\ninterface ModuleDescription {\n  originModulePath: string;\n  moduleTestRe: RegExp;\n}\n\nconst debug = require('debug')('expo:start:server:metro:fallback-resolver') as typeof console.log;\n\n/** Converts a list of module names to a regex that may either match bare module names or sub-modules of modules */\nconst dependenciesToRegex = (dependencies: string[]) =>\n  new RegExp(`^(?:${dependencies.join('|')})(?:$|/)`);\n\n/** Resolves an origin module and outputs a filter regex and target path for it */\nconst getModuleDescriptionWithResolver = (\n  context: ResolutionContext,\n  resolve: StrictResolver,\n  originModuleName: string\n): ModuleDescription | null => {\n  let filePath: string | undefined;\n  let packageMeta: PackageMeta | undefined | null;\n  try {\n    const resolution = resolve(path.join(originModuleName, 'package.json'));\n    if (resolution.type !== 'sourceFile') {\n      debug(`Fallback module resolution failed for origin module: ${originModuleName})`);\n      return null;\n    }\n    filePath = resolution.filePath;\n    packageMeta = context.getPackage(filePath);\n    if (!packageMeta) {\n      return null;\n    }\n  } catch (error: any) {\n    debug(\n      `Fallback module resolution threw: ${error.constructor.name}. (module: ${filePath || originModuleName})`\n    );\n    return null;\n  }\n  let dependencies: string[] = [];\n  if (packageMeta.dependencies) dependencies.push(...Object.keys(packageMeta.dependencies));\n  if (packageMeta.peerDependencies) {\n    const peerDependenciesMeta = packageMeta.peerDependenciesMeta;\n    let peerDependencies = Object.keys(packageMeta.peerDependencies);\n    // We explicitly include non-optional peer dependencies. Non-optional peer dependencies of\n    // `expo` and `expo-router` are either expected to be accessible on a project-level, since\n    // both are meant to be installed is direct dependencies, or shouldn't be accessible when\n    // they're fulfilled as isolated dependencies.\n    // The exception are only *optional* peer dependencies, since when they're installed\n    // automatically by newer package manager behaviour, they may become isolated dependencies\n    // that we still wish to access.\n    if (peerDependenciesMeta) {\n      peerDependencies = peerDependencies.filter((dependency) => {\n        const peerMeta = peerDependenciesMeta[dependency];\n        return peerMeta && typeof peerMeta === 'object' && peerMeta.optional === true;\n      });\n    }\n    dependencies.push(...peerDependencies);\n  }\n  // We deduplicate the dependencies and exclude modules that we know are only used for scripts or config-plugins\n  dependencies = dependencies.filter((moduleName, index, dependenciesArr) => {\n    if (EXCLUDE_ORIGIN_MODULES[moduleName]) return false;\n    return dependenciesArr.indexOf(moduleName) === index;\n  });\n  // Return test regex for dependencies and full origin module path to resolve through\n  const originModulePath = path.dirname(filePath);\n  return dependencies.length\n    ? { originModulePath, moduleTestRe: dependenciesToRegex(dependencies) }\n    : null;\n};\n\n/** Creates a fallback module resolver that resolves dependencis of modules named in `originModuleNames` via their path.\n * @remarks\n * The fallback resolver targets modules dependended on by modules named in `originModuleNames` and resolves\n * them from the module root of these origin modules instead.\n * It should only be used as a fallback after normal Node resolution (and other resolvers) have failed for:\n * - the `expo` package\n * - the `expo-router` package\n * Dependencies mentioned as either optional peer dependencies or direct dependencies by these modules may be isolated\n * and inaccessible via standard Node module resolution. This may happen when either transpilation adds these\n * dependencies to other parts of the tree (e.g. `@babel/runtime`) or when a dependency fails to hoist due to either\n * a corrupted dependency tree or when a peer dependency is fulfilled incorrectly (e.g. `expo-asset`)\n * @privateRemarks\n * This does NOT follow Node resolution and is *only* intended to provide a fallback for modules that we depend on\n * ourselves and know we can resolve (via expo or expo-router)!\n */\nexport function createFallbackModuleResolver({\n  projectRoot,\n  originModuleNames,\n  getStrictResolver,\n}: {\n  projectRoot: string;\n  originModuleNames: string[];\n  getStrictResolver: StrictResolverFactory;\n}): ExpoCustomMetroResolver {\n  const _moduleDescriptionsCache: Record<string, ModuleDescription | null> = {};\n\n  const getModuleDescription = (\n    immutableContext: ResolutionContext,\n    originModuleName: string,\n    platform: string | null\n  ) => {\n    if (_moduleDescriptionsCache[originModuleName] !== undefined) {\n      return _moduleDescriptionsCache[originModuleName];\n    }\n    // Resolve the origin module itself via the project root rather than the file that requested the missing module\n    // The addition of `package.json` doesn't matter here. We just need a file path that'll be turned into a directory path\n    // We don't need to modify `nodeModulesPaths` since it's guaranteed to contain the project's node modules paths\n    const context: ResolutionContext = {\n      ...immutableContext,\n      originModulePath: path.join(projectRoot, 'package.json'),\n    };\n    return (_moduleDescriptionsCache[originModuleName] = getModuleDescriptionWithResolver(\n      context,\n      getStrictResolver(context, platform),\n      originModuleName\n    ));\n  };\n\n  const fileSpecifierRe = /^[\\\\/]|^\\.\\.?(?:$|[\\\\/])/i;\n\n  return function requestFallbackModule(immutableContext, moduleName, platform) {\n    // Early return if `moduleName` cannot be a module specifier\n    // This doesn't have to be accurate as this resolver is a fallback for failed resolutions and\n    // we're only doing this to avoid unnecessary resolution work\n    if (fileSpecifierRe.test(moduleName)) {\n      return null;\n    }\n\n    for (const originModuleName of originModuleNames) {\n      const moduleDescription = getModuleDescription(immutableContext, originModuleName, platform);\n      if (moduleDescription && moduleDescription.moduleTestRe.test(moduleName)) {\n        // We instead resolve as if it was depended on by the `originModulePath` (the module named in `originModuleNames`)\n        const context: ResolutionContext = {\n          ...immutableContext,\n          nodeModulesPaths: [moduleDescription.originModulePath],\n          originModulePath: moduleDescription.originModulePath,\n        };\n        const res = getStrictResolver(context, platform)(moduleName);\n        debug(\n          `Fallback resolution for ${platform}: ${moduleName} -> from origin: ${originModuleName}`\n        );\n        return res;\n      }\n    }\n\n    return null;\n  };\n}\n"], "names": ["createFallbackModuleResolver", "EXCLUDE_ORIGIN_MODULES", "semver", "debug", "require", "dependenciesToRegex", "dependencies", "RegExp", "join", "getModuleDescriptionWithResolver", "context", "resolve", "originModuleName", "filePath", "packageMeta", "resolution", "path", "type", "getPackage", "error", "constructor", "name", "push", "Object", "keys", "peerDependencies", "peerDependenciesMeta", "filter", "dependency", "peerMeta", "optional", "moduleName", "index", "dependenciesArr", "indexOf", "originModulePath", "dirname", "length", "moduleTestRe", "projectRoot", "originModuleNames", "getStrictResolver", "_moduleDescriptionsCache", "getModuleDescription", "immutableContext", "platform", "undefined", "fileSpecifierRe", "requestFallbackModule", "test", "moduleDescription", "nodeModulesPaths", "res"], "mappings": "AAAA,0CAA0C;AAC1C,uFAAuF;AACvF,wFAAwF;AACxF,sFAAsF;AACtF,mFAAmF;AACnF,kFAAkF;AAClF,oBAAoB;AACpB,+CAA+C;;;;;+BA8H/BA;;;eAAAA;;;;gEA3HC;;;;;;;;;;;AAKjB;;;;;;;;;CASC,GACD,MAAMC,yBAA2D;IAC/D,gBAAgB;IAChB,wBAAwB;IACxB,gBAAgB;IAChBC,QAAQ;AACV;AAyBA,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,iHAAiH,GACjH,MAAMC,sBAAsB,CAACC,eAC3B,IAAIC,OAAO,CAAC,IAAI,EAAED,aAAaE,IAAI,CAAC,KAAK,QAAQ,CAAC;AAEpD,gFAAgF,GAChF,MAAMC,mCAAmC,CACvCC,SACAC,SACAC;IAEA,IAAIC;IACJ,IAAIC;IACJ,IAAI;QACF,MAAMC,aAAaJ,QAAQK,eAAI,CAACR,IAAI,CAACI,kBAAkB;QACvD,IAAIG,WAAWE,IAAI,KAAK,cAAc;YACpCd,MAAM,CAAC,qDAAqD,EAAES,iBAAiB,CAAC,CAAC;YACjF,OAAO;QACT;QACAC,WAAWE,WAAWF,QAAQ;QAC9BC,cAAcJ,QAAQQ,UAAU,CAACL;QACjC,IAAI,CAACC,aAAa;YAChB,OAAO;QACT;IACF,EAAE,OAAOK,OAAY;QACnBhB,MACE,CAAC,kCAAkC,EAAEgB,MAAMC,WAAW,CAACC,IAAI,CAAC,WAAW,EAAER,YAAYD,iBAAiB,CAAC,CAAC;QAE1G,OAAO;IACT;IACA,IAAIN,eAAyB,EAAE;IAC/B,IAAIQ,YAAYR,YAAY,EAAEA,aAAagB,IAAI,IAAIC,OAAOC,IAAI,CAACV,YAAYR,YAAY;IACvF,IAAIQ,YAAYW,gBAAgB,EAAE;QAChC,MAAMC,uBAAuBZ,YAAYY,oBAAoB;QAC7D,IAAID,mBAAmBF,OAAOC,IAAI,CAACV,YAAYW,gBAAgB;QAC/D,0FAA0F;QAC1F,0FAA0F;QAC1F,yFAAyF;QACzF,8CAA8C;QAC9C,oFAAoF;QACpF,0FAA0F;QAC1F,gCAAgC;QAChC,IAAIC,sBAAsB;YACxBD,mBAAmBA,iBAAiBE,MAAM,CAAC,CAACC;gBAC1C,MAAMC,WAAWH,oBAAoB,CAACE,WAAW;gBACjD,OAAOC,YAAY,OAAOA,aAAa,YAAYA,SAASC,QAAQ,KAAK;YAC3E;QACF;QACAxB,aAAagB,IAAI,IAAIG;IACvB;IACA,+GAA+G;IAC/GnB,eAAeA,aAAaqB,MAAM,CAAC,CAACI,YAAYC,OAAOC;QACrD,IAAIhC,sBAAsB,CAAC8B,WAAW,EAAE,OAAO;QAC/C,OAAOE,gBAAgBC,OAAO,CAACH,gBAAgBC;IACjD;IACA,oFAAoF;IACpF,MAAMG,mBAAmBnB,eAAI,CAACoB,OAAO,CAACvB;IACtC,OAAOP,aAAa+B,MAAM,GACtB;QAAEF;QAAkBG,cAAcjC,oBAAoBC;IAAc,IACpE;AACN;AAiBO,SAASN,6BAA6B,EAC3CuC,WAAW,EACXC,iBAAiB,EACjBC,iBAAiB,EAKlB;IACC,MAAMC,2BAAqE,CAAC;IAE5E,MAAMC,uBAAuB,CAC3BC,kBACAhC,kBACAiC;QAEA,IAAIH,wBAAwB,CAAC9B,iBAAiB,KAAKkC,WAAW;YAC5D,OAAOJ,wBAAwB,CAAC9B,iBAAiB;QACnD;QACA,+GAA+G;QAC/G,uHAAuH;QACvH,+GAA+G;QAC/G,MAAMF,UAA6B;YACjC,GAAGkC,gBAAgB;YACnBT,kBAAkBnB,eAAI,CAACR,IAAI,CAAC+B,aAAa;QAC3C;QACA,OAAQG,wBAAwB,CAAC9B,iBAAiB,GAAGH,iCACnDC,SACA+B,kBAAkB/B,SAASmC,WAC3BjC;IAEJ;IAEA,MAAMmC,kBAAkB;IAExB,OAAO,SAASC,sBAAsBJ,gBAAgB,EAAEb,UAAU,EAAEc,QAAQ;QAC1E,4DAA4D;QAC5D,6FAA6F;QAC7F,6DAA6D;QAC7D,IAAIE,gBAAgBE,IAAI,CAAClB,aAAa;YACpC,OAAO;QACT;QAEA,KAAK,MAAMnB,oBAAoB4B,kBAAmB;YAChD,MAAMU,oBAAoBP,qBAAqBC,kBAAkBhC,kBAAkBiC;YACnF,IAAIK,qBAAqBA,kBAAkBZ,YAAY,CAACW,IAAI,CAAClB,aAAa;gBACxE,kHAAkH;gBAClH,MAAMrB,UAA6B;oBACjC,GAAGkC,gBAAgB;oBACnBO,kBAAkB;wBAACD,kBAAkBf,gBAAgB;qBAAC;oBACtDA,kBAAkBe,kBAAkBf,gBAAgB;gBACtD;gBACA,MAAMiB,MAAMX,kBAAkB/B,SAASmC,UAAUd;gBACjD5B,MACE,CAAC,wBAAwB,EAAE0C,SAAS,EAAE,EAAEd,WAAW,iBAAiB,EAAEnB,kBAAkB;gBAE1F,OAAOwC;YACT;QACF;QAEA,OAAO;IACT;AACF"}