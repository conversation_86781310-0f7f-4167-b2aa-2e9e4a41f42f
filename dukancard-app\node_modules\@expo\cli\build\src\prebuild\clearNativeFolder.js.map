{"version": 3, "sources": ["../../../src/prebuild/clearNativeFolder.ts"], "sourcesContent": ["import { AndroidConfig, IOSConfig, ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { directoryExistsAsync } from '../utils/dir';\nimport { isInteractive } from '../utils/interactive';\nimport { logNewSection } from '../utils/ora';\nimport { confirmAsync } from '../utils/prompts';\n\ntype ArbitraryPlatform = ModPlatform | string;\n\n/** Delete the input native folders and print a loading step. */\nexport async function clearNativeFolder(projectRoot: string, folders: string[]) {\n  const step = logNewSection(`Clearing ${folders.join(', ')}`);\n  try {\n    await Promise.all(\n      folders.map((folderName) =>\n        fs.promises.rm(path.join(projectRoot, folderName), {\n          recursive: true,\n          force: true,\n        })\n      )\n    );\n    step.succeed(`Cleared ${folders.join(', ')} code`);\n  } catch (error: any) {\n    step.fail(`Failed to delete ${folders.join(', ')} code: ${error.message}`);\n    throw error;\n  }\n}\n\n/**\n * Returns `true` if a certain subset of required Android project files are intact.\n *\n * This isn't perfect but it serves the purpose of indicating that the user should\n * be warned to nuke the project files, most commonly when git is cleared and the root folder\n * remains in memory.\n */\nexport async function hasRequiredAndroidFilesAsync(projectRoot: string): Promise<boolean> {\n  try {\n    await Promise.all([\n      AndroidConfig.Paths.getAppBuildGradleAsync(projectRoot),\n      AndroidConfig.Paths.getProjectBuildGradleAsync(projectRoot),\n      AndroidConfig.Paths.getAndroidManifestAsync(projectRoot),\n      AndroidConfig.Paths.getMainApplicationAsync(projectRoot),\n    ]);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/** Returns `true` if a certain subset of required iOS project files are intact. */\nexport async function hasRequiredIOSFilesAsync(projectRoot: string) {\n  try {\n    // If any of the following required files are missing, then the project is malformed.\n    await Promise.all([\n      IOSConfig.Paths.getAllXcodeProjectPaths(projectRoot),\n      IOSConfig.Paths.getAllPBXProjectPaths(projectRoot),\n    ]);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Filter out platforms that do not have an existing platform folder.\n * If the user wants to validate that neither of ['ios', 'android'] are malformed then we should\n * first check that both `ios` and `android` folders exist.\n *\n * This optimization prevents us from prompting to clear a \"malformed\" project that doesn't exist yet.\n */\nasync function filterPlatformsThatDoNotExistAsync(\n  projectRoot: string,\n  platforms: ArbitraryPlatform[]\n): Promise<ArbitraryPlatform[]> {\n  const valid = await Promise.all(\n    platforms.map(async (platform) => {\n      if (await directoryExistsAsync(path.join(projectRoot, platform))) {\n        return platform;\n      }\n      return null;\n    })\n  );\n  return valid.filter(Boolean) as ArbitraryPlatform[];\n}\n\n/** Get a list of native platforms that have existing directories which contain malformed projects. */\nexport async function getMalformedNativeProjectsAsync(\n  projectRoot: string,\n  platforms: ArbitraryPlatform[]\n): Promise<ArbitraryPlatform[]> {\n  const VERIFIERS: Record<ArbitraryPlatform, (root: string) => Promise<boolean>> = {\n    android: hasRequiredAndroidFilesAsync,\n    ios: hasRequiredIOSFilesAsync,\n  };\n\n  const checkablePlatforms = platforms.filter((platform) => platform in VERIFIERS);\n  const checkPlatforms = await filterPlatformsThatDoNotExistAsync(projectRoot, checkablePlatforms);\n  return (\n    await Promise.all(\n      checkPlatforms.map(async (platform) => {\n        if (!VERIFIERS[platform]) {\n          return false;\n        }\n        if (await VERIFIERS[platform](projectRoot)) {\n          return false;\n        }\n        return platform;\n      })\n    )\n  ).filter(Boolean) as ArbitraryPlatform[];\n}\n\nexport async function promptToClearMalformedNativeProjectsAsync(\n  projectRoot: string,\n  checkPlatforms: ArbitraryPlatform[]\n) {\n  const platforms = await getMalformedNativeProjectsAsync(projectRoot, checkPlatforms);\n\n  if (!platforms.length) {\n    return;\n  }\n\n  const displayPlatforms = platforms.map((platform) => chalk.cyan(platform));\n  // Prompt which platforms to reset.\n  const message =\n    platforms.length > 1\n      ? `The ${displayPlatforms[0]} and ${displayPlatforms[1]} projects are malformed`\n      : `The ${displayPlatforms[0]} project is malformed`;\n\n  if (\n    // If the process is non-interactive, default to clearing the malformed native project.\n    // This would only happen on re-running prebuild.\n    !isInteractive() ||\n    // Prompt to clear the native folders.\n    (await confirmAsync({\n      message: `${message}, would you like to clear the project files and reinitialize them?`,\n      initial: true,\n    }))\n  ) {\n    if (!isInteractive()) {\n      Log.warn(`${message}, project files will be cleared and reinitialized.`);\n    }\n    await clearNativeFolder(projectRoot, platforms);\n  } else {\n    // Warn the user that the process may fail.\n    Log.warn('Continuing with malformed native projects');\n  }\n}\n"], "names": ["clearNativeFolder", "getMalformedNativeProjectsAsync", "hasRequiredAndroidFilesAsync", "hasRequiredIOSFilesAsync", "promptToClearMalformedNativeProjectsAsync", "projectRoot", "folders", "step", "logNewSection", "join", "Promise", "all", "map", "folderName", "fs", "promises", "rm", "path", "recursive", "force", "succeed", "error", "fail", "message", "AndroidConfig", "Paths", "getAppBuildGradleAsync", "getProjectBuildGradleAsync", "getAndroidManifestAsync", "getMainApplicationAsync", "IOSConfig", "getAllXcodeProjectPaths", "getAllPBXProjectPaths", "filterPlatformsThatDoNotExistAsync", "platforms", "valid", "platform", "directoryExistsAsync", "filter", "Boolean", "VERIFIERS", "android", "ios", "checkablePlatforms", "checkPlatforms", "length", "displayPlatforms", "chalk", "cyan", "isInteractive", "<PERSON><PERSON><PERSON>", "initial", "Log", "warn"], "mappings": ";;;;;;;;;;;IAcsBA,iBAAiB;eAAjBA;;IA4EAC,+BAA+B;eAA/BA;;IAnDAC,4BAA4B;eAA5BA;;IAeAC,wBAAwB;eAAxBA;;IA8DAC,yCAAyC;eAAzCA;;;;yBApHgC;;;;;;;gEACpC;;;;;;;gEACH;;;;;;;gEACE;;;;;;6DAEI;qBACgB;6BACP;qBACA;yBACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKtB,eAAeJ,kBAAkBK,WAAmB,EAAEC,OAAiB;IAC5E,MAAMC,OAAOC,IAAAA,kBAAa,EAAC,CAAC,SAAS,EAAEF,QAAQG,IAAI,CAAC,OAAO;IAC3D,IAAI;QACF,MAAMC,QAAQC,GAAG,CACfL,QAAQM,GAAG,CAAC,CAACC,aACXC,aAAE,CAACC,QAAQ,CAACC,EAAE,CAACC,eAAI,CAACR,IAAI,CAACJ,aAAaQ,aAAa;gBACjDK,WAAW;gBACXC,OAAO;YACT;QAGJZ,KAAKa,OAAO,CAAC,CAAC,QAAQ,EAAEd,QAAQG,IAAI,CAAC,MAAM,KAAK,CAAC;IACnD,EAAE,OAAOY,OAAY;QACnBd,KAAKe,IAAI,CAAC,CAAC,iBAAiB,EAAEhB,QAAQG,IAAI,CAAC,MAAM,OAAO,EAAEY,MAAME,OAAO,EAAE;QACzE,MAAMF;IACR;AACF;AASO,eAAenB,6BAA6BG,WAAmB;IACpE,IAAI;QACF,MAAMK,QAAQC,GAAG,CAAC;YAChBa,8BAAa,CAACC,KAAK,CAACC,sBAAsB,CAACrB;YAC3CmB,8BAAa,CAACC,KAAK,CAACE,0BAA0B,CAACtB;YAC/CmB,8BAAa,CAACC,KAAK,CAACG,uBAAuB,CAACvB;YAC5CmB,8BAAa,CAACC,KAAK,CAACI,uBAAuB,CAACxB;SAC7C;QACD,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAeF,yBAAyBE,WAAmB;IAChE,IAAI;QACF,qFAAqF;QACrF,MAAMK,QAAQC,GAAG,CAAC;YAChBmB,0BAAS,CAACL,KAAK,CAACM,uBAAuB,CAAC1B;YACxCyB,0BAAS,CAACL,KAAK,CAACO,qBAAqB,CAAC3B;SACvC;QACD,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA;;;;;;CAMC,GACD,eAAe4B,mCACb5B,WAAmB,EACnB6B,SAA8B;IAE9B,MAAMC,QAAQ,MAAMzB,QAAQC,GAAG,CAC7BuB,UAAUtB,GAAG,CAAC,OAAOwB;QACnB,IAAI,MAAMC,IAAAA,yBAAoB,EAACpB,eAAI,CAACR,IAAI,CAACJ,aAAa+B,YAAY;YAChE,OAAOA;QACT;QACA,OAAO;IACT;IAEF,OAAOD,MAAMG,MAAM,CAACC;AACtB;AAGO,eAAetC,gCACpBI,WAAmB,EACnB6B,SAA8B;IAE9B,MAAMM,YAA2E;QAC/EC,SAASvC;QACTwC,KAAKvC;IACP;IAEA,MAAMwC,qBAAqBT,UAAUI,MAAM,CAAC,CAACF,WAAaA,YAAYI;IACtE,MAAMI,iBAAiB,MAAMX,mCAAmC5B,aAAasC;IAC7E,OAAO,AACL,CAAA,MAAMjC,QAAQC,GAAG,CACfiC,eAAehC,GAAG,CAAC,OAAOwB;QACxB,IAAI,CAACI,SAAS,CAACJ,SAAS,EAAE;YACxB,OAAO;QACT;QACA,IAAI,MAAMI,SAAS,CAACJ,SAAS,CAAC/B,cAAc;YAC1C,OAAO;QACT;QACA,OAAO+B;IACT,GACF,EACAE,MAAM,CAACC;AACX;AAEO,eAAenC,0CACpBC,WAAmB,EACnBuC,cAAmC;IAEnC,MAAMV,YAAY,MAAMjC,gCAAgCI,aAAauC;IAErE,IAAI,CAACV,UAAUW,MAAM,EAAE;QACrB;IACF;IAEA,MAAMC,mBAAmBZ,UAAUtB,GAAG,CAAC,CAACwB,WAAaW,gBAAK,CAACC,IAAI,CAACZ;IAChE,mCAAmC;IACnC,MAAMb,UACJW,UAAUW,MAAM,GAAG,IACf,CAAC,IAAI,EAAEC,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAEA,gBAAgB,CAAC,EAAE,CAAC,uBAAuB,CAAC,GAC9E,CAAC,IAAI,EAAEA,gBAAgB,CAAC,EAAE,CAAC,qBAAqB,CAAC;IAEvD,IACE,uFAAuF;IACvF,iDAAiD;IACjD,CAACG,IAAAA,0BAAa,OACd,sCAAsC;IACrC,MAAMC,IAAAA,qBAAY,EAAC;QAClB3B,SAAS,GAAGA,QAAQ,kEAAkE,CAAC;QACvF4B,SAAS;IACX,IACA;QACA,IAAI,CAACF,IAAAA,0BAAa,KAAI;YACpBG,KAAIC,IAAI,CAAC,GAAG9B,QAAQ,kDAAkD,CAAC;QACzE;QACA,MAAMvB,kBAAkBK,aAAa6B;IACvC,OAAO;QACL,2CAA2C;QAC3CkB,KAAIC,IAAI,CAAC;IACX;AACF"}