{"version": 3, "sources": ["../../../../src/install/utils/autoAddConfigPlugins.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport {\n  normalizeStaticPlugin,\n  resolveConfigPluginFunctionWithInfo,\n} from '@expo/config-plugins/build/utils/plugin-resolver';\nimport { getAutoPlugins } from '@expo/prebuild-config';\n\nimport { attemptAddingPluginsAsync } from '../../utils/modifyConfigPlugins';\n\nconst debug = require('debug')('expo:install:config-plugins') as typeof console.log;\n\nconst AUTO_PLUGINS = getAutoPlugins();\n\n/**\n * Resolve if a package has a config plugin.\n * For sanity, we'll only support config plugins that use the `app.config.js` entry file,\n * this is because a package like `lodash` could be a \"valid\" config plugin and break the prebuild process.\n *\n * @param projectRoot\n * @param packageName\n * @returns\n */\nfunction packageHasConfigPlugin(projectRoot: string, packageName: string) {\n  try {\n    const info = resolveConfigPluginFunctionWithInfo(projectRoot, packageName);\n    if (info.isPluginFile) {\n      return info.plugin;\n    }\n  } catch {}\n  return false;\n}\n\n/**\n * Get a list of plugins that were are supplied as string module IDs.\n * @example\n * ```json\n * {\n *   \"plugins\": [\n *     \"expo-camera\",\n *     [\"react-native-firebase\", ...]\n *   ]\n * }\n * ```\n *   ↓ ↓ ↓ ↓ ↓ ↓\n *\n * `['expo-camera', 'react-native-firebase']`\n *\n */\nexport function getNamedPlugins(plugins: NonNullable<ExpoConfig['plugins']>): string[] {\n  const namedPlugins: string[] = [];\n  for (const plugin of plugins) {\n    try {\n      // @ts-ignore\n      const [normal] = normalizeStaticPlugin(plugin);\n      if (typeof normal === 'string') {\n        namedPlugins.push(normal);\n      }\n    } catch {\n      // ignore assertions\n    }\n  }\n  return namedPlugins;\n}\n\n/** Attempts to ensure that non-auto plugins are added to the `app.json` `plugins` array when modules with Expo Config Plugins are installed. */\nexport async function autoAddConfigPluginsAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'plugins'>,\n  packages: string[]\n) {\n  debug('Checking config plugins...');\n\n  const currentPlugins = exp.plugins || [];\n  const normalized = getNamedPlugins(currentPlugins);\n\n  debug(`Existing plugins: ${normalized.join(', ')}`);\n\n  const plugins = packages.filter((pkg) => {\n    if (normalized.includes(pkg)) {\n      // already included in plugins array\n      return false;\n    }\n    // Check if the package has a valid plugin. Must be a well-made plugin for it to work with this.\n    const plugin = packageHasConfigPlugin(projectRoot, pkg);\n\n    debug(`Package \"${pkg}\" has plugin: ${!!plugin}` + (plugin ? ` (args: ${plugin.length})` : ''));\n\n    if (AUTO_PLUGINS.includes(pkg)) {\n      debug(`Package \"${pkg}\" is an auto plugin, skipping...`);\n      return false;\n    }\n\n    return !!plugin;\n  });\n\n  await attemptAddingPluginsAsync(projectRoot, plugins);\n}\n"], "names": ["autoAddConfigPluginsAsync", "getNamed<PERSON><PERSON><PERSON>", "debug", "require", "AUTO_PLUGINS", "getAutoPlugins", "packageHasConfigPlugin", "projectRoot", "packageName", "info", "resolveConfigPluginFunctionWithInfo", "isPluginFile", "plugin", "plugins", "<PERSON><PERSON><PERSON><PERSON>", "normal", "normalizeStaticPlugin", "push", "exp", "packages", "currentPlugins", "normalized", "join", "filter", "pkg", "includes", "length", "attemptAddingPluginsAsync"], "mappings": ";;;;;;;;;;;IAiEsBA,yBAAyB;eAAzBA;;IAjBNC,eAAe;eAAfA;;;;yBA5CT;;;;;;;yBACwB;;;;;;qCAEW;AAE1C,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,eAAeC,IAAAA,gCAAc;AAEnC;;;;;;;;CAQC,GACD,SAASC,uBAAuBC,WAAmB,EAAEC,WAAmB;IACtE,IAAI;QACF,MAAMC,OAAOC,IAAAA,qDAAmC,EAACH,aAAaC;QAC9D,IAAIC,KAAKE,YAAY,EAAE;YACrB,OAAOF,KAAKG,MAAM;QACpB;IACF,EAAE,OAAM,CAAC;IACT,OAAO;AACT;AAkBO,SAASX,gBAAgBY,OAA2C;IACzE,MAAMC,eAAyB,EAAE;IACjC,KAAK,MAAMF,UAAUC,QAAS;QAC5B,IAAI;YACF,aAAa;YACb,MAAM,CAACE,OAAO,GAAGC,IAAAA,uCAAqB,EAACJ;YACvC,IAAI,OAAOG,WAAW,UAAU;gBAC9BD,aAAaG,IAAI,CAACF;YACpB;QACF,EAAE,OAAM;QACN,oBAAoB;QACtB;IACF;IACA,OAAOD;AACT;AAGO,eAAed,0BACpBO,WAAmB,EACnBW,GAAgC,EAChCC,QAAkB;IAElBjB,MAAM;IAEN,MAAMkB,iBAAiBF,IAAIL,OAAO,IAAI,EAAE;IACxC,MAAMQ,aAAapB,gBAAgBmB;IAEnClB,MAAM,CAAC,kBAAkB,EAAEmB,WAAWC,IAAI,CAAC,OAAO;IAElD,MAAMT,UAAUM,SAASI,MAAM,CAAC,CAACC;QAC/B,IAAIH,WAAWI,QAAQ,CAACD,MAAM;YAC5B,oCAAoC;YACpC,OAAO;QACT;QACA,gGAAgG;QAChG,MAAMZ,SAASN,uBAAuBC,aAAaiB;QAEnDtB,MAAM,CAAC,SAAS,EAAEsB,IAAI,cAAc,EAAE,CAAC,CAACZ,QAAQ,GAAIA,CAAAA,SAAS,CAAC,QAAQ,EAAEA,OAAOc,MAAM,CAAC,CAAC,CAAC,GAAG,EAAC;QAE5F,IAAItB,aAAaqB,QAAQ,CAACD,MAAM;YAC9BtB,MAAM,CAAC,SAAS,EAAEsB,IAAI,gCAAgC,CAAC;YACvD,OAAO;QACT;QAEA,OAAO,CAAC,CAACZ;IACX;IAEA,MAAMe,IAAAA,8CAAyB,EAACpB,aAAaM;AAC/C"}