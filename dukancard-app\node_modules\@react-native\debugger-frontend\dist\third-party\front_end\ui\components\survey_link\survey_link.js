import*as e from"../../../core/common/common.js";import*as t from"../../../core/i18n/i18n.js";import*as i from"../../lit-html/lit-html.js";import*as n from"../icon_button/icon_button.js";const r=new CSSStyleSheet;r.replaceSync(".link-icon{vertical-align:sub;margin-right:0.5ch}.link{padding:var(--issue-link-padding,4px 0 0 0);text-decoration:var(--issue-link-text-decoration,underline);cursor:pointer;font-size:var(--issue-link-font-size,14px);color:var(--sys-color-primary);outline-offset:2px;border:none;background:none;font-family:inherit}.link:focus:not(:focus-visible){outline:none}.pending-link{opacity:75%;pointer-events:none;cursor:default;text-decoration:none}.disabled-link{pointer-events:none;cursor:default;text-decoration:none}\n/*# sourceURL=surveyLink.css */\n");const o={openingSurvey:"Opening survey …",thankYouForYourFeedback:"Thank you for your feedback",anErrorOccurredWithTheSurvey:"An error occurred with the survey"},s=t.i18n.registerUIStrings("ui/components/survey_link/SurveyLink.ts",o),a=t.i18n.getLocalizedString.bind(void 0,s);class h extends HTMLElement{static litTagName=i.literal`devtools-survey-link`;#e=this.attachShadow({mode:"open"});#t="";#i=e.UIString.LocalizedEmptyString;#n=()=>{};#r=()=>{};#o="Checking";connectedCallback(){this.#e.adoptedStyleSheets=[r]}set data(e){this.#t=e.trigger,this.#i=e.promptText,this.#n=e.canShowSurvey,this.#r=e.showSurvey,this.#s()}#s(){this.#o="Checking",this.#n(this.#t,(({canShowSurvey:e})=>{this.#o=e?"ShowLink":"DontShowLink",this.#a()}))}#h(){this.#o="Sending",this.#a(),this.#r(this.#t,(({surveyShown:e})=>{this.#o=e?"SurveyShown":"Failed",this.#a()}))}#a(){if("Checking"===this.#o||"DontShowLink"===this.#o)return;let e=this.#i;"Sending"===this.#o?e=a(o.openingSurvey):"SurveyShown"===this.#o?e=a(o.thankYouForYourFeedback):"Failed"===this.#o&&(e=a(o.anErrorOccurredWithTheSurvey));let t="";"Sending"===this.#o?t="pending-link":"Failed"!==this.#o&&"SurveyShown"!==this.#o||(t="disabled-link");const r="ShowLink"!==this.#o,s=i.html`
      <button class="link ${t}" tabindex=${r?"-1":"0"} .disabled=${r} aria-disabled=${r} @click=${this.#h}>
        <${n.Icon.Icon.litTagName} class="link-icon" .data=${{iconName:"review",color:"var(--sys-color-primary)",width:"var(--issue-link-icon-size, 16px)",height:"var(--issue-link-icon-size, 16px)"}}></${n.Icon.Icon.litTagName}><!--
      -->${e}
      </button>
    `;i.render(s,this.#e,{host:this})}}customElements.define("devtools-survey-link",h);var c=Object.freeze({__proto__:null,SurveyLink:h});export{c as SurveyLink};
